-- <PERSON><PERSON><PERSON> da tabela principal de logs de auditoria
CREATE TABLE IF NOT EXISTS `audit_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `user_id` INT,
    `username` VA<PERSON><PERSON><PERSON>(255),
    `action` VARCHAR(50),
    `entity_type` VARCHAR(50),
    `entity_id` VARCHAR(255),
    `old_value` JSO<PERSON>,
    `new_value` JSON,
    `ip_address` VARCHAR(45),
    `user_agent` VA<PERSON>HA<PERSON>(255),
    INDEX `idx_timestamp` (`timestamp`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_entity_type` (`entity_type`),
    INDEX `idx_entity_id` (`entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON><PERSON><PERSON> para armazenar logs específicos de CPFs
CREATE TABLE IF NOT EXISTS `cpf_audit_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `audit_log_id` BIGINT,
    `cpf` VARCHAR(11),
    `status_anterior` VARCHAR(50),
    `status_novo` VARCHAR(50),
    `detalhes_alteracao` JSON,
    `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`audit_log_id`) REFERENCES `audit_logs` (`id`),
    INDEX `idx_cpf` (`cpf`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela para armazenar logs específicos de RPAs
CREATE TABLE IF NOT EXISTS `rpa_audit_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `audit_log_id` BIGINT,
    `rpa_id` VARCHAR(50),
    `status_anterior` VARCHAR(50),
    `status_novo` VARCHAR(50),
    `configuracoes_alteradas` JSON,
    `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`audit_log_id`) REFERENCES `audit_logs` (`id`),
    INDEX `idx_rpa_id` (`rpa_id`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela para armazenar logs de acesso dos usuários
CREATE TABLE IF NOT EXISTS `user_access_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `audit_log_id` BIGINT,
    `user_id` INT,
    `tipo_acesso` VARCHAR(50),
    `sucesso` BOOLEAN,
    `detalhes` JSON,
    `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`audit_log_id`) REFERENCES `audit_logs` (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 