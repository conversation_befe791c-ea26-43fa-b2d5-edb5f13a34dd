import sys
import os

# Adiciona o diretório raiz do projeto ao PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)

# Define o caminho do diretório atual do script
current_dir = os.path.dirname(os.path.abspath(__file__))

import pandas as pd
import mysql.connector
import logging
from utils.db import get_mysql_connection
from datetime import datetime

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def formatar_cpf(cpf):
    """Formata o CPF para garantir 11 dígitos com zeros à esquerda"""
    if pd.isna(cpf):
        return None
    
    # Remove caracteres não numéricos
    cpf = ''.join(filter(str.isdigit, str(cpf)))
    
    # Garante que tenha 11 dígitos com zeros à esquerda
    return cpf.zfill(11)

def atualizar_status_crl():
    try:
        # Conectar ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Ler a planilha CRL
        planilha_path = os.path.join(current_dir, 'CRL.xlsx')
        logger.info(f"Lendo planilha {planilha_path}...")
        
        # Lê a planilha especificando o tipo das colunas como string
        df = pd.read_excel(planilha_path, dtype=str)
        
        # Verificar nomes das colunas
        logger.info("Colunas encontradas na planilha: " + ", ".join(df.columns))
        
        # Inicializar contadores
        total_cpfs_crl2 = 0
        total_cpfs_crl3 = 0
        cpfs_atualizados_crl2 = 0
        cpfs_atualizados_crl3 = 0
        cpfs_nao_encontrados = 0
        
        # Processar CPFs da CRL 2
        if 'CRL 2' in df.columns:
            # Remove duplicatas e valores nulos
            cpfs_crl2 = df['CRL 2'].dropna().unique()
            cpfs_crl2 = [formatar_cpf(cpf) for cpf in cpfs_crl2 if formatar_cpf(cpf) is not None]
            total_cpfs_crl2 = len(cpfs_crl2)
            
            logger.info(f"\nProcessando {total_cpfs_crl2} CPFs da CRL 2...")
            for cpf in cpfs_crl2:
                cursor.execute("SELECT cpf FROM associados WHERE cpf = %s", (cpf,))
                if cursor.fetchone():
                    cursor.execute("UPDATE associados SET status_crl = 2 WHERE cpf = %s", (cpf,))
                    cpfs_atualizados_crl2 += 1
                    logger.info(f"CPF {cpf} atualizado para status_crl = 2")
                else:
                    cpfs_nao_encontrados += 1
                    logger.info(f"CPF {cpf} não encontrado na tabela associados")
        
        # Processar CPFs da CRL 3
        if 'CRL 3' in df.columns:
            # Remove duplicatas e valores nulos
            cpfs_crl3 = df['CRL 3'].dropna().unique()
            cpfs_crl3 = [formatar_cpf(cpf) for cpf in cpfs_crl3 if formatar_cpf(cpf) is not None]
            total_cpfs_crl3 = len(cpfs_crl3)
            
            logger.info(f"\nProcessando {total_cpfs_crl3} CPFs da CRL 3...")
            for cpf in cpfs_crl3:
                cursor.execute("SELECT cpf FROM associados WHERE cpf = %s", (cpf,))
                if cursor.fetchone():
                    cursor.execute("UPDATE associados SET status_crl = 3 WHERE cpf = %s", (cpf,))
                    cpfs_atualizados_crl3 += 1
                    logger.info(f"CPF {cpf} atualizado para status_crl = 3")
                else:
                    cpfs_nao_encontrados += 1
                    logger.info(f"CPF {cpf} não encontrado na tabela associados")
        
        # Commit das alterações
        conn.commit()
        
        # Log do resumo final
        logger.info("\nResumo do processamento:")
        logger.info(f"Total de CPFs na CRL 2: {total_cpfs_crl2}")
        logger.info(f"Total de CPFs na CRL 3: {total_cpfs_crl3}")
        logger.info(f"CPFs atualizados para status_crl = 2: {cpfs_atualizados_crl2}")
        logger.info(f"CPFs atualizados para status_crl = 3: {cpfs_atualizados_crl3}")
        logger.info(f"CPFs não encontrados: {cpfs_nao_encontrados}")
        
    except Exception as e:
        logger.error(f"Erro ao processar planilha: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    atualizar_status_crl() 