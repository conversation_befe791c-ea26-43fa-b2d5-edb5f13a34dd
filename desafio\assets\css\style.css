:root {
    /* Cores Principais do Sicoob */
    --sicoob-turquoise: #00AE9D;  /* Turquesa */
    --sicoob-dark-green: #003641; /* Verde escuro */
    --sicoob-light-green: #C9D200;/* Verde claro */
    --sicoob-medium-green: #7DB61C;/* Verde médio */
    --sicoob-purple: #49479D;     /* Roxo */
    
    /* Cores Neutras */
    --sicoob-dark: #1D1D1B;       /* Cinza Escuro */
    --sicoob-light: #F5F5F5;      /* Cinza Claro */
    --sicoob-gray: #58595B;       /* Cinza Médio */
    
    /* Cores de Estado */
    --sicoob-error: #DC3545;      /* Vermelho para erros */
    --sicoob-success: #28A745;    /* Verde para sucesso */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Source Sans Pro', sans-serif;
    line-height: 1.6;
    color: var(--sicoob-dark);
    background-color: var(--sicoob-light);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

header .logo {
    height: 50px;
    width: auto;
    display: block;
}

header nav {
    float: right;
}

header nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

header nav a {
    color: var(--sicoob-dark);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

header nav a:hover {
    color: var(--sicoob-turquoise);
}

/* Login Page */
.login-page {
    background-color: var(--sicoob-light);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

.login-box .logo {
    height: 60px;
    width: auto;
    margin-bottom: 1.5rem;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--sicoob-dark);
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--sicoob-turquoise);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: var(--sicoob-dark-green);
}

.error-message {
    background-color: var(--sicoob-error);
    color: white;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
}

/* Content Area */
main {
    padding: 2rem 0;
}

h1 {
    color: var(--sicoob-turquoise);
    margin-bottom: 1.5rem;
}

.content {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: var(--sicoob-light);
    font-weight: 600;
}

tr:hover {
    background-color: #f8f8f8;
}

/* Footer */
footer {
    background-color: var(--sicoob-dark);
    color: white;
    padding: 1rem 0;
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    header nav {
        float: none;
        margin-top: 1rem;
    }

    header nav ul {
        flex-direction: column;
        gap: 1rem;
    }

    .container {
        padding: 0 10px;
    }

    .content {
        padding: 1rem;
    }
} 