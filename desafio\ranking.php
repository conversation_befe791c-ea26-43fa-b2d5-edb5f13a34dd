<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Obtém o PA do usuário
$pa = get_user_pa();

// Query para ranking por usuário
$query_usuarios = "SELECT 
    u.username,
    u.pa,
    COUNT(*) as total_vendas,
    SUM(v.valor) as valor_total,
    GROUP_CONCAT(DISTINCT v.produto) as produtos
FROM campanha_users u
INNER JOIN campanha_vendas v ON u.username = v.usuario";

// Query para ranking por PA
$query_pas = "SELECT 
    v.pa,
    COUNT(*) as total_vendas,
    SUM(v.valor) as valor_total,
    GROUP_CONCAT(DISTINCT v.produto) as produtos
FROM campanha_vendas v";

// Se não for admin, filtra por PA
if ($pa !== '00') {
    $query_usuarios .= " WHERE u.pa = ?";
    $query_pas .= " WHERE v.pa = ?";
}

$query_usuarios .= " GROUP BY u.username, u.pa ORDER BY valor_total DESC";
$query_pas .= " GROUP BY v.pa ORDER BY valor_total DESC";

// Executa as queries
$stmt_usuarios = $pdo->prepare($query_usuarios);
$stmt_pas = $pdo->prepare($query_pas);

if ($pa !== '00') {
    $stmt_usuarios->execute([$pa]);
    $stmt_pas->execute([$pa]);
} else {
    $stmt_usuarios->execute();
    $stmt_pas->execute();
}

$ranking_usuarios = $stmt_usuarios->fetchAll();
$ranking_pas = $stmt_pas->fetchAll();

// Lista de produtos para exibição
$produtos = [
    'CREDITO_PESSOAL' => 'Crédito Pessoal',
    'CHEQUE_ESPECIAL' => 'Cheque Especial',
    'CARTAO_CREDITO' => 'Cartão de Crédito',
    'DEBITO_AUTOMATICO' => 'Débito Automático'
];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ranking - Campanha Sicoob</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <nav>
                <ul>
                    <li><a href="index.php">Listagem</a></li>
                    <li><a href="minhas_vendas.php">Minhas Vendas</a></li>
                    <li><a href="ranking.php">Ranking</a></li>
                    <?php if ($_SESSION['is_admin']): ?>
                    <li><a href="admin/users.php">Usuários</a></li>
                    <li><a href="admin/vendas.php">Vendas</a></li>
                    <?php endif; ?>
                    <li><a href="logout.php">Sair</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="page-header">
            <h1>Ranking da Campanha</h1>
            <a href="ranking_publico.php" target="_blank" class="btn btn-primary">
                <i class="fas fa-tv"></i>
                Versão para TV
            </a>
        </div>
        
        <div class="ranking-container">
            <!-- Ranking por Usuário -->
            <div class="ranking-section">
                <h2>Ranking por Usuário</h2>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>Usuário</th>
                                <th>PA</th>
                                <th>Total de Vendas</th>
                                <th>Valor Total</th>
                                <th>Produtos Vendidos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ranking_usuarios as $pos => $usuario): ?>
                                <tr>
                                    <td><?php echo $pos + 1; ?>º</td>
                                    <td><?php echo htmlspecialchars($usuario['username']); ?></td>
                                    <td><?php echo htmlspecialchars($usuario['pa']); ?></td>
                                    <td><?php echo $usuario['total_vendas']; ?></td>
                                    <td>R$ <?php echo number_format($usuario['valor_total'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php
                                        $produtos_vendidos = explode(',', $usuario['produtos']);
                                        $produtos_vendidos = array_unique($produtos_vendidos);
                                        foreach ($produtos_vendidos as $prod) {
                                            echo "<span class='product-badge'>";
                                            echo "<i class='fas " . get_product_icon($prod) . "'></i>";
                                            echo $produtos[$prod];
                                            echo "</span>";
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Ranking por PA -->
            <div class="ranking-section">
                <h2>Ranking por PA</h2>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>PA</th>
                                <th>Total de Vendas</th>
                                <th>Valor Total</th>
                                <th>Produtos Vendidos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ranking_pas as $pos => $pa_info): ?>
                                <tr>
                                    <td><?php echo $pos + 1; ?>º</td>
                                    <td><?php echo htmlspecialchars($pa_info['pa']); ?></td>
                                    <td><?php echo $pa_info['total_vendas']; ?></td>
                                    <td>R$ <?php echo number_format($pa_info['valor_total'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php
                                        $produtos_vendidos = explode(',', $pa_info['produtos']);
                                        $produtos_vendidos = array_unique($produtos_vendidos);
                                        foreach ($produtos_vendidos as $prod) {
                                            echo "<span class='product-badge'>";
                                            echo "<i class='fas " . get_product_icon($prod) . "'></i>";
                                            echo $produtos[$prod];
                                            echo "</span>";
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .page-header h1 {
        margin: 0;
    }

    .ranking-container {
        display: grid;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .ranking-section h2 {
        color: var(--sicoob-turquoise);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .ranking-section h2::before {
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
    }

    .ranking-section:first-child h2::before {
        content: "\f007";
    }

    .ranking-section:last-child h2::before {
        content: "\f1ad";
    }

    .content {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
    }

    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    th {
        background: var(--sicoob-gray-100);
        color: var(--sicoob-dark-green);
        font-weight: 600;
        text-align: left;
        padding: 1rem;
        border-bottom: 2px solid var(--sicoob-gray-200);
    }

    td {
        padding: 1rem;
        border-bottom: 1px solid var(--sicoob-gray-200);
    }

    tr:hover td {
        background: var(--sicoob-gray-100);
    }

    td:first-child {
        font-weight: 700;
        color: var(--sicoob-turquoise);
    }

    .product-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.375rem 0.75rem;
        background: var(--sicoob-gray-100);
        border-radius: var(--radius-sm);
        color: var(--sicoob-dark-green);
        font-size: 0.875rem;
        margin: 0.25rem;
    }

    .product-badge i {
        color: var(--sicoob-turquoise);
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }

        td, th {
            padding: 0.75rem;
        }

        .product-badge {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    }
    </style>
</body>
</html> 