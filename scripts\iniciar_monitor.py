import subprocess
import os
import sys
import time
import psutil

def verificar_monitor_rodando():
    """Verifica se o monitor já está rodando"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and 'python' in cmdline[0].lower() and 'rpa_status_server.py' in ' '.join(cmdline):
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    return False

def iniciar_monitor():
    """Inicia o servidor de monitoramento do RPA"""
    if verificar_monitor_rodando():
        print("Monitor já está rodando!")
        return
    
    try:
        # Inicia o servidor em background
        pythonw = os.path.join(os.path.dirname(sys.executable), 'pythonw.exe')
        subprocess.Popen([pythonw, 'rpa_status_server.py'], 
                        creationflags=subprocess.CREATE_NO_WINDOW)
        
        print("Monitor iniciado com sucesso!")
        print("Aguardando inicialização...")
        time.sleep(5)  # Aguarda 5 segundos para o servidor iniciar
        
        if verificar_monitor_rodando():
            print("Monitor está rodando corretamente!")
        else:
            print("AVISO: Monitor pode não ter iniciado corretamente.")
            
    except Exception as e:
        print(f"Erro ao iniciar monitor: {e}")

if __name__ == "__main__":
    iniciar_monitor() 