from flask import Blueprint, render_template, request, jsonify
from utils.audit_logger import audit_logger
from datetime import datetime, timedelta
from functools import wraps
from flask import session
from utils.db import get_mysql_connection

audit_bp = Blueprint('audit', __name__)

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('is_admin'):
            return jsonify({'error': 'Acesso não autorizado'}), 403
        return f(*args, **kwargs)
    return decorated_function

@audit_bp.route('/audit/logs')
@admin_required
def view_logs():
    """Página principal de visualização de logs"""
    return render_template('audit_logs.html')

@audit_bp.route('/audit/api/logs')
@admin_required
def get_logs():
    """API para buscar logs com filtros"""
    try:
        # Parâmetros de filtro
        entity_type = request.args.get('entity_type')
        entity_id = request.args.get('entity_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Converter strings de data para objetos datetime
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        
        # Buscar logs
        logs = audit_logger.get_entity_history(
            entity_type=entity_type,
            entity_id=entity_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            'success': True,
            'data': logs
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@audit_bp.route('/audit/api/stats')
@admin_required
def get_stats():
    """API para estatísticas de auditoria"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Total de logs por tipo de entidade
        cursor.execute("""
            SELECT entity_type, COUNT(*) as total
            FROM audit_logs
            GROUP BY entity_type
        """)
        entity_stats = cursor.fetchall()
        
        # Total de logs por tipo de ação
        cursor.execute("""
            SELECT action, COUNT(*) as total
            FROM audit_logs
            GROUP BY action
        """)
        action_stats = cursor.fetchall()
        
        # Logs por período (últimos 7 dias)
        cursor.execute("""
            SELECT DATE(timestamp) as date, COUNT(*) as total
            FROM audit_logs
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(timestamp)
            ORDER BY date
        """)
        daily_stats = cursor.fetchall()
        
        return jsonify({
            'success': True,
            'data': {
                'entity_stats': entity_stats,
                'action_stats': action_stats,
                'daily_stats': daily_stats
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close() 