import os
import logging
from utils.db import get_mysql_connection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_display_name():
    """
    Adiciona a coluna display_name na tabela rpa_tracking e 
    atualiza os nomes de exibição dos RPAs existentes
    """
    try:
        # Lê o arquivo SQL
        migration_file = os.path.join('config', 'db_migrations', 'add_display_name.sql')
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_commands = f.read()

        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Executa os comandos SQL
        for command in sql_commands.split(';'):
            if command.strip():
                cursor.execute(command)
                
        conn.commit()
        
        # Verifica se a atualização foi feita
        cursor.execute("""
            SELECT rpa_name, display_name 
            FROM rpa_tracking 
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        
        logger.info("\nAtualização concluída! Registros atuais:")
        logger.info("-" * 50)
        
        for rpa_name, display_name in results:
            logger.info(f"RPA: {rpa_name}")
            logger.info(f"Nome de exibição: {display_name or 'Não definido'}")
            logger.info("-" * 30)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Erro ao adicionar coluna display_name: {e}")

if __name__ == '__main__':
    add_display_name() 