{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Upload de Planilhas</h1>
    <div class="row mt-4">
        <!-- Card para Upload de Associados -->
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-people-fill me-1"></i>
                    Upload de Associados
                </div>
                <div class="card-body">
                    <form id="uploadAssociadosForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="associadosFile" class="form-label">Selecione a planilha de Associados</label>
                            <input class="form-control" type="file" id="associadosFile" accept=".xlsx,.xls">
                        </div>
                        <div class="progress mb-3 d-none" id="progressAssociados">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="uploadAssociadosBtn">
                            <i class="bi bi-upload"></i> Upload
                        </button>
                        <button type="button" class="btn btn-success d-none" id="executarAssociadosBtn">
                            <i class="bi bi-play-fill"></i> Executar
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Card para Upload de Crédito -->
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-credit-card-fill me-1"></i>
                    Upload de Crédito
                </div>
                <div class="card-body">
                    <form id="uploadCreditoForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="creditoFile" class="form-label">Selecione a planilha de Crédito</label>
                            <input class="form-control" type="file" id="creditoFile" accept=".xlsx,.xls">
                        </div>
                        <div class="progress mb-3 d-none" id="progressCredito">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="uploadCreditoBtn">
                            <i class="bi bi-upload"></i> Upload
                        </button>
                        <button type="button" class="btn btn-success d-none" id="executarCreditoBtn">
                            <i class="bi bi-play-fill"></i> Executar
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Card para Upload de Score -->
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-graph-up-arrow me-1"></i>
                    Upload de Score
                </div>
                <div class="card-body">
                    <form id="uploadScoreForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="scoreFile" class="form-label">Selecione a planilha de Score</label>
                            <input class="form-control" type="file" id="scoreFile" accept=".xlsx,.xls">
                        </div>
                        <div class="progress mb-3 d-none" id="progressScore">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="uploadScoreBtn">
                            <i class="bi bi-upload"></i> Upload
                        </button>
                        <button type="button" class="btn btn-success d-none" id="executarScoreBtn">
                            <i class="bi bi-play-fill"></i> Executar
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    function formatCPF(cpf) {
        // Remove caracteres não numéricos
        cpf = cpf.replace(/\D/g, '');
        // Completa com zeros à esquerda se necessário
        return cpf.padStart(11, '0');
    }

    function handleFileUpload(formId, progressId, executarBtnId, uploadUrl, executeUrl) {
        const form = document.getElementById(formId);
        const progress = document.getElementById(progressId);
        const executarBtn = document.getElementById(executarBtnId);

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            progress.classList.remove('d-none');
            
            try {
                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    progress.querySelector('.progress-bar').style.width = '100%';
                    executarBtn.classList.remove('d-none');
                    flash('success', 'Upload realizado com sucesso!');
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Erro no upload');
                }
            } catch (error) {
                flash('danger', 'Erro ao realizar upload: ' + error.message);
            }
        });

        executarBtn.addEventListener('click', async () => {
            try {
                const response = await fetch(executeUrl, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Erro no processamento');
                }

                const result = await response.json();

                // Se for processamento de Score, mostra estatísticas
                if (formId === 'uploadScoreForm' && result.estatisticas) {
                    const stats = result.estatisticas;
                    flash('success', `
                        Processamento concluído com sucesso!<br>
                        <small>
                        • Total de linhas na planilha: ${stats.total_linhas_planilha}<br>
                        • CPFs únicos na planilha: ${stats.cpfs_unicos_planilha}<br>
                        • Scores atualizados: ${stats.scores_atualizados}<br>
                        • Total de associados com score: ${stats.total_associados_com_score}
                        </small>
                    `);
                } 
                // Se for processamento de Associados, mostra estatísticas
                else if (formId === 'uploadAssociadosForm' && result.estatisticas) {
                    const stats = result.estatisticas;
                    flash('success', `
                        Processamento concluído com sucesso!<br>
                        <small>
                        • Total de linhas na planilha: ${stats.total_linhas_planilha}<br>
                        • CPFs únicos na planilha: ${stats.cpfs_unicos_planilha}<br>
                        • CPFs antes da atualização: ${stats.cpfs_antes}<br>
                        • CPFs após atualização: ${stats.cpfs_depois}<br>
                        • Registros atualizados na tabela associados: ${stats.registros_atualizados_associados}
                        </small>
                    `);
                }
                else {
                    flash('success', result.message);
                }

                // Reset form and progress
                form.reset();
                progress.classList.add('d-none');
                progress.querySelector('.progress-bar').style.width = '0%';
                executarBtn.classList.add('d-none');
            } catch (error) {
                flash('danger', 'Erro ao processar: ' + error.message);
            }
        });
    }

    // Inicializa os handlers para os formulários
    handleFileUpload(
        'uploadAssociadosForm',
        'progressAssociados',
        'executarAssociadosBtn',
        '/upload/associados',
        '/executar/associados'
    );

    handleFileUpload(
        'uploadCreditoForm',
        'progressCredito',
        'executarCreditoBtn',
        '/upload/credito',
        '/executar/credito'
    );

    handleFileUpload(
        'uploadScoreForm',
        'progressScore',
        'executarScoreBtn',
        '/upload/score',
        '/executar/score'
    );
</script>
{% endblock %}
