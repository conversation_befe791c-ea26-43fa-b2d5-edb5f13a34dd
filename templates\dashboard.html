{% extends "base.html" %}

{% block title %}Dashboard{% endblock %}

{% block content %}
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="page-icon-modern bg-sicoob-azul me-3">
                            <i class="bi bi-speedometer2"></i>
                        </div>
                        <div>
                            <h1 class="h3 mb-0 text-sicoob-verde-escuro">Dashboard RPA Monitor</h1>
                            <p class="text-muted mb-0">Visão geral dos seus robôs de automação</p>
                        </div>
                    </div>
                    <a href="{{ url_for('rpa_monitor') }}" class="btn-modern btn-sicoob-primary">
                        <i class="bi bi-robot me-2"></i>
                        <span>Gerenciar RPAs</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Cards de resumo modernos -->
            <div class="row mb-4">
                <div class="col-12 col-md-6 col-lg-3 mb-4">
                    <div class="card-modern card-total">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle">Total de RPAs</h6>
                                    <h3 class="card-number text-sicoob-azul" id="totalRpas">-</h3>
                                </div>
                                <div class="card-icon bg-sicoob-azul">
                                    <i class="bi bi-robot"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 col-md-6 col-lg-3 mb-4">
                    <div class="card-modern card-running">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle">Em Execução</h6>
                                    <h3 class="card-number text-sicoob-verde-escuro" id="runningRpas">-</h3>
                                </div>
                                <div class="card-icon bg-sicoob-verde-escuro">
                                    <i class="bi bi-play-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 col-md-6 col-lg-3 mb-4">
                    <div class="card-modern card-stopped">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle">Parados</h6>
                                    <h3 class="card-number text-danger" id="stoppedRpas">-</h3>
                                </div>
                                <div class="card-icon bg-danger">
                                    <i class="bi bi-stop-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 col-md-6 col-lg-3 mb-4">
                    <div class="card-modern card-error">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle">Com Erro</h6>
                                    <h3 class="card-number text-sicoob-roxo" id="errorRpas">-</h3>
                                </div>
                                <div class="card-icon bg-sicoob-roxo">
                                    <i class="bi bi-exclamation-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lista de RPAs Recentes -->
            <div class="card-modern table-card">
                <div class="card-header-modern">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="table-icon me-3">
                                <i class="bi bi-list-ul"></i>
                            </div>
                            <div>
                                <h5 class="mb-0 text-sicoob-verde-escuro">RPAs Recentes</h5>
                                <p class="text-muted mb-0 small">Status em tempo real dos seus robôs</p>
                            </div>
                        </div>
                        <a href="{{ url_for('rpa_monitor') }}" class="btn-modern btn-sicoob-secondary">
                            <i class="bi bi-arrow-right me-2"></i>
                            <span>Ver Todos</span>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-modern">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Processo</th>
                                    <th>Status</th>
                                    <th>Uptime</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="recentRpasTable">
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="loading-state">
                                            <div class="spinner-border text-sicoob-azul" role="status">
                                                <span class="visually-hidden">Carregando...</span>
                                            </div>
                                            <p class="text-muted mt-2 mb-0">Carregando dados...</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    /* Cores da Identidade Visual */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
        --sicoob-verde-claro-rgb: 201, 210, 0;
        --sicoob-verde-escuro-rgb: 125, 182, 28;
    }

    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* Header Moderno */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(var(--sicoob-verde-claro-rgb), 0.05) 0%, rgba(var(--sicoob-verde-escuro-rgb), 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(var(--sicoob-verde-claro-rgb), 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    /* Botão Moderno */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    .btn-sicoob-secondary {
        background: linear-gradient(135deg, var(--sicoob-cinza) 0%, #7a8599 100%);
        color: white;
    }

    .btn-sicoob-secondary:hover {
        color: white;
    }

    /* Botão Gerenciar com cores Sicoob */
    .btn-sicoob-manage {
        background: rgba(0, 174, 157, 0.1);
        border: 1px solid rgba(0, 174, 157, 0.3);
        color: var(--sicoob-azul);
        border-radius: 8px;
        padding: 6px 12px;
        font-weight: 600;
        font-size: 13px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-sicoob-manage:hover {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    .btn-sicoob-manage:focus {
        box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.2);
        outline: none;
    }

    /* Cards Modernos */
    .card-modern {
        border: none;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        background: white;
        position: relative;
    }

    .card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .card-total::before {
        background: linear-gradient(90deg, var(--sicoob-azul) 0%, #008a7a 100%);
    }

    .card-running::before {
        background: linear-gradient(90deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
    }

    .card-stopped::before {
        background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
    }

    .card-error::before {
        background: linear-gradient(90deg, var(--sicoob-roxo) 0%, #3a3785 100%);
    }

    .card-modern:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    .card-modern .card-body {
        padding: 24px;
    }

    .card-subtitle {
        color: var(--sicoob-cinza);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .card-number {
        font-size: 32px;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0;
    }

    .card-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Tabela Moderna */
    .table-card {
        margin-top: 32px;
    }

    .card-header-modern {
        background: rgba(248, 249, 250, 0.8);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 20px 24px;
    }

    .table-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--sicoob-verde-claro) 0%, var(--sicoob-verde-escuro) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        box-shadow: 0 4px 12px rgba(125, 182, 28, 0.3);
    }

    .table-modern {
        margin-bottom: 0;
    }

    .table-modern thead th {
        border-bottom: 2px solid rgba(var(--sicoob-verde-claro-rgb), 0.2);
        background: rgba(var(--sicoob-verde-claro-rgb), 0.05);
        color: var(--sicoob-verde-escuro);
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 16px;
    }

    .table-modern tbody td {
        padding: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.02);
    }

    /* Status Indicators Modernos */
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 10px;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
        position: relative;
    }

    .status-indicator::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        border: 2px solid transparent;
        animation: pulse 2s infinite;
    }

    .status-running {
        background-color: var(--sicoob-verde-escuro);
    }
    .status-running::after {
        border-color: var(--sicoob-verde-escuro);
    }

    .status-stopped {
        background-color: #dc3545;
    }
    .status-stopped::after {
        border-color: #dc3545;
    }

    .status-error {
        background-color: var(--sicoob-roxo);
    }
    .status-error::after {
        border-color: var(--sicoob-roxo);
    }

    .status-unknown {
        background-color: var(--sicoob-cinza);
    }
    .status-unknown::after {
        border-color: var(--sicoob-cinza);
    }

    @keyframes pulse {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.5;
            transform: scale(1.2);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Loading State */
    .loading-state {
        padding: 40px 20px;
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .btn-modern {
            padding: 10px 20px;
            font-size: 13px;
        }

        .card-modern .card-body {
            padding: 20px;
        }

        .card-number {
            font-size: 28px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function formatUptime(seconds) {
        if (!seconds) return '-';
        
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        if (days > 0) {
            return `${days}d${hours}h${minutes}m`;
        }
        if (hours > 0) {
            return `${hours}h${minutes}m${remainingSeconds}s`;
        }
        if (minutes > 0) {
            return `${minutes}m${remainingSeconds}s`;
        }
        return `${remainingSeconds}s`;
    }

    function getStatusClass(status) {
        switch(status) {
            case 'running': return 'status-running';
            case 'stopped': return 'status-stopped';
            case 'error': return 'status-error';
            default: return 'status-error';
        }
    }

    function getStatusText(status) {
        switch(status) {
            case 'running': return 'Em execução';
            case 'stopped': return 'Parado';
            case 'error': return 'Erro';
            default: return 'Desconhecido';
        }
    }

    function updateDashboard() {
        Promise.all([
            fetch('/api/rpas').then(response => response.json()),
            fetch('/api/status').then(response => response.json())
        ])
        .then(([rpasData, statusData]) => {
            // Contadores
            let total = 0;
            let running = 0;
            let stopped = 0;
            let error = 0;

            // Atualiza a tabela de RPAs recentes
            const tableBody = document.getElementById('recentRpasTable');
            tableBody.innerHTML = '';

            for (const rpa of rpasData) {
                total++;
                const status = statusData[rpa.id] || { status: 'unknown', uptime: 0, error: 'Status desconhecido' };
                
                // Atualiza contadores
                switch(status.status) {
                    case 'running': running++; break;
                    case 'stopped': stopped++; break;
                    case 'error': error++; break;
                }

                // Adiciona linha na tabela
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="status-indicator ${getStatusClass(status.status)}"></span>
                        ${rpa.name}
                    </td>
                    <td>${rpa.process_name}</td>
                    <td>${getStatusText(status.status)}</td>
                    <td>${formatUptime(status.uptime || 0)}</td>
                    <td>
                        <a href="{{ url_for('rpa_monitor') }}" class="btn btn-sm btn-sicoob-manage">
                            <i class="bi bi-gear me-1"></i>
                            Gerenciar
                        </a>
                    </td>
                `;
                tableBody.appendChild(row);
            }

            if (total === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">
                            Nenhum RPA cadastrado. 
                            <a href="{{ url_for('rpa_monitor') }}">Adicione um RPA</a> para começar.
                        </td>
                    </tr>
                `;
            }

            // Atualiza os cards
            document.getElementById('totalRpas').textContent = total;
            document.getElementById('runningRpas').textContent = running;
            document.getElementById('stoppedRpas').textContent = stopped;
            document.getElementById('errorRpas').textContent = error;
        })
        .catch(error => console.error('Error:', error));
    }

    // Atualiza o dashboard a cada 3 segundos
    setInterval(updateDashboard, 3000);
    
    // Primeira atualização
    updateDashboard();
</script>
{% endblock %}
