{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center mb-3">
                    <div class="page-icon-modern bg-sicoob-verde-claro me-3">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div>
                        <h1 class="h3 mb-0 text-sicoob-verde-escuro">CRLs Pendentes</h1>
                        <p class="text-muted mb-0"><PERSON><PERSON><PERSON><PERSON> as CRLs pendentes e removidas do sistema</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Controles Modernos -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="control-group-modern">
                <label class="control-label-modern">Itens por página:</label>
                <select class="form-select-modern" id="perPageSelect" onchange="changePerPage(this.value)">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                    <option value="500" {% if per_page == 500 %}selected{% endif %}>500</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stats-summary-modern">
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-sicoob-verde-claro">{{ total_pendentes }}</span>
                    <span class="stat-label-modern">CRLs Pendentes</span>
                </div>
                <div class="stat-divider-modern"></div>
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-danger">{{ total_removidos }}</span>
                    <span class="stat-label-modern">CRLs Removidas</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Abas Modernas -->
    <div class="tabs-container-modern">
        <ul class="nav nav-tabs-modern" id="crlTabs" role="tablist">
            <li class="nav-item-modern" role="presentation">
                <button class="nav-link-modern active" id="pendentes-tab" data-bs-toggle="tab" data-bs-target="#pendentes" type="button" role="tab">
                    <div class="tab-content-modern">
                        <i class="bi bi-clock-history tab-icon-modern"></i>
                        <span class="tab-text-modern">Pendentes</span>
                        <span class="badge-modern bg-sicoob-verde-claro">{{ total_pendentes }}</span>
                    </div>
                </button>
            </li>
            <li class="nav-item-modern" role="presentation">
                <button class="nav-link-modern" id="removidos-tab" data-bs-toggle="tab" data-bs-target="#removidos" type="button" role="tab">
                    <div class="tab-content-modern">
                        <i class="bi bi-trash tab-icon-modern"></i>
                        <span class="tab-text-modern">Removidos</span>
                        <span class="badge-modern bg-danger">{{ total_removidos }}</span>
                    </div>
                </button>
            </li>
        </ul>
    </div>

    <style>
    /* Cores da Identidade Visual */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
        --sicoob-verde-claro-rgb: 201, 210, 0;
        --sicoob-verde-escuro-rgb: 125, 182, 28;
    }

    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }

    /* Header Moderno */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(var(--sicoob-verde-claro-rgb), 0.05) 0%, rgba(var(--sicoob-verde-escuro-rgb), 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(var(--sicoob-verde-claro-rgb), 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(var(--sicoob-verde-claro-rgb), 0.3);
    }

    /* Controles Modernos */
    .control-group-modern {
        display: flex;
        align-items: center;
        gap: 12px;
        background: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .control-label-modern {
        font-weight: 600;
        color: #495057;
        margin: 0;
        white-space: nowrap;
    }

    .form-select-modern {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        min-width: 80px;
    }

    .form-select-modern:focus {
        border-color: var(--sicoob-verde-claro);
        box-shadow: 0 0 0 0.2rem rgba(var(--sicoob-verde-claro-rgb), 0.25);
        background-color: white;
    }

    /* Estatísticas Modernas */
    .stats-summary-modern {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
        background: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .stat-item-modern {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .stat-number-modern {
        font-size: 24px;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 4px;
    }

    .stat-label-modern {
        font-size: 12px;
        font-weight: 500;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-divider-modern {
        width: 1px;
        height: 40px;
        background: linear-gradient(to bottom, transparent, #e9ecef, transparent);
    }

    /* Abas Modernas */
    .tabs-container-modern {
        margin-bottom: 24px;
    }

    .nav-tabs-modern {
        border: none;
        display: flex;
        gap: 8px;
        background: #f8f9fa;
        padding: 6px;
        border-radius: 12px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    }

    .nav-item-modern {
        flex: 1;
    }

    .nav-link-modern {
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        background: transparent;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .nav-link-modern:hover {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.1);
        transform: translateY(-1px);
    }

    .nav-link-modern.active {
        background: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .tab-content-modern {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-weight: 600;
    }

    .tab-icon-modern {
        font-size: 16px;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .tab-text-modern {
        color: #495057;
        transition: color 0.3s ease;
    }

    .nav-link-modern.active .tab-icon-modern {
        color: var(--sicoob-verde-claro);
    }

    .nav-link-modern.active .tab-text-modern {
        color: #212529;
    }

    .badge-modern {
        font-size: 11px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
        color: white;
        min-width: 24px;
        text-align: center;
    }

    /* Tabelas Modernas */
    .table-responsive {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        padding: 16px 20px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
    }

    .table tbody td {
        padding: 16px 20px;
        border-top: 1px solid #f1f3f4;
        color: #495057;
        font-weight: 500;
        vertical-align: middle;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    /* Filtros Modernos */
    .column-filter {
        position: relative;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .filter-icon {
        cursor: pointer;
        color: #6c757d;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 6px;
        background: rgba(108, 117, 125, 0.1);
    }

    .filter-icon:hover {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.2);
        color: var(--sicoob-verde-escuro);
    }

    .filter-icon.has-filter {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.2);
        color: var(--sicoob-verde-escuro);
    }

    .clear-filter {
        cursor: pointer;
        display: none;
        color: #dc3545;
        font-size: 12px;
    }

    .filter-icon.has-filter .clear-filter {
        display: inline-block;
    }

    .filter-input {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        z-index: 1000;
        display: none;
        padding: 12px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        min-width: 220px;
    }

    .filter-input.show {
        display: block !important;
        animation: filterSlideIn 0.3s ease;
    }

    @keyframes filterSlideIn {
        from {
            opacity: 0;
            transform: translateY(-8px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .filter-input input {
        width: 100%;
        padding: 10px 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-input input:focus {
        border-color: var(--sicoob-verde-claro);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(var(--sicoob-verde-claro-rgb), 0.25);
        background-color: white;
    }

    .filter-input input::placeholder {
        color: #adb5bd;
        font-weight: 400;
    }

    /* Botões de Ação Modernos */
    .btn-action-modern {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-action-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-action-modern:hover::before {
        opacity: 1;
    }

    .btn-action-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-danger.btn-action-modern {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .btn-success.btn-action-modern {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .btn-action-modern:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .btn-action-modern:disabled:hover {
        transform: none;
        box-shadow: none;
    }

    /* Animação de carregamento */
    .btn-action-modern .bi-hourglass-split {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Paginação Moderna */
    .pagination {
        gap: 4px;
    }

    .page-link {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        margin: 0;
    }

    .page-link:hover {
        background: rgba(var(--sicoob-verde-claro-rgb), 0.1);
        border-color: var(--sicoob-verde-claro);
        color: var(--sicoob-verde-escuro);
        transform: translateY(-1px);
    }

    .page-item.active .page-link {
        background: var(--sicoob-verde-claro);
        border-color: var(--sicoob-verde-claro);
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(var(--sicoob-verde-claro-rgb), 0.3);
    }

    .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
    }

    /* Informações de Paginação */
    .pagination-info-modern {
        background: white;
        padding: 12px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        color: #6c757d;
        font-weight: 500;
        font-size: 14px;
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .stats-summary-modern {
            flex-direction: column;
            gap: 12px;
        }

        .stat-divider-modern {
            width: 100%;
            height: 1px;
        }

        .control-group-modern {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
        }
    }
    </style>

    <!-- Conteúdo das Abas -->
    <div class="tab-content mt-3" id="crlTabsContent">
        <!-- Aba Pendentes -->
        <div class="tab-pane fade show active" id="pendentes" role="tabpanel">
            <!-- Paginação Superior Pendentes -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_pendentes|length }} de {{ total_pendentes }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page-1, per_page=per_page) }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=p, per_page=per_page) }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages_pendentes %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page+1, per_page=per_page) }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="column-filter">
                                    CPF
                                    <div class="filter-icon" data-column="cpf">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar CPF..." data-column="cpf">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    PA
                                    <div class="filter-icon" data-column="pa">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar PA..." data-column="pa">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    Nome
                                    <div class="filter-icon" data-column="nome">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar Nome..." data-column="nome">
                                    </div>
                                </div>
                            </th>
                            <th>Data Aprovação</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary">
                        {% for crl in crls_pendentes %}
                        <tr>
                            <td>{{ crl['cpf'][:3] }}.{{ crl['cpf'][3:6] }}.{{ crl['cpf'][6:9] }}-{{ crl['cpf'][9:] }}</td>
                            <td>{{ crl['pa'] }}</td>
                            <td>{{ crl['nome'] if crl['nome'] else '-' }}</td>
                            <td>{{ crl['data_aprovacao'].strftime('%d/%m/%Y %H:%M:%S') if crl['data_aprovacao'] else '-' }}</td>
                            <td>
                                <button class="btn btn-danger btn-action-modern" onclick="removerCRL('{{ crl['cpf'] }}')" title="Remover CRL">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Inferior Pendentes -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_pendentes|length }} de {{ total_pendentes }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page-1, per_page=per_page) }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=p, per_page=per_page) }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages_pendentes %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page+1, per_page=per_page) }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Aba Removidos -->
        <div class="tab-pane fade" id="removidos" role="tabpanel">
            <!-- Paginação Superior Removidos -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_removidos|length }} de {{ total_removidos }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page-1, per_page=per_page, tab='removidos') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page_removidos + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=p, per_page=per_page, tab='removidos') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages_removidos %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page+1, per_page=per_page, tab='removidos') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="column-filter">
                                    CPF
                                    <div class="filter-icon" data-column="cpf">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar CPF..." data-column="cpf">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    PA
                                    <div class="filter-icon" data-column="pa">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar PA..." data-column="pa">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    Nome
                                    <div class="filter-icon" data-column="nome">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar Nome..." data-column="nome">
                                    </div>
                                </div>
                            </th>
                            <th>Data Aprovação</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary">
                        {% for crl in crls_removidos %}
                        <tr>
                            <td>{{ crl['cpf'][:3] }}.{{ crl['cpf'][3:6] }}.{{ crl['cpf'][6:9] }}-{{ crl['cpf'][9:] }}</td>
                            <td>{{ crl['pa'] }}</td>
                            <td>{{ crl['nome'] if crl['nome'] else '-' }}</td>
                            <td>{{ crl['data_aprovacao'].strftime('%d/%m/%Y %H:%M:%S') if crl['data_aprovacao'] else '-' }}</td>
                            <td>
                                <button class="btn btn-success btn-action-modern" onclick="restaurarCRL('{{ crl['cpf'] }}')" title="Restaurar CRL">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Inferior Removidos -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_removidos|length }} de {{ total_removidos }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page-1, per_page=per_page, tab='removidos') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page_removidos + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=p, per_page=per_page, tab='removidos') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages_removidos %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_pendentes', page=page+1, per_page=per_page, tab='removidos') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function removerCRL(cpf) {
    if (confirm('Tem certeza que deseja remover esta CRL?')) {
        // Mostrar indicador de carregamento
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        button.disabled = true;

        fetch(`/api/crls/${cpf}/remover`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                if (response.status === 401) {
                    alert('Sessão expirada. Você será redirecionado para o login.');
                    window.location.href = '/login';
                    return Promise.reject('Session expired');
                }
                if (response.status === 403) {
                    alert('Acesso negado. Você não tem permissão para esta ação.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                    return Promise.reject('Access denied');
                }
                if (response.status === 500) {
                    alert('Erro interno do servidor. Tente novamente em alguns instantes.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                    return Promise.reject('Server error');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Verificar se a resposta é JSON válido
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.error('Content-Type:', contentType);
                throw new Error('Resposta não é JSON válido');
            }

            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data && data.success) {
                // Mostrar feedback de sucesso
                button.innerHTML = '<i class="bi bi-check-lg"></i>';
                button.classList.remove('btn-danger');
                button.classList.add('btn-success');

                // Recarregar após delay para mostrar feedback
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                const errorMsg = data ? (data.error || 'Erro desconhecido') : 'Resposta inválida do servidor';
                alert('Erro ao remover CRL: ' + errorMsg);
                console.error('Error data:', data);

                // Restaurar botão
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Erro completo:', error);
            console.error('Error stack:', error.stack);

            // Verificar se é erro de rede
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                alert('Erro de conexão. Verifique sua internet e tente novamente.');
            } else {
                alert('Erro ao processar a requisição: ' + error.message);
            }

            // Restaurar botão
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

function restaurarCRL(cpf) {
    if (confirm('Tem certeza que deseja restaurar esta CRL?')) {
        // Mostrar indicador de carregamento
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        button.disabled = true;

        fetch(`/api/crls/${cpf}/restaurar`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                if (response.status === 401) {
                    alert('Sessão expirada. Você será redirecionado para o login.');
                    window.location.href = '/login';
                    return Promise.reject('Session expired');
                }
                if (response.status === 403) {
                    alert('Acesso negado. Você não tem permissão para esta ação.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                    return Promise.reject('Access denied');
                }
                if (response.status === 500) {
                    alert('Erro interno do servidor. Tente novamente em alguns instantes.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                    return Promise.reject('Server error');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Verificar se a resposta é JSON válido
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.error('Content-Type:', contentType);
                throw new Error('Resposta não é JSON válido');
            }

            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data && data.success) {
                // Mostrar feedback de sucesso
                button.innerHTML = '<i class="bi bi-check-lg"></i>';
                button.classList.remove('btn-success');
                button.classList.add('btn-primary');

                // Recarregar após delay para mostrar feedback
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                const errorMsg = data ? (data.error || 'Erro desconhecido') : 'Resposta inválida do servidor';
                alert('Erro ao restaurar CRL: ' + errorMsg);
                console.error('Error data:', data);

                // Restaurar botão
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Erro completo:', error);
            console.error('Error stack:', error.stack);

            // Verificar se é erro de rede
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                alert('Erro de conexão. Verifique sua internet e tente novamente.');
            } else {
                alert('Erro ao processar a requisição: ' + error.message);
            }

            // Restaurar botão
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

function changePerPage(value) {
    const url = new URL(window.location.href);
    url.searchParams.set('per_page', value);
    url.searchParams.set('page', 1);  // Volta para a primeira página
    window.location.href = url.toString();
}

// Mantém a aba selecionada após recarregar a página
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    if (tab === 'removidos') {
        const removidosTab = new bootstrap.Tab(document.getElementById('removidos-tab'));
        removidosTab.show();
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Gerenciamento dos filtros de coluna
    const filterIcons = document.querySelectorAll('.filter-icon');
    const filterInputs = document.querySelectorAll('.filter-input input');
    let activeFilters = new Map();
    let activeFilterInputs = new Set(); // Para controlar quais inputs estão abertos

    // Função para atualizar o estado visual do filtro
    function updateFilterState(column, value) {
        const activeTab = document.querySelector('.tab-pane.active');
        const tabId = activeTab.id;
        const filterIcon = activeTab.querySelector(`.filter-icon[data-column="${column}"]`);

        if (value && value.length > 0) {
            filterIcon.classList.add('has-filter');
            activeFilters.set(`${tabId}_${column}`, value);
        } else {
            filterIcon.classList.remove('has-filter');
            activeFilters.delete(`${tabId}_${column}`);
        }
    }

    // Função para realizar o filtro
    function applyFilter(column, value) {
        const activeTab = document.querySelector('.tab-pane.active');
        const tabId = activeTab.id;
        updateFilterState(column, value);

        const url = new URL(window.location.href);

        // Atualizar ou remover parâmetros de filtro
        if (value) {
            url.searchParams.set(`filter_${column}`, value);
        } else {
            url.searchParams.delete(`filter_${column}`);
        }

        // Adicionar o parâmetro da aba atual
        if (tabId === 'removidos') {
            url.searchParams.set('tab', 'removidos');
        } else {
            url.searchParams.delete('tab');
        }

        // Atualizar a URL sem recarregar a página
        window.history.pushState({}, '', url);

        // Fazer requisição AJAX para buscar dados filtrados
        fetch(url)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Atualizar conteúdo das abas
                const pendentesTab = doc.querySelector('#pendentes');
                const removidosTab = doc.querySelector('#removidos');

                if (pendentesTab && tabId === 'pendentes') {
                    document.querySelector('#pendentes').innerHTML = pendentesTab.innerHTML;
                }
                if (removidosTab && tabId === 'removidos') {
                    document.querySelector('#removidos').innerHTML = removidosTab.innerHTML;
                }

                // Reaplica os event listeners
                initializeFilterListeners();

                // Restaura os estados visuais dos filtros
                activeFilters.forEach((val, key) => {
                    const [tab, col] = key.split('_');
                    if (val && tab === tabId) {
                        const filterIcon = document.querySelector(`#${tab} .filter-icon[data-column="${col}"]`);
                        if (filterIcon) {
                            filterIcon.classList.add('has-filter');
                        }
                        const input = document.querySelector(`#${tab} input[data-column="${col}"]`);
                        if (input) {
                            input.value = val;
                            if (activeFilterInputs.has(`${tab}_${col}`)) {
                                input.closest('.filter-input').classList.add('show');
                                input.focus();
                            }
                        }
                    }
                });

                // Reaplica os event listeners para os botões de ação
                document.querySelectorAll('[onclick^="removerCRL"]').forEach(btn => {
                    const cpf = btn.getAttribute('onclick').match(/'([^']+)'/)[1];
                    btn.onclick = () => removerCRL(cpf);
                });
                document.querySelectorAll('[onclick^="restaurarCRL"]').forEach(btn => {
                    const cpf = btn.getAttribute('onclick').match(/'([^']+)'/)[1];
                    btn.onclick = () => restaurarCRL(cpf);
                });
            })
            .catch(error => {
                console.error('Erro ao filtrar:', error);
            });
    }

    // Inicializar event listeners
    function initializeFilterListeners() {
        // Toggle do filtro ao clicar no ícone de lupa
        document.querySelectorAll('.filter-icon').forEach(icon => {
            const searchIcon = icon.querySelector('.bi-search');
            const clearIcon = icon.querySelector('.clear-filter');
            const column = icon.dataset.column;

            searchIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                const filterInput = icon.nextElementSibling;
                const wasOpen = filterInput.classList.contains('show');
                const activeTab = document.querySelector('.tab-pane.active');
                const tabId = activeTab.id;

                // Fecha todos os outros filtros
                document.querySelectorAll('.filter-input').forEach(input => {
                    if (input !== filterInput) {
                        input.classList.remove('show');
                        const inputColumn = input.querySelector('input').dataset.column;
                        activeFilterInputs.delete(`${tabId}_${inputColumn}`);
                    }
                });

                // Toggle do filtro atual
                if (wasOpen) {
                    filterInput.classList.remove('show');
                    activeFilterInputs.delete(`${tabId}_${column}`);
                } else {
                    filterInput.classList.add('show');
                    activeFilterInputs.add(`${tabId}_${column}`);
                    filterInput.querySelector('input').focus();
                }
            });

            // Limpar filtro ao clicar no X
            clearIcon.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const filterInput = icon.nextElementSibling;
                const input = filterInput.querySelector('input');
                const activeTab = document.querySelector('.tab-pane.active');
                const tabId = activeTab.id;

                // Limpa o valor do input
                input.value = '';

                // Remove classes de estado
                icon.classList.remove('has-filter');
                filterInput.classList.remove('show');

                // Limpa os registros de estado
                activeFilters.delete(`${tabId}_${column}`);
                activeFilterInputs.delete(`${tabId}_${column}`);

                // Recarrega a página com o filtro limpo
                const url = new URL(window.location.href);
                url.searchParams.delete(`filter_${column}`);
                if (tabId === 'removidos') {
                    url.searchParams.set('tab', 'removidos');
                }
                window.location.href = url.toString();
            });
        });

        // Prevenir que o clique no input feche o filtro
        document.querySelectorAll('.filter-input').forEach(filterInput => {
            filterInput.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            const input = filterInput.querySelector('input');
            if (input) {
                input.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const activeTab = document.querySelector('.tab-pane.active');
                    const tabId = activeTab.id;
                    activeFilterInputs.add(`${tabId}_${this.dataset.column}`);
                });
            }
        });

        // Fechar filtro ao clicar fora
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.column-filter')) {
                document.querySelectorAll('.filter-input').forEach(input => {
                    input.classList.remove('show');
                });
                activeFilterInputs.clear();
            }
        });

        // Lógica de filtro em tempo real
        document.querySelectorAll('.filter-input input').forEach(input => {
            input.addEventListener('input', function() {
                const column = this.dataset.column;
                const value = this.value.toLowerCase();
                const activeTab = document.querySelector('.tab-pane.active');
                const tabId = activeTab.id;
                activeFilterInputs.add(`${tabId}_${column}`); // Mantém o registro de que este input está ativo
                applyFilter(column, value);
            });
        });
    }

    // Inicializar os listeners
    initializeFilterListeners();

    // Adicionar listeners para as abas
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (e) {
            const activeTab = e.target.getAttribute('data-bs-target').substring(1);
            const url = new URL(window.location.href);

            if (activeTab === 'removidos') {
                url.searchParams.set('tab', 'removidos');
            } else {
                url.searchParams.delete('tab');
            }

            window.history.pushState({}, '', url);

            // Limpar os filtros ao trocar de aba
            document.querySelectorAll('.filter-icon').forEach(icon => {
                icon.classList.remove('has-filter');
            });
            document.querySelectorAll('.filter-input').forEach(input => {
                input.classList.remove('show');
                input.querySelector('input').value = '';
            });
            activeFilters.clear();
            activeFilterInputs.clear();
        });
    });

    // Restaurar estado dos filtros da URL
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('tab') || 'pendentes';

    urlParams.forEach((value, key) => {
        if (key.startsWith('filter_')) {
            const column = key.replace('filter_', '');
            const input = document.querySelector(`#${activeTab} input[data-column="${column}"]`);
            if (input) {
                input.value = value;
                updateFilterState(column, value);
            }
        }
    });
});
</script>
{% endblock %}