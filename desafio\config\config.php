<?php
// Configurações do Banco de Dados
define('DB_HOST', 'rpa.sicoobcredilivre.com.br');
define('DB_USER', 'rpa');
define('DB_PASS', 'sicoob@123');
define('DB_NAME', 'rpa');
define('DB_PORT', 3306);

// Configurações do LDAP
define('LDAP_HOST', 'administracao.local');
define('LDAP_PORT', 389);
define('LDAP_VERSION', 3);
define('LDAP_BASE_DN', 'DC=administracao,DC=local');

// Conexão com o banco de dados
try {
    $options = array(
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 5
    );

    // Adiciona a opção de SSL apenas se a constante existir
    if (defined('PDO::MYSQL_ATTR_SSL_DISABLE')) {
        $options[PDO::MYSQL_ATTR_SSL_DISABLE] = true;
    }

    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        $options
    );
} catch(PDOException $e) {
    die("Erro na conexão com o banco de dados: " . $e->getMessage());
}

// Funções úteis
function get_pa_from_username($username) {
    // Se for admin.pedro, retorna null para ter acesso a todos os PAs
    if ($username === 'admin.pedro') {
        return null;
    }
    
    // Tenta encontrar um número de 2 dígitos no username
    if (preg_match('/_(\d{2})$/', $username, $matches)) {
        return $matches[1];
    }
    
    // Se não encontrar no formato padrão, procura por qualquer número de 2 dígitos
    if (preg_match('/(\d{2})/', $username, $matches)) {
        return $matches[1];
    }
    
    // Se não encontrar nenhum PA, retorna null
    return null;
} 