import mysql.connector
from datetime import datetime, timed<PERSON><PERSON>

def get_mysql_connection():
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Erro ao conectar ao MySQL: {err}")
        raise

def verificar_e_registrar():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica execuções recentes
        print("\nVerificando execuções recentes...")
        cursor.execute("""
            SELECT id, rpa_name, start_time, end_time, status, active_time
            FROM rpa_executions
            WHERE start_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY start_time DESC
        """)
        
        execucoes = cursor.fetchall()
        if execucoes:
            print("\nExecuções encontradas nas últimas 24 horas:")
            for exec in execucoes:
                print(f"\nID: {exec[0]}")
                print(f"RPA: {exec[1]}")
                print(f"Início: {exec[2]}")
                print(f"Fim: {exec[3] if exec[3] else 'Em andamento'}")
                print(f"Status: {exec[4]}")
                print(f"Tempo ativo: {exec[5]} segundos")
        else:
            print("\nNenhuma execução encontrada nas últimas 24 horas.")
        
        # Verifica se há alguma execução em andamento
        cursor.execute("""
            SELECT id, rpa_name, start_time
            FROM rpa_executions
            WHERE status = 'running'
            ORDER BY start_time DESC
            LIMIT 1
        """)
        
        execucao_atual = cursor.fetchone()
        if execucao_atual:
            print(f"\nExecução em andamento encontrada:")
            print(f"ID: {execucao_atual[0]}")
            print(f"RPA: {execucao_atual[1]}")
            print(f"Iniciado em: {execucao_atual[2]}")
        else:
            print("\nNenhuma execução em andamento encontrada.")
            
            # Pergunta se deseja registrar novo início
            resposta = input("\nDeseja registrar um novo início de execução? (s/n): ")
            if resposta.lower() == 's':
                # Registra novo início
                now = datetime.now()
                cursor.execute("""
                    INSERT INTO rpa_executions 
                    (rpa_name, start_time, status, active_time, last_status_update)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    'RPA_ASSOCIADOS',
                    now,
                    'running',
                    0,
                    now
                ))
                
                execution_id = cursor.lastrowid
                
                # Registra no histórico
                cursor.execute("""
                    INSERT INTO rpa_execution_history 
                    (execution_id, status, start_time)
                    VALUES (%s, %s, %s)
                """, (
                    execution_id,
                    'running',
                    now
                ))
                
                conn.commit()
                print("\nNovo início de execução registrado com sucesso!")
                print(f"ID: {execution_id}")
                print(f"Início: {now}")
        
    except Exception as e:
        print(f"\nErro: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    verificar_e_registrar() 