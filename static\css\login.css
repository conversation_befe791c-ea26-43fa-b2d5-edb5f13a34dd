.btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    color: white;
}

.btn-check + .btn-outline-primary {
    flex: 1;
    border-radius: 0;
}

.btn-check + .btn-outline-primary:first-of-type {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.btn-check + .btn-outline-primary:last-of-type {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.card {
    border: none;
    border-radius: 1rem;
}

.card-body {
    padding: 2rem;
}

.form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.btn-primary {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
}

.alert {
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
} 