{% extends "base.html" %}

{% block extra_css %}
<style>
    /* ===== CORES DA IDENTIDADE VISUAL SICOOB ===== */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
    }

    /* Classes utilitárias de cores */
    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* ===== HEADER MODERNO ===== */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(125, 182, 28, 0.05) 0%, rgba(0, 174, 157, 0.05) 100%);
        border-radius: 16px;
        padding: 32px;
        border: 1px solid rgba(125, 182, 28, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        margin-bottom: 32px;
    }

    .page-icon-modern {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        box-shadow: 0 6px 20px rgba(125, 182, 28, 0.4);
    }

    /* ===== SEÇÕES MODERNAS ===== */
    .section-header-modern {
        margin-bottom: 24px;
        padding: 20px 0;
        border-bottom: 2px solid rgba(0, 174, 157, 0.1);
    }

    .section-title-modern {
        font-size: 24px;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .section-icon-modern {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* ===== CARDS MODERNOS ===== */
    .upload-card-modern {
        background: white;
        border-radius: 16px;
        padding: 32px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: none;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .upload-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        transition: all 0.3s ease;
    }

    .upload-card-modern:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }

    .upload-card-modern:hover::before {
        height: 6px;
    }

    .card-limites-disponiveis::before {
        background: linear-gradient(90deg, var(--sicoob-azul) 0%, #008a7a 100%);
    }

    .card-limites-uso::before {
        background: linear-gradient(90deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
    }

    .card-cartoes::before {
        background: linear-gradient(90deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
    }

    .card-header-modern {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        gap: 16px;
    }

    .card-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .card-title-modern {
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    /* ===== ALERTS MODERNOS ===== */
    .alert-modern {
        border-radius: 12px;
        padding: 16px 20px;
        margin-bottom: 20px;
        border: none;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        position: relative;
        overflow: hidden;
    }

    .alert-modern::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
    }

    .alert-info-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.1) 0%, rgba(0, 138, 122, 0.05) 100%);
        color: var(--sicoob-azul);
    }

    .alert-info-modern::before {
        background: var(--sicoob-azul);
    }

    .alert-path-modern {
        background: linear-gradient(135deg, rgba(142, 154, 175, 0.1) 0%, rgba(122, 133, 153, 0.05) 100%);
        color: var(--sicoob-cinza);
    }

    .alert-path-modern::before {
        background: var(--sicoob-cinza);
    }

    .alert-icon-modern {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.2);
        flex-shrink: 0;
        margin-top: 2px;
    }

    .alert-content-modern {
        flex: 1;
        font-weight: 500;
        line-height: 1.5;
    }

    /* ===== FORMULÁRIOS MODERNOS ===== */
    .form-modern {
        margin-top: 24px;
    }

    .file-input-modern {
        position: relative;
        margin-bottom: 20px;
    }

    .file-input-modern input[type="file"] {
        border-radius: 12px;
        border: 2px dashed rgba(0, 174, 157, 0.3);
        padding: 16px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: rgba(0, 174, 157, 0.02);
    }

    .file-input-modern input[type="file"]:hover {
        border-color: var(--sicoob-azul);
        background: rgba(0, 174, 157, 0.05);
    }

    .file-input-modern input[type="file"]:focus {
        outline: none;
        border-color: var(--sicoob-azul);
        box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.1);
    }

    /* ===== PROGRESS BAR MODERNO ===== */
    .progress-modern {
        height: 12px;
        border-radius: 6px;
        background: rgba(0, 174, 157, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-bar-modern {
        border-radius: 6px;
        transition: width 0.3s ease;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: 600;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .progress-bar-azul {
        background: linear-gradient(90deg, var(--sicoob-azul) 0%, #008a7a 100%);
    }

    .progress-bar-verde {
        background: linear-gradient(90deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
    }

    .progress-bar-roxo {
        background: linear-gradient(90deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
    }

    /* ===== BOTÕES MODERNOS ===== */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    .btn-sicoob-success {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        color: white;
    }

    .btn-sicoob-success:hover {
        color: white;
    }

    .btn-sicoob-purple {
        background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
        color: white;
    }

    .btn-sicoob-purple:hover {
        color: white;
    }

    .btn-actions-modern {
        display: flex;
        gap: 12px;
        margin-top: 24px;
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 24px 20px;
        }

        .page-icon-modern {
            width: 56px;
            height: 56px;
            font-size: 24px;
        }

        .upload-card-modern {
            padding: 24px 20px;
        }

        .card-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .card-title-modern {
            font-size: 18px;
        }

        .section-icon-modern {
            width: 40px;
            height: 40px;
            font-size: 18px;
        }

        .section-title-modern {
            font-size: 20px;
        }

        .btn-actions-modern {
            flex-direction: column;
        }

        .btn-modern {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center">
                    <div class="page-icon-modern bg-sicoob-verde-escuro me-4">
                        <i class="bi bi-wallet2"></i>
                    </div>
                    <div>
                        <h1 class="h2 mb-2 text-sicoob-verde-escuro">Enquadramento de Limites</h1>
                        <p class="text-muted mb-0 lead">Faça o upload das planilhas de enquadramento de limites e cheque especial</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Primeira Seção - Limites Disponíveis -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="section-header-modern">
                <h2 class="section-title-modern text-sicoob-azul">
                    <div class="section-icon-modern bg-sicoob-azul">
                        <i class="bi bi-wallet2"></i>
                    </div>
                    Limites Disponíveis
                </h2>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-12">
            <div class="row")
                <!-- Card Upload Crédito Pessoal -->
                <div class="col-lg-6 mb-4">
                    <div class="upload-card-modern card-limites-disponiveis">
                        <div class="card-header-modern">
                            <div class="card-icon-modern bg-sicoob-azul">
                                <i class="bi bi-credit-card"></i>
                            </div>
                            <div>
                                <h3 class="card-title-modern">Upload Crédito Pessoal</h3>
                            </div>
                        </div>

                        <div class="alert-modern alert-info-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="alert-content-modern">
                                Faça o upload da Planilha de Crédito Pessoal no formato CSV (separado por ponto e vírgula)
                            </div>
                        </div>

                        <div class="alert-modern alert-path-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-folder"></i>
                            </div>
                            <div class="alert-content-modern">
                                <p class="mb-2"><strong>Caminho para download do relatório:</strong></p>
                                <p class="mb-0 small">
                                    Sisbr 2.0 - Plataforma de Crédito - Concessão de Limites - Créd Automático - Relatórios - Relatório de Clientes Enquadrados
                                </p>
                            </div>
                        </div>

                        <form id="uploadLimitesForm" enctype="multipart/form-data" class="form-modern">
                            <div class="file-input-modern">
                                <input class="form-control form-control-lg" type="file" id="limitesFile" name="file" accept=".csv">
                            </div>

                            <div class="progress-modern d-none" id="progressLimites">
                                <div class="progress-bar-modern progress-bar-azul" role="progressbar" style="width: 0%;">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>

                            <div class="btn-actions-modern">
                                <button type="submit" class="btn-modern btn-sicoob-primary" id="uploadBtn">
                                    <i class="bi bi-upload"></i>
                                    <span>Upload</span>
                                </button>
                                <button type="button" class="btn-modern btn-sicoob-success d-none" id="executarLimitesBtn">
                                    <i class="bi bi-play-fill"></i>
                                    <span>Executar</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Card Upload Cheque Especial -->
                <div class="col-lg-6 mb-4">
                    <div class="upload-card-modern card-limites-disponiveis">
                        <div class="card-header-modern">
                            <div class="card-icon-modern bg-sicoob-azul">
                                <i class="bi bi-bank"></i>
                            </div>
                            <div>
                                <h3 class="card-title-modern">Upload Cheque Especial</h3>
                            </div>
                        </div>

                        <div class="alert-modern alert-info-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="alert-content-modern">
                                Faça o upload da Planilha de Cheque Especial no formato CSV (separado por ponto e vírgula)
                            </div>
                        </div>

                        <div class="alert-modern alert-path-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-folder"></i>
                            </div>
                            <div class="alert-content-modern">
                                <p class="mb-2"><strong>Caminho para download do relatório:</strong></p>
                                <p class="mb-0 small">
                                    Sisbr 2.0 - Plataforma de Crédito - Concessão de Limites - Cheque Especial - Relatórios - Relatório de Clientes Enquadrados
                                </p>
                            </div>
                        </div>

                        <form id="uploadChequeForm" enctype="multipart/form-data" class="form-modern">
                            <div class="file-input-modern">
                                <input class="form-control form-control-lg" type="file" id="chequeFile" name="file" accept=".csv">
                            </div>

                            <div class="progress-modern d-none" id="progressCheque">
                                <div class="progress-bar-modern progress-bar-azul" role="progressbar" style="width: 0%;">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>

                            <div class="btn-actions-modern">
                                <button type="submit" class="btn-modern btn-sicoob-primary" id="uploadChequeBtn">
                                    <i class="bi bi-upload"></i>
                                    <span>Upload</span>
                                </button>
                                <button type="button" class="btn-modern btn-sicoob-success d-none" id="executarChequeBtn">
                                    <i class="bi bi-play-fill"></i>
                                    <span>Executar</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Segunda Seção - Limites Em Uso -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="section-header-modern">
                <h2 class="section-title-modern text-sicoob-verde-escuro">
                    <div class="section-icon-modern bg-sicoob-verde-escuro">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    Limites Em Uso
                </h2>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="row">
                <!-- Card Upload Crédito Pessoal e Cheque Especial Em Uso -->
                <div class="col-lg-12 mb-4">
                    <div class="upload-card-modern card-limites-uso">
                        <div class="card-header-modern">
                            <div class="card-icon-modern bg-sicoob-verde-escuro">
                                <i class="bi bi-currency-exchange"></i>
                            </div>
                            <div>
                                <h3 class="card-title-modern">Upload Crédito Pessoal e Cheque Especial (Em uso)</h3>
                            </div>
                        </div>

                        <div class="alert-modern alert-info-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="alert-content-modern">
                                Faça o upload da Planilha de Limites em Uso no formato Excel (.xlsx ou .xls)
                            </div>
                        </div>

                        <div class="alert-modern alert-path-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-folder"></i>
                            </div>
                            <div class="alert-content-modern">
                                <p class="mb-2"><strong>Caminho para download do relatório:</strong></p>
                                <p class="mb-0 small">
                                    Sisbr Analítico / Conteúdo da equipe / Área Pública - Consumidor / Área Pública da Central 1003 - Consumidor / Área Pública da Central 1003 / 01 - Crédito / 01.08 - fábrica de limites / 1.3- Gestão de Contratos da Fábrica (sem Cartão) - Visão Analítica
                                </p>
                            </div>
                        </div>

                        <form id="uploadLimitesUsoForm" enctype="multipart/form-data" class="form-modern">
                            <div class="file-input-modern">
                                <input class="form-control form-control-lg" type="file" id="limitesUsoFile" name="file" accept=".xlsx,.xls">
                            </div>

                            <div class="progress-modern d-none" id="progressLimitesUso">
                                <div class="progress-bar-modern progress-bar-verde" role="progressbar" style="width: 0%;">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>

                            <div class="btn-actions-modern">
                                <button type="submit" class="btn-modern btn-sicoob-success" id="uploadLimitesUsoBtn">
                                    <i class="bi bi-upload"></i>
                                    <span>Upload</span>
                                </button>
                                <button type="button" class="btn-modern btn-sicoob-success d-none" id="executarLimitesUsoBtn">
                                    <i class="bi bi-play-fill"></i>
                                    <span>Executar</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Card Upload Cartões -->
                <div class="col-lg-12 mb-4">
                    <div class="upload-card-modern card-cartoes">
                        <div class="card-header-modern">
                            <div class="card-icon-modern bg-sicoob-roxo">
                                <i class="bi bi-credit-card-2-front"></i>
                            </div>
                            <div>
                                <h3 class="card-title-modern">Upload Cartões</h3>
                            </div>
                        </div>

                        <div class="alert-modern alert-info-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="alert-content-modern">
                                Faça o upload da Planilha de Cartões no formato Excel (.xlsx)
                            </div>
                        </div>

                        <div class="alert-modern alert-path-modern">
                            <div class="alert-icon-modern">
                                <i class="bi bi-folder"></i>
                            </div>
                            <div class="alert-content-modern">
                                <p class="mb-2"><strong>Caminho para download do relatório:</strong></p>
                                <p class="mb-0 small">
                                    Sisbr Analítico / Conteúdo da equipe / Área Pública - Consumidor / Área Pública do Sicoob - Consumidor / Números e Negócios / Relatórios / 02. Analítico / CARTÕES / SICOOBCARD - BASE E ATIVAÇÃO / Contas Cartão - Relação Analítica de Contas
                                </p>
                            </div>
                        </div>

                        <form id="uploadCartoesForm" enctype="multipart/form-data" class="form-modern">
                            <div class="file-input-modern">
                                <input class="form-control form-control-lg" type="file" id="cartoesFile" name="file" accept=".xlsx">
                            </div>

                            <div class="progress-modern d-none" id="progressCartoes">
                                <div class="progress-bar-modern progress-bar-roxo" role="progressbar" style="width: 0%;">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>

                            <div class="btn-actions-modern">
                                <button type="submit" class="btn-modern btn-sicoob-purple" id="uploadCartoesBtn">
                                    <i class="bi bi-upload"></i>
                                    <span>Upload</span>
                                </button>
                                <button type="button" class="btn-modern btn-sicoob-purple d-none" id="executarCartoesBtn">
                                    <i class="bi bi-play-fill"></i>
                                    <span>Executar</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Processamento -->
<div class="modal fade" id="processingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-body p-5">
                <div class="text-center mb-4">
                    <div class="spinner-border" style="width: 3rem; height: 3rem; color: #00AE9D;" role="status">
                        <span class="visually-hidden">Processando...</span>
                    </div>
                </div>
                <h4 class="text-center mb-3">Processando Enquadramento de Limites</h4>
                <p class="text-muted text-center mb-0" id="processingStatus">Aguarde enquanto processamos os dados...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Resultado -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 pb-0">
                <h4 class="modal-title">Resultado do Processamento</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                </div>
                <div id="resultContent" class="text-center">
                    <!-- O conteúdo será preenchido via JavaScript -->
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn px-4" data-bs-dismiss="modal" style="background-color: #00AE9D; color: white;">Fechar</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    let processingModal = null;
    let resultModal = null;

    function showProcessingModal() {
        if (!processingModal) {
            processingModal = new bootstrap.Modal(document.getElementById('processingModal'));
        }
        processingModal.show();
    }

    function hideProcessingModal() {
        if (processingModal) {
            processingModal.hide();
        }
    }

    function flash(type, message) {
        const event = new CustomEvent('flash', {
            detail: { type, message }
        });
        document.dispatchEvent(event);
    }

    function parseResultMessage(message) {
        const lines = message.split('\n');
        const result = {
            total_registros: 0,
            registros_atualizados: 0,
            registros_sem_status: 0,
            registros_ignorados: 0
        };

        lines.forEach(line => {
            if (line.includes('Total de registros na planilha:')) {
                result.total_registros = parseInt(line.split(':')[1].trim());
            } else if (line.includes('CPFs atualizados (status 3 ou 8):') || line.includes('Registros atualizados (status 3 ou 8):')) {
                result.registros_atualizados = parseInt(line.split(':')[1].trim());
            } else if (line.includes('Registros sem status 3 ou 8:') || line.includes('CPFs sem status 3 ou 8:')) {
                result.registros_sem_status = parseInt(line.split(':')[1].trim());
            } else if (line.includes('Registros ignorados')) {
                const ignoredPart = line.split(':')[1].split('(')[0].trim();
                result.registros_ignorados = parseInt(ignoredPart);
            }
        });

        return `
            <h4 class="mb-4" style="color: #00AE9D;">Processamento Concluído</h4>
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="p-3 border bg-light rounded">
                        <i class="bi bi-file-text fs-3" style="color: #00AE9D;"></i>
                        <h5 class="mt-2">CPFs Processados</h5>
                        <h3 style="color: #00AE9D;">${result.total_registros}</h3>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="p-3 border bg-light rounded">
                        <i class="bi bi-check-circle text-success fs-3"></i>
                        <h5 class="mt-2">CPFs Atualizados</h5>
                        <h3 class="text-success">${result.registros_atualizados}</h3>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="p-3 border bg-light rounded">
                        <i class="bi bi-exclamation-circle text-warning fs-3"></i>
                        <h5 class="mt-2">CPFs Pendentes</h5>
                        <h3 class="text-warning">${result.registros_sem_status}</h3>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="p-3 border bg-light rounded">
                        <i class="bi bi-x-circle text-danger fs-3"></i>
                        <h5 class="mt-2">CPFs Ignorados</h5>
                        <h3 class="text-danger">${result.registros_ignorados}</h3>
                    </div>
                </div>
            </div>
        `;
    }

    function showResultModal(result) {
        if (!resultModal) {
            resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
        }
        
        const resultContent = document.getElementById('resultContent');
        resultContent.innerHTML = parseResultMessage(result.message);
        
        resultModal.show();
    }

    function handleFileUpload(formId, progressId, uploadUrl, executeUrl) {
        const form = document.getElementById(formId);
        const progress = document.getElementById(progressId);
        const progressBar = progress.querySelector('.progress-bar-modern');
        const progressText = progressBar.querySelector('.progress-text');
        const uploadBtn = form.querySelector('button[type="submit"]');
        const executarBtn = form.querySelector('button[type="button"]');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            progress.classList.remove('d-none');
            progressBar.style.width = '0%';
            progressText.textContent = '0%';
            
            try {
                progressBar.style.width = '50%';
                progressText.textContent = '50%';

                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Erro no upload');
                }

                progressBar.style.width = '100%';
                progressText.textContent = '100%';
                uploadBtn.classList.add('d-none');
                executarBtn.classList.remove('d-none');
                flash('success', 'Upload realizado com sucesso!');
            } catch (error) {
                flash('danger', 'Erro ao realizar upload: ' + error.message);
                progress.classList.add('d-none');
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
            }
        });

        executarBtn.addEventListener('click', async () => {
            try {
                showProcessingModal();
                const response = await fetch(executeUrl, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Erro no processamento');
                }

                const result = await response.json();
                hideProcessingModal();
                showResultModal(result);

                // Reset form and progress
                form.reset();
                progress.classList.add('d-none');
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
                executarBtn.classList.add('d-none');
                uploadBtn.classList.remove('d-none');
            } catch (error) {
                hideProcessingModal();
                flash('danger', 'Erro ao processar: ' + error.message);
            }
        });
    }

    // Inicializa os handlers para os formulários
    handleFileUpload(
        'uploadLimitesForm',
        'progressLimites',
        '/upload/limites',
        '/executar/limites'
    );

    handleFileUpload(
        'uploadChequeForm',
        'progressCheque',
        '/upload/cheque',
        '/executar/cheque'
    );

    handleFileUpload(
        'uploadLimitesUsoForm',
        'progressLimitesUso',
        '/upload/limites-uso',
        '/executar/limites-uso'
    );

    // Inicializa o handler para o formulário de cartões
    handleFileUpload(
        'uploadCartoesForm',
        'progressCartoes',
        '/upload/cartoes',
        '/executar/cartoes'
    );
</script>
{% endblock %}
