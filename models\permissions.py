"""
Gerenciamento de permissões de usuários
"""
import json
import os
from pathlib import Path
from config.ldap_config import get_ldap_connection, search_user, logger

PERMISSIONS_FILE = Path(__file__).parent.parent / 'data' / 'permissions.json'

# Garante que o diretório data existe
PERMISSIONS_FILE.parent.mkdir(exist_ok=True)

# Módulos disponíveis no sistema
AVAILABLE_MODULES = {
    'dashboard': 'Dashboard',
    'rpa_monitor': 'RPA Monitor',
    'cpfs': 'CPFs',
    'permissions': 'Gerenciamento de Permissões',
    'uploads': 'Upload de Planilhas',
    'robozometro': 'Robozôme<PERSON>'
}

def migrate_permissions():
    """Migra o arquivo de permissões para o novo formato"""
    if PERMISSIONS_FILE.exists():
        try:
            with open(PERMISSIONS_FILE, 'r', encoding='utf-8') as f:
                old_permissions = json.load(f)
            
            # Cria nova estrutura
            new_permissions = {
                'admins': old_permissions.get('admins', []),
                'enabled_users': [],
                'module_access': old_permissions.get('module_access', {}),
                'user_info': {}
            }
            
            # Habilita todos os usuários que já tinham permissões
            for username in new_permissions['admins']:
                if username not in new_permissions['enabled_users']:
                    new_permissions['enabled_users'].append(username)
            
            for username in new_permissions['module_access'].keys():
                if username not in new_permissions['enabled_users']:
                    new_permissions['enabled_users'].append(username)
            
            # Salva nova estrutura
            with open(PERMISSIONS_FILE, 'w', encoding='utf-8') as f:
                json.dump(new_permissions, f, indent=4, ensure_ascii=False)
            
            return new_permissions
        except Exception as e:
            logger.error(f"Erro ao migrar permissões: {e}")
            return get_default_permissions()
    return get_default_permissions()

def get_default_permissions():
    """Retorna a estrutura padrão de permissões"""
    return {
        'admins': [],  # Lista de usuários administradores
        'enabled_users': [],  # Lista de usuários habilitados
        'module_access': {},  # Dicionário de usuário -> lista de módulos permitidos
        'user_info': {}  # Dicionário de usuário -> informações adicionais (nome completo, etc)
    }

def load_permissions():
    """Carrega as permissões do arquivo"""
    if PERMISSIONS_FILE.exists():
        try:
            with open(PERMISSIONS_FILE, 'r', encoding='utf-8') as f:
                permissions = json.load(f)
                # Garante que tem todos os campos necessários
                if 'user_info' not in permissions:
                    permissions['user_info'] = {}
                return permissions
        except Exception as e:
            logger.error(f"Erro ao carregar permissões: {e}")
            return get_default_permissions()
    return get_default_permissions()

def save_permissions(permissions):
    """Salva as permissões no arquivo"""
    # Garante que tem todos os campos necessários
    if 'enabled_users' not in permissions:
        permissions['enabled_users'] = []
    if 'admins' not in permissions:
        permissions['admins'] = []
    if 'module_access' not in permissions:
        permissions['module_access'] = {}
    if 'user_info' not in permissions:
        permissions['user_info'] = {}
    
    with open(PERMISSIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(permissions, f, indent=4, ensure_ascii=False)

def verify_ad_user(username):
    """Verifica se um usuário existe no AD"""
    try:
        conn = get_ldap_connection()
        if not conn:
            return None
        
        user_entry = search_user(conn, username)
        if user_entry:
            # Retorna o nome completo do usuário
            display_name = str(user_entry['displayName']) if 'displayName' in user_entry else username
            return display_name
        return None
    except Exception as e:
        logger.error(f"Erro ao verificar usuário no AD: {e}")
        return None

def add_user(username, full_name):
    """Adiciona um novo usuário"""
    if username == 'administrador':
        return False, "Não é possível adicionar o usuário administrador"
    
    permissions = load_permissions()
    
    # Verifica se o usuário já existe
    if username in permissions.get('user_info', {}):
        return False, "Usuário já cadastrado"
    
    # Adiciona o usuário
    if 'user_info' not in permissions:
        permissions['user_info'] = {}
    permissions['user_info'][username] = {'full_name': full_name}
    
    # Define permissões padrão
    if username not in permissions['module_access']:
        permissions['module_access'][username] = ['cpfs']
    
    save_permissions(permissions)
    return True, "Usuário adicionado com sucesso"

def get_user_permissions(username):
    """Retorna as permissões de um usuário"""
    # Usuário administrador sempre tem acesso total
    if username == 'administrador':
        return {
            'is_admin': True,
            'enabled': True,
            'modules': list(AVAILABLE_MODULES.keys()),
            'full_name': 'Administrador'
        }
    
    permissions = load_permissions()
    
    # Verifica se o usuário está habilitado
    is_enabled = username in permissions['enabled_users']
    
    # Obtém o nome completo
    full_name = permissions.get('user_info', {}).get(username, {}).get('full_name', username)
    
    # Se é admin, tem acesso a tudo
    if username in permissions['admins']:
        return {
            'is_admin': True,
            'enabled': is_enabled,
            'modules': list(AVAILABLE_MODULES.keys()),
            'full_name': full_name
        }
    
    # Usuário comum - acesso padrão apenas ao módulo CPFs se estiver habilitado
    modules = permissions.get('module_access', {}).get(username, ['cpfs'])
    return {
        'is_admin': False,
        'enabled': is_enabled,
        'modules': modules if is_enabled else [],
        'full_name': full_name
    }

def update_user_permissions(username, is_admin, is_enabled, allowed_modules):
    """Atualiza as permissões de um usuário"""
    if username == 'administrador':
        return  # Não permite modificar o usuário administrador
        
    permissions = load_permissions()
    
    # Atualiza status de admin
    if is_admin:
        if username not in permissions['admins']:
            permissions['admins'].append(username)
    else:
        if username in permissions['admins']:
            permissions['admins'].remove(username)
    
    # Atualiza status de habilitado
    if is_enabled:
        if username not in permissions['enabled_users']:
            permissions['enabled_users'].append(username)
    else:
        if username in permissions['enabled_users']:
            permissions['enabled_users'].remove(username)
        # Se desabilitado, remove todas as permissões
        if username in permissions['module_access']:
            del permissions['module_access'][username]
        return
    
    # Atualiza acesso aos módulos
    if not is_admin:  # Admins sempre têm acesso a tudo
        permissions['module_access'][username] = allowed_modules
    elif username in permissions['module_access']:
        # Se virou admin, remove da lista de módulos específicos
        del permissions['module_access'][username]
    
    save_permissions(permissions)

def get_all_users_permissions():
    """Retorna as permissões de todos os usuários"""
    permissions = load_permissions()
    return {
        'admins': permissions['admins'],
        'enabled_users': permissions['enabled_users'],
        'module_access': permissions['module_access'],
        'user_info': permissions.get('user_info', {})
    }

def is_user_enabled(username):
    """Verifica se um usuário está habilitado"""
    if username == 'administrador':
        return True
    permissions = load_permissions()
    return username in permissions['enabled_users']

def get_domain_users():
    """Retorna lista de usuários do domínio"""
    try:
        # Tenta primeiro uma conexão autenticada com o usuário atual
        from flask_login import current_user
        if current_user and current_user.is_authenticated and hasattr(current_user, 'password'):
            conn = get_ldap_connection(current_user.username, current_user.password)
        else:
            conn = get_ldap_connection()
            
        if not conn:
            print("Não foi possível estabelecer conexão com o LDAP")
            return []
        
        from config.ldap_config import get_all_users
        return get_all_users(conn)
    except Exception as e:
        print(f"Erro ao buscar usuários do domínio: {e}")
        return []
