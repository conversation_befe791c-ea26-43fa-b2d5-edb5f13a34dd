<?php
session_start();
require_once 'config/config.php';

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Conectar ao LDAP
    $ldap = ldap_connect(LDAP_HOST, LDAP_PORT);
    ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, LDAP_VERSION);
    ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);

    if ($ldap) {
        // Tenta autenticar o usuário
        $bind = @ldap_bind($ldap, $username . '@' . LDAP_HOST, $password);
        
        if ($bind) {
            // Autenticação bem sucedida
            $pa = get_pa_from_username($username);
            
            // Verifica se o usuário já existe na tabela campanha_users
            $stmt = $pdo->prepare("SELECT id, is_admin FROM campanha_users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user) {
                // Se não existir, cadastra o usuário
                $stmt = $pdo->prepare("INSERT INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
                $stmt->execute([
                    $username,
                    $pa ? $pa : '00',
                    ($username === 'admin.pedro') // mantém a lógica padrão apenas para novos usuários
                ]);
                
                // Busca os dados do usuário recém inserido
                $stmt = $pdo->prepare("SELECT id, is_admin FROM campanha_users WHERE username = ?");
                $stmt->execute([$username]);
                $user = $stmt->fetch();
            }
            
            // Define as variáveis de sessão
            $_SESSION['user_id'] = $username;
            $_SESSION['pa'] = $pa ? $pa : '00';
            $_SESSION['is_admin'] = $user['is_admin'] ? true : false;

            header('Location: index.php');
            exit();
        } else {
            $error = 'Usuário ou senha inválidos';
        }
        ldap_close($ldap);
    } else {
        $error = 'Erro ao conectar ao servidor LDAP';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Desafio Sicoob</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="page-container">
        <div class="campaign-header">
            <h1>
                <span class="campaign-title">
                    <i class="fas fa-robot"></i>
                    DESAFIO RPA
                    <i class="fas fa-cogs"></i>
                </span>
                <span class="campaign-subtitle">
                    <i class="fas fa-sync"></i>
                    automatizando processos, ampliando resultados
                    <i class="fas fa-chart-line"></i>
                </span>
            </h1>
        </div>

        <div class="login-container">
            <div class="login-box">
                <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
                <h2>Login do Desafio</h2>
                
                <?php if ($error): ?>
                    <div class="error-message"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="username">Usuário:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Senha:</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <button type="submit" class="btn-primary">Entrar</button>
                </form>
            </div>
        </div>
    </div>

    <style>
    .login-page {
        min-height: 100vh;
        background: var(--sicoob-light);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .page-container {
        width: 100%;
        max-width: 800px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .campaign-header {
        text-align: center;
        padding: 2.5rem;
        background: linear-gradient(135deg, var(--sicoob-turquoise) 0%, var(--sicoob-dark-green) 100%);
        border-radius: 8px;
        box-shadow: 0 8px 16px rgba(0, 54, 65, 0.15);
        position: relative;
        overflow: hidden;
        width: 100%;
    }

    .campaign-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--sicoob-light-green);
    }

    .campaign-header h1 {
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        position: relative;
    }

    .campaign-title {
        color: var(--sicoob-light-green);
        font-size: 3rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-title i {
        font-size: 2.5rem;
        animation: pulse 2s infinite;
    }

    .campaign-subtitle {
        color: white;
        font-size: 1.5rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .campaign-subtitle i {
        font-size: 1.25rem;
        opacity: 0.9;
    }

    .campaign-subtitle i.fa-sync {
        animation: spin 4s linear infinite;
    }

    .campaign-subtitle i.fa-chart-line {
        animation: slideUp 2s infinite;
    }

    .login-container {
        width: 100%;
        max-width: 400px;
    }

    .login-box {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        text-align: center;
    }

    .login-box .logo {
        height: 60px;
        margin-bottom: 1.5rem;
    }

    .login-box h2 {
        color: var(--sicoob-dark-green);
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
        text-align: left;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--sicoob-gray);
        font-weight: 600;
    }

    .form-group input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        transition: border-color 0.2s;
    }

    .form-group input:focus {
        border-color: var(--sicoob-turquoise);
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.1);
    }

    .btn-primary {
        background: var(--sicoob-turquoise);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
        width: 100%;
    }

    .btn-primary:hover {
        background: var(--sicoob-dark-green);
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    @keyframes slideUp {
        0% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
        100% { transform: translateY(0); }
    }

    @media (max-width: 768px) {
        .login-page {
            padding: 1rem;
        }

        .page-container {
            gap: 1.5rem;
        }

        .campaign-header {
            padding: 2rem 1.5rem;
        }

        .campaign-title {
            font-size: 2rem;
            letter-spacing: 1px;
        }
        
        .campaign-title i {
            font-size: 1.75rem;
        }

        .campaign-subtitle {
            font-size: 1.125rem;
        }

        .campaign-subtitle i {
            font-size: 1rem;
        }

        .login-box {
            padding: 1.5rem;
        }
    }
    </style>
</body>
</html> 