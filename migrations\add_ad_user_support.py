import os
import sys

# Adiciona o diretório raiz do projeto ao PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from utils.db import get_mysql_connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def column_exists(cursor, table, column):
    """Verifica se uma coluna existe na tabela"""
    cursor.execute("""
        SELECT COUNT(*)
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = %s
        AND column_name = %s
    """, (table, column))
    return cursor.fetchone()[0] > 0

def migrate():
    """
    Adiciona suporte para usuários AD na tabela users
    """
    conn = get_mysql_connection()
    cursor = conn.cursor()
    
    try:
        # Lista de colunas a serem adicionadas
        columns = [
            ('is_ad_user', 'TINYINT DEFAULT 0'),
            ('email', 'VARCHAR(255)'),
            ('department', 'VARCHAR(255)'),
            ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        ]
        
        # Adiciona cada coluna se ela não existir
        for column_name, column_def in columns:
            if not column_exists(cursor, 'users', column_name):
                logger.info(f"Adicionando coluna {column_name}...")
                cursor.execute(f'''
                    ALTER TABLE users
                    ADD COLUMN {column_name} {column_def}
                ''')
            else:
                logger.info(f"Coluna {column_name} já existe, pulando...")
        
        # Atualiza os usuários existentes como não-AD
        logger.info("Atualizando usuários existentes como não-AD...")
        cursor.execute('''
            UPDATE users
            SET is_ad_user = 0
            WHERE is_ad_user IS NULL
        ''')
        
        # Verifica se a tabela modules existe
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'modules'
        """)
        if cursor.fetchone()[0] == 0:
            logger.info("Criando tabela modules...")
            cursor.execute('''
                CREATE TABLE modules (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
        
        # Garante que o módulo robozometro existe nas permissões
        logger.info("Garantindo que o módulo robozometro existe...")
        cursor.execute('''
            INSERT IGNORE INTO modules (name, description)
            VALUES ('robozometro', 'Módulo Robozômetro - Acesso via AD')
        ''')
        
        conn.commit()
        logger.info("Migração concluída com sucesso!")
        
    except Exception as e:
        logger.error(f"Erro durante a migração: {str(e)}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate() 