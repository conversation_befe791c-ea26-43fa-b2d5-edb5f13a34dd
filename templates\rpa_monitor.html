{% extends "base.html" %}

{% block title %}RPA Monitor{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="page-icon-modern bg-sicoob-azul me-3">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div>
                            <h1 class="h3 mb-0 text-sicoob-verde-escuro">Monitoramento de RPAs</h1>
                            <p class="text-muted mb-0">Gerencie e monitore seus robôs de automação</p>
                        </div>
                    </div>
                    <button class="btn-modern btn-sicoob-primary" onclick="showAddRpaModal()">
                        <i class="bi bi-plus-lg me-2"></i>
                        <span>Adicionar RPA</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="rpasGrid">
        <!-- Cards serão inseridos aqui via JavaScript -->
    </div>
</div>

<!-- Modal para adicionar/editar RPA -->
<div class="modal fade" id="rpaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-robot me-2"></i>
                    Adicionar RPA
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rpaForm" onsubmit="event.preventDefault(); saveRpa();">
                    <div class="mb-3">
                        <label for="rpaName" class="form-label">Nome</label>
                        <input type="text" class="form-control" id="rpaName" required>
                    </div>
                    <div class="mb-3">
                        <label for="processName" class="form-label">Nome do Processo</label>
                        <input type="text" class="form-control" id="processName" required>
                    </div>
                    <div class="mb-3">
                        <label for="rpaHost" class="form-label">Host</label>
                        <input type="text" class="form-control" id="rpaHost" required>
                    </div>
                    <div class="mb-3">
                        <label for="rpaPort" class="form-label">Porta</label>
                        <input type="number" class="form-control" id="rpaPort" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="autoRestart">
                        <label class="form-check-label" for="autoRestart">Reinício Automático</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn-modern btn-sicoob-primary" onclick="saveRpa()">
                    <i class="bi bi-check-lg me-2"></i>
                    <span>Salvar</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de informações -->
<div class="modal fade" id="infoModal" tabindex="-1" aria-labelledby="infoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="infoModalLabel">
                    <i class="bi bi-info-circle me-2"></i>
                    Informações do RPA
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="rpaInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Confirmar Exclusão
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir este RPA?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn-modern" onclick="deleteRpa()" style="background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3a3785 100%); color: white;">
                    <i class="bi bi-trash me-2"></i>
                    <span>Excluir</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de logs -->
<div class="modal fade" id="logsModal" tabindex="-1" aria-labelledby="logsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logsModalLabel">
                    <i class="bi bi-terminal me-2"></i>
                    Logs do RPA
                </h5>
                <div class="d-flex gap-2 align-items-center">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearLogs()" style="border-radius: 8px;">
                        <i class="bi bi-trash me-1"></i> Limpar
                    </button>
                    <div class="form-check form-switch mb-0">
                        <input class="form-check-input" type="checkbox" id="autoScrollSwitch" checked>
                        <label class="form-check-label" for="autoScrollSwitch">Auto-scroll</label>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div id="logsContent" class="logs-container">
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-arrow-repeat spin"></i> Conectando ao servidor de logs...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Cores da Identidade Visual */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
        --sicoob-verde-claro-rgb: 201, 210, 0;
        --sicoob-verde-escuro-rgb: 125, 182, 28;
    }

    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* Header Moderno */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(var(--sicoob-verde-claro-rgb), 0.05) 0%, rgba(var(--sicoob-verde-escuro-rgb), 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(var(--sicoob-verde-claro-rgb), 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    /* Botão Moderno */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    /* Espaçamento entre fileiras de cards */
    .rpa-card-column {
        margin-bottom: 32px;
    }

    /* Cards Modernos */
    .card {
        position: relative;
        border: none;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        margin-bottom: 0;
        overflow: hidden;
        background: white;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--sicoob-verde-claro) 0%, var(--sicoob-azul) 100%);
    }

    .card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    .card-body {
        padding: 24px;
        position: relative;
    }

    /* Área de Controles do Card */
    .card-header-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px 8px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background: rgba(248, 249, 250, 0.5);
        margin: -24px -24px 16px -24px;
    }

    .order-controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .order-number {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
        border-radius: 8px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: 700;
        box-shadow: 0 2px 6px rgba(0, 174, 157, 0.25);
    }

    .order-buttons {
        display: flex;
        gap: 2px;
    }

    .btn-order {
        padding: 3px 5px;
        font-size: 9px;
        line-height: 1;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-order:hover {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
        transform: translateY(-1px);
    }

    .btn-order:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        transform: none;
    }

    .btn-order:disabled:hover {
        background: white;
        border-color: #e9ecef;
        color: #6c757d;
        transform: none;
    }

    /* Botões de Ação do Cabeçalho */
    .header-actions {
        display: flex;
        gap: 4px;
    }

    .btn-header-action {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }

    .btn-header-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .btn-header-action.btn-outline-primary {
        background: rgba(0, 174, 157, 0.08);
        border-color: rgba(0, 174, 157, 0.2);
        color: var(--sicoob-azul);
    }

    .btn-header-action.btn-outline-primary:hover {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
    }

    .btn-header-action.btn-outline-secondary {
        background: rgba(142, 154, 175, 0.08);
        border-color: rgba(142, 154, 175, 0.2);
        color: var(--sicoob-cinza);
    }

    .btn-header-action.btn-outline-secondary:hover {
        background: var(--sicoob-cinza);
        border-color: var(--sicoob-cinza);
        color: white;
    }

    .btn-header-action.btn-outline-info {
        background: rgba(125, 182, 28, 0.08);
        border-color: rgba(125, 182, 28, 0.2);
        color: var(--sicoob-verde-escuro);
    }

    .btn-header-action.btn-outline-info:hover {
        background: var(--sicoob-verde-escuro);
        border-color: var(--sicoob-verde-escuro);
        color: white;
    }

    .card.dragging {
        transform: scale(1.02);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        opacity: 0.8;
        cursor: grabbing !important;
        z-index: 1000;
    }

    .drag-handle {
        cursor: grab;
        padding: 0.5rem;
        color: #6c757d;
        opacity: 0.5;
        transition: opacity 0.2s;
        display: flex;
        align-items: center;
        -webkit-user-select: none;
        user-select: none;
    }

    .drag-handle:hover {
        opacity: 1;
        background-color: rgba(0,0,0,0.05);
    }

    .drag-placeholder {
        border: 2px dashed #ccc;
        background-color: #f8f9fa;
        margin-bottom: 1rem;
        border-radius: 0.25rem;
        height: 200px;
    }

    .rpa-card-container {
        transition: transform 0.2s;
    }

    .rpa-card-container.dragging {
        opacity: 0.5;
    }

    /* Status Indicators Modernos */
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 10px;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
        position: relative;
    }

    .status-indicator::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        border: 2px solid transparent;
        animation: pulse 2s infinite;
    }

    .status-running {
        background-color: var(--sicoob-verde-escuro);
    }
    .status-running::after {
        border-color: var(--sicoob-verde-escuro);
    }

    .status-stopped {
        background-color: #dc3545;
    }
    .status-stopped::after {
        border-color: #dc3545;
    }

    .status-error {
        background-color: var(--sicoob-roxo);
    }
    .status-error::after {
        border-color: var(--sicoob-roxo);
    }

    .status-starting {
        background-color: var(--sicoob-azul);
    }
    .status-starting::after {
        border-color: var(--sicoob-azul);
    }

    .status-stopping {
        background-color: #ffc107;
    }
    .status-stopping::after {
        border-color: #ffc107;
    }

    .status-unknown {
        background-color: var(--sicoob-cinza);
    }
    .status-unknown::after {
        border-color: var(--sicoob-cinza);
    }

    .status-auto_restarting {
        background-color: #ffc107;
    }
    .status-auto_restarting::after {
        border-color: #ffc107;
    }

    .status-restarted {
        background-color: var(--sicoob-azul);
    }
    .status-restarted::after {
        border-color: var(--sicoob-azul);
    }

    .status-cleaning {
        background-color: var(--sicoob-azul);
    }
    .status-cleaning::after {
        border-color: var(--sicoob-azul);
    }

    @keyframes pulse {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.5;
            transform: scale(1.2);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Título do Card Moderno */
    .card-title {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 18px;
        font-weight: 700;
        color: #2c3e50;
    }

    .process-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--sicoob-cinza);
        margin-bottom: 8px;
    }

    /* Informações do Card */
    .card-info {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin-bottom: 16px;
    }

    .card-info .small {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #6c757d;
        font-weight: 500;
    }

    .card-info .small i {
        color: var(--sicoob-azul);
        font-size: 14px;
    }

    /* Badge Moderno */
    .badge {
        border-radius: 8px;
        padding: 6px 12px;
        font-size: 11px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .bg-info {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%) !important;
    }
    
    /* Botões de Ação Modernos */
    .btn-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-icon:hover::before {
        opacity: 1;
    }

    .btn-icon:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-outline-primary {
        background: rgba(var(--sicoob-azul), 0.1);
        border-color: rgba(0, 174, 157, 0.3);
        color: var(--sicoob-azul);
    }

    .btn-outline-primary:hover {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
    }

    .btn-outline-secondary {
        background: rgba(142, 154, 175, 0.1);
        border-color: rgba(142, 154, 175, 0.3);
        color: var(--sicoob-cinza);
    }

    .btn-outline-secondary:hover {
        background: var(--sicoob-cinza);
        border-color: var(--sicoob-cinza);
        color: white;
    }

    .btn-outline-info {
        background: rgba(125, 182, 28, 0.1);
        border-color: rgba(125, 182, 28, 0.3);
        color: var(--sicoob-verde-escuro);
    }

    .btn-outline-info:hover {
        background: var(--sicoob-verde-escuro);
        border-color: var(--sicoob-verde-escuro);
        color: white;
    }

    .btn-outline-success {
        background: rgba(125, 182, 28, 0.1);
        border-color: rgba(125, 182, 28, 0.3);
        color: var(--sicoob-verde-escuro);
    }

    .btn-outline-success:hover {
        background: var(--sicoob-verde-escuro);
        border-color: var(--sicoob-verde-escuro);
        color: white;
    }

    /* Botão Remover com cor Sicoob Roxo */
    .btn-outline-danger {
        background: rgba(73, 71, 157, 0.1);
        border-color: rgba(73, 71, 157, 0.3);
        color: var(--sicoob-roxo);
    }

    .btn-outline-danger:hover {
        background: var(--sicoob-roxo);
        border-color: var(--sicoob-roxo);
        color: white;
    }

    /* Botão Stop com cor Vermelha */
    .btn-outline-stop {
        background: rgba(220, 53, 69, 0.1);
        border-color: rgba(220, 53, 69, 0.3);
        color: #dc3545;
    }

    .btn-outline-stop:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    /* Loading States */
    .btn-loading {
        position: relative;
    }

    .btn-loading .spinner-border {
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
        vertical-align: middle;
    }

    .btn-loading .btn-icon-content {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Toast Container - Padrão Sicoob */
    .toast-container-sicoob {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1055;
        max-width: 400px;
    }

    /* Toast Sicoob */
    .toast-sicoob {
        background: white;
        border: none;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 54, 65, 0.15);
        margin-bottom: 1rem;
        overflow: hidden;
        min-width: 350px;
    }

    .toast-sicoob .toast-header {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, var(--sicoob-turquesa) 100%);
        color: white;
        border: none;
        padding: 1rem 1.25rem;
        font-weight: 600;
    }

    .toast-sicoob .toast-body {
        padding: 1.25rem;
        color: var(--sicoob-verde-escuro);
        font-weight: 500;
        line-height: 1.5;
    }

    .toast-sicoob .btn-close {
        filter: brightness(0) invert(1);
        opacity: 0.8;
    }

    .toast-sicoob .btn-close:hover {
        opacity: 1;
    }

    /* Toast de Erro */
    .toast-sicoob.toast-error .toast-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .toast-sicoob.toast-error .toast-body {
        color: #721c24;
    }

    /* Toast de Sucesso */
    .toast-sicoob.toast-success .toast-header {
        background: linear-gradient(135deg, var(--sicoob-verde-medio) 0%, #6ba91a 100%);
    }

    .toast-sicoob.toast-success .toast-body {
        color: #155724;
    }

    /* Toast de Aviso */
    .toast-sicoob.toast-warning .toast-header {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .toast-sicoob.toast-warning .toast-body {
        color: #856404;
    }

    /* Toast de Info */
    .toast-sicoob.toast-info .toast-header {
        background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3d3b8a 100%);
    }

    .toast-sicoob.toast-info .toast-body {
        color: #0c5460;
    }

    /* Ícones dos Toasts */
    .toast-icon {
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    /* Modais - Padrão Sicoob */
    .modal-content {
        border: none;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 54, 65, 0.15);
        overflow: hidden;
    }

    .modal-header {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, var(--sicoob-turquesa) 100%);
        color: white;
        border: none;
        padding: 1.5rem 2rem;
        position: relative;
    }

    .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .modal-title {
        font-size: 1.25rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .modal-header .btn-close {
        filter: brightness(0) invert(1);
        opacity: 0.8;
        font-size: 1.1rem;
        margin: 0;
    }

    .modal-header .btn-close:hover {
        opacity: 1;
        transform: scale(1.1);
    }

    .modal-body {
        padding: 2rem;
        background: white;
        color: var(--sicoob-verde-escuro);
    }

    .modal-footer {
        background: rgba(0, 174, 157, 0.02);
        border: none;
        padding: 1.5rem 2rem;
        gap: 1rem;
    }

    /* Formulários nos Modais */
    .modal .form-label {
        font-weight: 600;
        color: var(--sicoob-verde-escuro);
        margin-bottom: 0.5rem;
    }

    .modal .form-control {
        border: 2px solid rgba(0, 174, 157, 0.2);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .modal .form-control:focus {
        border-color: var(--sicoob-turquesa);
        box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.15);
    }

    .modal .form-check-input {
        border: 2px solid rgba(0, 174, 157, 0.3);
        border-radius: 4px;
    }

    .modal .form-check-input:checked {
        background-color: var(--sicoob-turquesa);
        border-color: var(--sicoob-turquesa);
    }

    .modal .form-check-label {
        font-weight: 500;
        color: var(--sicoob-verde-escuro);
    }

    /* Modal de Informações */
    #infoModal .modal-header {
        background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3d3b8a 100%);
    }

    #infoModal .modal-body {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    #infoModal .table {
        margin-bottom: 0;
    }

    #infoModal .table th {
        background: rgba(0, 174, 157, 0.05);
        color: var(--sicoob-verde-escuro);
        font-weight: 600;
        border: none;
        padding: 1rem;
    }

    #infoModal .table td {
        border: none;
        padding: 1rem;
        color: var(--sicoob-verde-escuro);
    }

    #infoModal .alert {
        border: none;
        border-radius: 8px;
        font-weight: 500;
    }

    /* Modal de Exclusão */
    #deleteModal .modal-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    #deleteModal .modal-body {
        text-align: center;
        padding: 2.5rem 2rem;
    }

    #deleteModal .modal-body p {
        font-size: 1.1rem;
        font-weight: 500;
        color: var(--sicoob-verde-escuro);
        margin-bottom: 0;
    }

    /* Modal de Logs */
    #logsModal .modal-header {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #2c5f6b 100%);
    }

    #logsModal .modal-body {
        padding: 0;
        background: #1e1e1e;
    }

    .logs-container {
        background: #1e1e1e;
        color: #d4d4d4;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        line-height: 1.4;
        padding: 1rem;
        max-height: 400px;
        overflow-y: auto;
    }

    /* Botões nos Modais */
    .modal .btn-secondary {
        background: rgba(0, 54, 65, 0.1);
        border: 2px solid rgba(0, 54, 65, 0.2);
        color: var(--sicoob-verde-escuro);
        font-weight: 600;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }

    .modal .btn-secondary:hover {
        background: rgba(0, 54, 65, 0.15);
        border-color: rgba(0, 54, 65, 0.3);
        color: var(--sicoob-verde-escuro);
        transform: translateY(-1px);
    }

    /* Responsividade dos Modais */
    @media (max-width: 768px) {
        .modal-header {
            padding: 1.25rem 1.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.25rem 1.5rem;
        }

        .modal-title {
            font-size: 1.1rem;
        }
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .btn-modern {
            padding: 10px 20px;
            font-size: 13px;
        }

        .card-body {
            padding: 20px;
        }

        .order-controls {
            top: 12px;
            right: 12px;
        }
    }

    .logs-container {
        font-family: monospace;
        background-color: #1e1e1e;
        color: #d4d4d4;
        padding: 1rem;
        border-radius: 0.25rem;
        height: 500px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .logs-container::-webkit-scrollbar {
        width: 8px;
    }

    .logs-container::-webkit-scrollbar-track {
        background: #2d2d2d;
    }

    .logs-container::-webkit-scrollbar-thumb {
        background: #666;
        border-radius: 4px;
    }

    .logs-container::-webkit-scrollbar-thumb:hover {
        background: #888;
    }

    .log-line {
        padding: 2px 0;
        border-bottom: 1px solid #333;
    }

    .log-line:last-child {
        border-bottom: none;
    }

    .log-timestamp {
        color: #569cd6;
    }

    .log-level-ERROR { color: #f14c4c; }
    .log-level-WARNING { color: #cca700; }
    .log-level-INFO { color: #3794ff; }
    .log-level-DEBUG { color: #6a9955; }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .spin {
        animation: spin 1s linear infinite;
        display: inline-block;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let currentRpaId = null;
    let loadingStates = {};
    let lastOrder = null;
    let activeLogSocket = null;
    let currentRpaLogs = null;
    let connectionTimeout = null;

    function updateRpasDisplay(rpasData) {
        const grid = document.getElementById('rpasGrid');
        grid.innerHTML = '';

        rpasData.forEach((rpa, index) => {
            const col = document.createElement('div');
            col.className = 'col-12 col-md-6 col-lg-4 rpa-card-column';
            col.setAttribute('data-rpa-id', rpa.id);
            
            const status = rpa.status || { status: 'unknown', error: 'Status desconhecido' };
            const isRunning = status.status === 'running' || status.status === 'restarted';
            const isIntermediate = status.status === 'auto_restarting';
            const isError = status.status === 'error';
            const loading = loadingStates[rpa.id] || false;
            const buttonDisabled = loading || isIntermediate || isError;
            
            col.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <!-- Cabeçalho do Card com Controles -->
                        <div class="card-header-controls">
                            <div class="order-controls">
                                <div class="order-number">#${index + 1}</div>
                                <div class="order-buttons">
                                    <button type="button" class="btn btn-order"
                                        onclick="moveRpa('${rpa.id}', 'up')"
                                        ${index === 0 ? 'disabled' : ''}
                                        title="Mover para cima">
                                        <i class="bi bi-arrow-up"></i>
                                    </button>
                                    <button type="button" class="btn btn-order"
                                        onclick="moveRpa('${rpa.id}', 'down')"
                                        ${index === rpasData.length - 1 ? 'disabled' : ''}
                                        title="Mover para baixo">
                                        <i class="bi bi-arrow-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="header-actions">
                                <button type="button" class="btn btn-header-action btn-outline-primary" onclick="showInfoModal('${rpa.id}', ${JSON.stringify(status).replace(/"/g, '&quot;')})" title="Informações">
                                    <i class="bi bi-info-circle"></i>
                                </button>
                                <button type="button" class="btn btn-header-action btn-outline-secondary" onclick="showEditRpaModal('${rpa.id}', ${JSON.stringify(rpa).replace(/"/g, '&quot;')})" title="Editar">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-header-action btn-outline-info" onclick="showLogsModal('${rpa.id}', '${escapeHtml(rpa.name)}', '${escapeHtml(rpa.host)}')" title="Logs">
                                    <i class="bi bi-terminal"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Conteúdo Principal do Card -->
                        <!-- Conteúdo Principal do Card -->
                        <div class="mb-3">
                            <h5 class="card-title">
                                <span class="status-indicator status-${status.status}"></span>
                                ${escapeHtml(rpa.name)}
                            </h5>
                            <div class="process-name">${escapeHtml(rpa.process_name)}</div>
                            <div class="card-info">
                                <div class="small">
                                    <i class="bi bi-hdd-network"></i>
                                    <span>${escapeHtml(rpa.host)}:${rpa.port}</span>
                                </div>
                                <div class="small">
                                    <i class="bi bi-pc-display"></i>
                                    <span>${status.machine_info ? status.machine_info.hostname : 'N/A'}</span>
                                </div>
                            </div>
                            ${rpa.auto_restart ?
                                '<div class="mt-2"><span class="badge bg-info"><i class="bi bi-arrow-repeat"></i> Reinício Automático</span></div>'
                                : ''}
                        </div>
                        
                        <!-- Rodapé do Card com Status e Ações -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="mb-1">
                                    <strong>Status:</strong> ${getStatusText(status.status)}
                                    ${isIntermediate ? '<span class="spinner-border spinner-border-sm ms-2"></span>' : ''}
                                </div>
                                ${status.uptime ? `<div class="uptime">Uptime: ${formatUptime(status.uptime)}</div>` : ''}
                                ${status.error ? `
                                    <div class="alert alert-danger py-2 px-3 mt-2 mb-0">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        ${status.error}
                                    </div>` : ''}
                            </div>
                            <div class="btn-group">
                                <button type="button"
                                    class="btn btn-icon ${isRunning ? 'btn-outline-stop' : 'btn-outline-success'} ${buttonDisabled ? 'btn-loading' : ''}"
                                    onclick="${isRunning ? 'stopRpa' : 'startRpa'}('${rpa.id}')"
                                    title="${isRunning ? 'Parar' : 'Iniciar'}"
                                    ${buttonDisabled ? 'disabled' : ''}>
                                    <div class="btn-icon-content">
                                        ${buttonDisabled ? '<div class="spinner-border spinner-border-sm" role="status"></div>' :
                                        `<i class="bi bi-${isRunning ? 'stop-fill' : 'play-fill'}"></i>`}
                                    </div>
                                </button>
                                <button type="button" class="btn btn-icon btn-outline-danger" 
                                    onclick="showDeleteModal('${rpa.id}')"
                                    ${buttonDisabled ? 'disabled' : ''}>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            grid.appendChild(col);
        });

        // Atualiza a última ordem conhecida
        lastOrder = rpasData.map(rpa => rpa.id);
    }

    function moveRpa(rpaId, direction) {
        const grid = document.getElementById('rpasGrid');
        const cards = Array.from(grid.children);
        const currentIndex = cards.findIndex(card => card.getAttribute('data-rpa-id') === rpaId);
        
        if (currentIndex === -1) return;
        
        let newIndex;
        if (direction === 'up' && currentIndex > 0) {
            newIndex = currentIndex - 1;
        } else if (direction === 'down' && currentIndex < cards.length - 1) {
            newIndex = currentIndex + 1;
        } else {
            return;
        }

        // Cria nova ordem
        const newOrder = [...lastOrder];
        [newOrder[currentIndex], newOrder[newIndex]] = [newOrder[newIndex], newOrder[currentIndex]];

        // Salva nova ordem
        const toast = showToast('Salvando ordem...', 'info');
        
        fetch('/api/rpas/order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ order: newOrder })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao salvar ordem');
            }
            return response.json();
        })
        .then(() => {
            updateToast(toast, 'Ordem salva com sucesso!', 'success');
            lastOrder = newOrder;
            updateStatus(); // Atualiza a exibição
        })
        .catch(error => {
            console.error('Erro ao salvar ordem:', error);
            updateToast(toast, 'Erro ao salvar ordem', 'danger');
            updateStatus();
        });
    }

    // Função para mostrar toast Sicoob
    function showToastSicoob(title, message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toastContainerSicoob') || createToastContainerSicoob();

        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast toast-sicoob toast-${type}`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const iconMap = {
            'error': 'bi-exclamation-triangle-fill',
            'success': 'bi-check-circle-fill',
            'warning': 'bi-exclamation-circle-fill',
            'info': 'bi-info-circle-fill'
        };

        const titleMap = {
            'error': title || 'Erro',
            'success': title || 'Sucesso',
            'warning': title || 'Atenção',
            'info': title || 'Informação'
        };

        toast.innerHTML = `
            <div class="toast-header">
                <i class="bi ${iconMap[type]} toast-icon"></i>
                <strong class="me-auto">${titleMap[type]}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        toastContainer.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast, {
            autohide: duration > 0,
            delay: duration
        });

        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });

        return toast;
    }

    // Função auxiliar para mostrar toast (compatibilidade)
    function showToast(message, type = 'info') {
        return showToastSicoob(null, message, type, 3000);
    }

    // Função para atualizar mensagem do toast
    function updateToast(toast, message, type) {
        if (toast && toast.querySelector) {
            const bodyElement = toast.querySelector('.toast-body');
            if (bodyElement) {
                bodyElement.innerHTML = message;
            }

            // Atualiza a classe do toast
            toast.className = `toast toast-sicoob toast-${type} show`;
        }
    }

    // Função para criar container de toasts Sicoob
    function createToastContainerSicoob() {
        const container = document.createElement('div');
        container.id = 'toastContainerSicoob';
        container.className = 'toast-container-sicoob';
        document.body.appendChild(container);
        return container;
    }

    // Função para criar container de toasts (compatibilidade)
    function createToastContainer() {
        return createToastContainerSicoob();
    }

    async function startRpa(rpaId) {
        await controlRpa(rpaId, 'start');
    }

    async function stopRpa(rpaId) {
        await controlRpa(rpaId, 'stop');
    }

    async function controlRpa(rpaId, action) {
        if (loadingStates[rpaId]) return;
        
        loadingStates[rpaId] = true;
        updateLoadingState(rpaId);
        
        try {
            const response = await fetch(`/api/rpas/${rpaId}/${action}`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || `Erro ao ${action === 'start' ? 'iniciar' : 'parar'} RPA`);
            }
            
            await updateStatus();

            // Toast de sucesso
            showToastSicoob(
                'Operação Realizada',
                `RPA ${action === 'start' ? 'iniciado' : 'parado'} com sucesso!`,
                'success',
                4000
            );
        } catch (error) {
            console.error(`Erro ao ${action} RPA:`, error);
            showToastSicoob(
                'Erro na Operação',
                `Erro ao ${action === 'start' ? 'iniciar' : 'parar'} RPA: ${error.message}`,
                'error',
                7000
            );
        } finally {
            loadingStates[rpaId] = false;
            updateLoadingState(rpaId);
        }
    }

    function updateLoadingState(rpaId) {
        const button = document.querySelector(`[data-rpa-id="${rpaId}"] .btn-icon-content`);
        if (!button) return;
        
        const loading = loadingStates[rpaId];
        const isRunning = document.querySelector(`[data-rpa-id="${rpaId}"]`).querySelector('.status-indicator').classList.contains('status-running');
        
        button.innerHTML = loading ? 
            '<div class="spinner-border spinner-border-sm" role="status"></div>' :
            `<i class="bi bi-${isRunning ? 'stop-fill' : 'play-fill'}"></i>`;
    }

    function showDeleteModal(rpaId) {
        currentRpaId = rpaId;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    async function toggleAutoRestart(rpaId, enabled) {
        try {
            const response = await fetch(`/api/rpas/${rpaId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    auto_restart: enabled
                })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao atualizar configuração de auto restart');
            }

            await updateStatus();

            // Toast de sucesso
            showToastSicoob(
                'Configuração Atualizada',
                `Auto restart ${enabled ? 'ativado' : 'desativado'} com sucesso!`,
                'success',
                4000
            );
        } catch (error) {
            console.error('Erro ao atualizar auto restart:', error);
            showToastSicoob(
                'Erro de Configuração',
                'Erro ao atualizar configuração de auto restart: ' + error.message,
                'error',
                7000
            );
        }
    }

    function showInfoModal(rpaId, status) {
        const modal = document.getElementById('infoModal');
        const rpaInfo = document.getElementById('rpaInfo');
        
        const formatMemory = (bytes) => {
            if (!bytes) return 'N/A';
            const mb = bytes / (1024 * 1024);
            return mb.toFixed(2) + ' MB';
        };
        
        const formatCPU = (cpu) => {
            if (!cpu && cpu !== 0) return 'N/A';
            return cpu.toFixed(1) + '%';
        };
        
        const formatThreads = (threads) => {
            if (!threads && threads !== 0) return 'N/A';
            return threads.toString();
        };
        
        const formatPID = (pid) => {
            if (!pid && pid !== 0) return 'N/A';
            return pid.toString();
        };
        
        const formatRestartCount = (count) => {
            if (!count && count !== 0) return '0';
            return count.toString();
        };
        
        rpaInfo.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Informações do Processo</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>PID:</strong></td>
                            <td>${formatPID(status.pid)}</td>
                        </tr>
                        <tr>
                            <td><strong>Memória:</strong></td>
                            <td>${formatMemory(status.memory_usage)}</td>
                        </tr>
                        <tr>
                            <td><strong>CPU:</strong></td>
                            <td>${formatCPU(status.cpu_usage)}</td>
                        </tr>
                        <tr>
                            <td><strong>Threads:</strong></td>
                            <td>${formatThreads(status.threads)}</td>
                        </tr>
                        <tr>
                            <td><strong>Reinícios:</strong></td>
                            <td>${formatRestartCount(status.restart_count)}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>${getStatusText(status.status)}</td>
                        </tr>
                        <tr>
                            <td><strong>Última Atualização:</strong></td>
                            <td>${formatDateTime(status.last_check)}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Status da Execução</h6>
                    ${status.error ? `
                        <div class="alert alert-danger">
                            <strong>Erro:</strong> ${formatErrorMessage(status.error)}
                        </div>
                    ` : ''}
                    ${status.uptime ? `
                        <p><strong>Tempo em Execução:</strong> ${formatUptime(status.uptime)}</p>
                    ` : ''}
                </div>
            </div>
        `;
        
        const bsModal = bootstrap.Modal.getInstance(modal) || new bootstrap.Modal(modal);
        bsModal.show();
    }

    function showEditRpaModal(rpaId, rpa) {
        currentRpaId = rpaId;
        document.getElementById('rpaName').value = rpa.name;
        document.getElementById('processName').value = rpa.process_name;
        document.getElementById('rpaHost').value = rpa.host;
        document.getElementById('rpaPort').value = rpa.port;
        document.getElementById('autoRestart').checked = rpa.auto_restart;

        // Atualiza o título do modal
        document.querySelector('#rpaModal .modal-title').innerHTML = '<i class="bi bi-pencil me-2"></i>Editar RPA';

        const modal = new bootstrap.Modal(document.getElementById('rpaModal'));
        modal.show();
    }

    async function saveRpa() {
        const formData = {
            name: document.getElementById('rpaName').value,
            process_name: document.getElementById('processName').value,
            host: document.getElementById('rpaHost').value,
            port: parseInt(document.getElementById('rpaPort').value),
            auto_restart: document.getElementById('autoRestart').checked
        };

        try {
            const url = currentRpaId ? `/api/rpas/${currentRpaId}` : '/api/rpas';
            const method = currentRpaId ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao salvar RPA');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('rpaModal'));
            modal.hide();
            updateStatus();

            // Toast de sucesso
            showToastSicoob(
                'RPA Salvo',
                `RPA ${currentRpaId ? 'atualizado' : 'criado'} com sucesso!`,
                'success',
                4000
            );
        } catch (error) {
            console.error('Erro ao salvar RPA:', error);
            showToastSicoob(
                'Erro ao Salvar',
                'Erro ao salvar RPA: ' + error.message,
                'error',
                7000
            );
        }
    }

    async function deleteRpa() {
        if (!currentRpaId) return;
        
        try {
            const response = await fetch(`/api/rpas/${currentRpaId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Erro ao excluir RPA');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();
            updateStatus();

            // Toast de sucesso
            showToastSicoob(
                'RPA Excluído',
                'RPA excluído com sucesso!',
                'success',
                4000
            );
        } catch (error) {
            console.error('Erro ao excluir RPA:', error);
            showToastSicoob(
                'Erro ao Excluir',
                'Erro ao excluir RPA: ' + error.message,
                'error',
                7000
            );
        }
    }

    function showAddRpaModal() {
        currentRpaId = null;
        document.getElementById('rpaForm').reset();
        document.querySelector('#rpaModal .modal-title').innerHTML = '<i class="bi bi-robot me-2"></i>Adicionar RPA';
        const modal = new bootstrap.Modal(document.getElementById('rpaModal'));
        modal.show();
    }

    function formatMemoryUsage(memory) {
        if (!memory) return 'N/A';
        const mb = memory / (1024 * 1024);
        return mb.toFixed(1) + ' MB';
    }

    function formatCpuUsage(cpu) {
        if (!cpu) return 'N/A';
        return cpu.toFixed(1) + '%';
    }

    function formatDateTime(isoString) {
        if (!isoString) return '-';
        const date = new Date(isoString);
        return date.toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    function formatUptime(seconds) {
        if (!seconds) return '0s';
        
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        if (days > 0) {
            return `${days}d${hours}h${minutes}m`;
        }
        if (hours > 0) {
            return `${hours}h${minutes}m${remainingSeconds}s`;
        }
        if (minutes > 0) {
            return `${minutes}m${remainingSeconds}s`;
        }
        return `${remainingSeconds}s`;
    }

    function formatDuration(duration) {
        if (!duration) return 'N/A';
        
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor((duration % 3600) / 60);
        const seconds = Math.floor(duration % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        }
        if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        }
        return `${seconds}s`;
    }

    function getExecutionStatusText(status) {
        const statusMap = {
            'success': '<span class="text-success">Sucesso</span>',
            'error': '<span class="text-danger">Erro</span>',
            'interrupted': '<span class="text-warning">Interrompido</span>',
            'running': '<span class="text-primary">Em Execução</span>'
        };
        return statusMap[status] || status;
    }

    function getLogLevelClass(level) {
        const levelMap = {
            'ERROR': 'danger',
            'WARNING': 'warning',
            'INFO': 'info',
            'DEBUG': 'secondary'
        };
        return levelMap[level] || 'secondary';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatErrorMessage(error) {
        if (typeof error === 'string') {
            if (error.includes('Connection') || error.includes('connect')) {
                return 'Servidor RPA não está respondendo. Verifique se o serviço está em execução na máquina virtual.';
            }
            if (error.includes('timeout')) {
                return 'Tempo limite de conexão excedido. Verifique se o serviço está respondendo.';
            }
            return error;
        }
        return 'Erro desconhecido ao conectar com o servidor.';
    }

    function getStatusText(status) {
        if (status === 'running' || status === 'restarted') return 'Em Execução';
        if (status === 'stopped') return 'Parado';
        if (status === 'error' || status === 'auto_restarting') return 'Erro';
        if (status === 'unknown') return 'Erro';
        return 'Desconhecido';
    }

    function getNotificationMessage(status, error) {
        switch (status) {
            case 'error':
                return error || 'RPA parou de forma inesperada';  
            case 'auto_restarting':
                return 'RPA reiniciando automaticamente';  
            case 'restarted':
                return 'RPA reiniciado com sucesso';  
            default:
                return null;  
        }
    }

    function getNotificationClass(status, error) {
        if (status === 'error') return 'danger';      // Vermelho
        if (status === 'auto_restarting') return 'warning';  // Amarelo
        if (status === 'restarted') return 'info';    // Azul
        return null;
    }

    function getNotificationIcon(status) {
        if (status === 'error') return 'exclamation-triangle-fill';
        if (status === 'auto_restarting') return 'arrow-repeat';
        if (status === 'restarted') return 'check-circle-fill';
        return 'info-circle-fill';
    }

    function getStatusClass(status) {
        const classMap = {
            'running': 'success',      
            'stopped': 'danger',       
            'starting': 'info',        
            'stopping': 'warning',     
            'error': 'danger',         
            'auto_restarting': 'warning',  
            'restarted': 'info',       
            'restarting': 'info',      
            'cleaning': 'info',        
            'unknown': 'secondary'     
        };
        return classMap[status] || 'secondary';
    }

    function isStatusIntermediate(status) {
        return ['cleaning', 'restarting', 'starting', 'stopping', 'auto_restarting'].includes(status);
    }

    async function updateStatus() {
        try {
            const [rpasResponse, statusResponse, orderResponse] = await Promise.all([
                fetch('/api/rpas'),
                fetch('/api/status'),
                fetch('/api/rpas/order')
            ]);

            if (!rpasResponse.ok || !statusResponse.ok) {
                throw new Error('Erro ao carregar dados dos RPAs');
            }

            const rpasData = await rpasResponse.json();
            const statusData = await statusResponse.json();
            const orderData = await orderResponse.json();

            // Cria um mapa dos RPAs para acesso rápido
            const rpasMap = new Map(rpasData.map(rpa => [rpa.id, rpa]));

            // Adiciona o status a cada RPA
            rpasData.forEach(rpa => {
                rpa.status = statusData[rpa.id] || { status: 'unknown', error: 'Status desconhecido' };
            });

            // Ordena os RPAs conforme a ordem salva
            let orderedRpas;
            if (orderData && orderData.order && orderData.order.length > 0) {
                orderedRpas = orderData.order
                    .map(id => rpasMap.get(id))
                    .filter(rpa => rpa); // Remove undefined entries
                
                // Adiciona RPAs que não estão na ordem (novos) ao final
                rpasData.forEach(rpa => {
                    if (!orderData.order.includes(rpa.id)) {
                        orderedRpas.push(rpa);
                    }
                });
            } else {
                orderedRpas = rpasData;
            }

            updateRpasDisplay(orderedRpas);
        } catch (error) {
            console.error('Erro ao atualizar status:', error);
            document.getElementById('rpasGrid').innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Erro ao carregar os RPAs. Por favor, tente novamente.
                    </div>
                </div>`;
        }
    }

    function showLogsModal(rpaId, rpaName, rpaIp) {
        currentRpaLogs = rpaId;
        document.getElementById('logsModalLabel').textContent = `Logs do RPA: ${rpaName}`;
        
        const modal = new bootstrap.Modal(document.getElementById('logsModal'));
        modal.show();
        
        // Limpa logs anteriores
        document.getElementById('logsContent').innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="bi bi-arrow-repeat spin"></i> Conectando ao servidor de logs...
            </div>
        `;
        
        // Conecta ao WebSocket para receber logs usando o IP da VM
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${rpaIp}:5002/${rpaId}`;
        connectToLogs(wsUrl, rpaIp);
    }

    function connectToLogs(wsUrl, rpaIp) {
        // Fecha conexão anterior se existir
        if (activeLogSocket) {
            console.log('Fechando conexão WebSocket anterior');
            activeLogSocket.close();
            activeLogSocket = null;
        }

        console.log(`Tentando conectar ao WebSocket na URL: ${wsUrl}`);

        const maxRetries = 3;
        let retryCount = 0;
        let retryDelay = 2000; // 2 segundos

        function connect(rpaId, rpaIp) {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${rpaIp}:5002/${rpaId}`;
            console.log(`Tentando conectar ao WebSocket: ${wsUrl}`);

            connectionTimeout = setTimeout(() => {
                console.error('Tempo limite de conexão excedido');
                activeLogSocket = null;
            }, 5000); // 5 segundos de timeout

            const ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                console.log('Conexão WebSocket estabelecida com sucesso');
                clearTimeout(connectionTimeout);
                document.getElementById('logsContent').innerHTML = '';
                retryCount = 0;
                activeLogSocket = ws;

                // Envia ping a cada 30 segundos para manter a conexão ativa
                const pingInterval = setInterval(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        console.log('Enviando ping para manter conexão');
                        try {
                            ws.send(JSON.stringify({ 
                                type: 'ping',
                                timestamp: new Date().toISOString()
                            }));
                        } catch (error) {
                            console.error('Erro ao enviar ping:', error);
                            clearInterval(pingInterval);
                        }
                    } else {
                        console.log('Conexão fechada, parando pings');
                        clearInterval(pingInterval);
                    }
                }, 30000);

                // Envia mensagem inicial
                try {
                    ws.send(JSON.stringify({
                        type: 'init',
                        rpaId: rpaId,
                        timestamp: new Date().toISOString()
                    }));
                } catch (error) {
                    console.error('Erro ao enviar mensagem inicial:', error);
                }
            };
            
            ws.onmessage = (event) => {
                try {
                    const log = JSON.parse(event.data);
                    console.log('Log recebido:', log);
                    appendLog(log);
                } catch (error) {
                    console.error('Erro ao processar mensagem:', error);
                    appendLog({
                        timestamp: new Date().toISOString(),
                        level: 'ERROR',
                        message: `Erro ao processar mensagem: ${error.message}`
                    });
                }
            };
            
            ws.onerror = (error) => {
                console.error('Erro na conexão WebSocket:', error);
                clearTimeout(connectionTimeout);
                console.error('Detalhes do erro:', error);
                const errorDetails = `
                    <div class="alert alert-danger">
                        <h5>Erro ao conectar com o servidor de logs</h5>
                        <p><strong>Detalhes da conexão:</strong></p>
                        <ul>
                            <li>URL: ${wsUrl}</li>
                            <li>Tentativa: ${retryCount + 1} de ${maxRetries}</li>
                            <li>Erro: ${error.message || 'Erro desconhecido'}</li>
                            <li>Estado: ${ws.readyState}</li>
                            <li>Data/Hora: ${new Date().toLocaleString()}</li>
                        </ul>
                        ${retryCount < maxRetries ? '<p>Tentando reconectar...</p>' : ''}
                    </div>
                `;
                if (retryCount === 0) {
                    document.getElementById('logsContent').innerHTML = errorDetails;
                }
            };
            
            ws.onclose = (event) => {
                console.log('Conexão WebSocket fechada', {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean,
                    timestamp: new Date().toISOString()
                });
                
                clearTimeout(connectionTimeout);
                activeLogSocket = null;
                
                if (currentRpaLogs === rpaId && retryCount < maxRetries) {
                    retryCount++;
                    setTimeout(() => connect(rpaId, rpaIp), retryDelay);
                } else if (retryCount >= maxRetries) {
                    document.getElementById('logsContent').innerHTML = `
                        <div class="alert alert-danger">
                            <h5>Falha na conexão</h5>
                            <p>Não foi possível estabelecer conexão com o servidor de logs após ${maxRetries} tentativas.</p>
                            <p><strong>Detalhes da última tentativa:</strong></p>
                            <ul>
                                <li>URL: ${wsUrl}</li>
                                <li>Código de fechamento: ${event.code}</li>
                                <li>Razão: ${event.reason || 'Não especificada'}</li>
                                <li>Fechamento limpo: ${event.wasClean ? 'Sim' : 'Não'}</li>
                                <li>Data/Hora: ${new Date().toLocaleString()}</li>
                            </ul>
                            <div class="mt-3">
                                <button class="btn btn-danger" onclick="connectToLogs('${wsUrl}', '${rpaIp}')">
                                    <i class="bi bi-arrow-repeat"></i> Tentar Novamente
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="window.location.reload()">
                                    <i class="bi bi-arrow-clockwise"></i> Recarregar Página
                                </button>
                            </div>
                        </div>
                    `;
                }
            };
        }

        connect(currentRpaLogs, rpaIp);
    }

    function appendLog(log) {
        const logsContent = document.getElementById('logsContent');
        const autoScroll = document.getElementById('autoScrollSwitch').checked;
        const wasScrolledToBottom = logsContent.scrollHeight - logsContent.clientHeight <= logsContent.scrollTop + 1;
        
        const logLine = document.createElement('div');
        logLine.className = 'log-line';
        
        const timestamp = new Date(log.timestamp).toLocaleTimeString();
        logLine.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span>
            <span class="log-level-${log.level}">[${log.level}]</span>
            ${escapeHtml(log.message)}
        `;
        
        logsContent.appendChild(logLine);
        
        // Mantém apenas os últimos 1000 logs para evitar sobrecarga de memória
        while (logsContent.children.length > 1000) {
            logsContent.removeChild(logsContent.firstChild);
        }
        
        // Auto-scroll se estiver ativado e já estava no final
        if (autoScroll && wasScrolledToBottom) {
            logsContent.scrollTop = logsContent.scrollHeight;
        }
    }

    function clearLogs() {
        document.getElementById('logsContent').innerHTML = '';
    }

    // Fecha a conexão WebSocket quando o modal for fechado
    document.getElementById('logsModal').addEventListener('hidden.bs.modal', () => {
        if (activeLogSocket) {
            activeLogSocket.close();
            activeLogSocket = null;
        }
        currentRpaLogs = null;
    });

    // Inicialização
    document.addEventListener('DOMContentLoaded', () => {
        updateStatus();
        setInterval(updateStatus, 5000);
    });
</script>
{% endblock %}
