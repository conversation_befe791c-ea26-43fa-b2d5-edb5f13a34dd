<?php
require_once 'config/config.php';

try {
    // Verifica se a coluna já existe
    $columns = $pdo->query("SHOW COLUMNS FROM campanha_vendas LIKE 'data_atualizacao'")->fetchAll();
    
    if (empty($columns)) {
        // Adiciona a coluna se não existir
        $pdo->exec("ALTER TABLE campanha_vendas ADD COLUMN data_atualizacao TIMESTAMP NULL AFTER data_venda");
        echo "Coluna data_atualizacao adicionada com sucesso!\n";
    } else {
        echo "A coluna data_atualizacao já existe.\n";
    }
} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage() . "\n";
} 