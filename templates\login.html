<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - RPA Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        :root {
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
            --text-light: #ffffff;
            --text-dark: #263238;
            --background-gradient: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
        }

        body {
            min-height: 100vh;
            background: var(--background-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Tahoma, Arial, sans-serif;
        }

        .login-container {
            width: 100%;
            max-width: 900px;
            margin: 2rem;
            display: flex;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .login-info {
            flex: 1;
            padding: 3rem;
            background: var(--background-gradient);
            color: var(--text-light);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .login-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("{{ url_for('static', filename='img/pattern.png') }}") center/cover;
            opacity: 0.1;
        }

        .login-form {
            flex: 1;
            padding: 3rem;
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-container img {
            max-width: 200px;
            height: auto;
        }

        .welcome-text {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            position: relative;
            color: var(--text-light);
            text-align: center;
            line-height: 1.4;
            max-width: 80%;
        }

        .description {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            text-align: center;
        }

        .form-control {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 0.8rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 0.5rem;
        }

        .btn-login {
            background: var(--sicoob-turquesa);
            border: none;
            border-radius: 10px;
            padding: 0.8rem;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            background: var(--sicoob-verde-escuro);
            transform: translateY(-2px);
        }

        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
            max-width: 500px;
        }

        .alert {
            margin-bottom: 1rem;
            border: none;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid var(--sicoob-verde-claro);
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid var(--sicoob-verde-medio);
        }

        .alert-info {
            background-color: #cce5ff;
            color: #004085;
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .alert i {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .alert .btn-close {
            margin-left: auto;
            font-size: 1.2rem;
            padding: 0.5rem;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .alert .btn-close:hover {
            opacity: 1;
        }

        .input-group-text {
            background-color: transparent;
            border-right: 0;
            color: var(--sicoob-verde-escuro);
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 1rem;
            }

            .login-info, .login-form {
                padding: 2rem;
            }

            .flash-messages {
                top: 10px;
                right: 10px;
                left: 10px;
                min-width: auto;
            }

            .logo-container img {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <!-- Flash Messages Container -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                        {% if category == 'warning' %}
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        {% elif category == 'success' %}
                            <i class="bi bi-check-circle-fill"></i>
                        {% elif category == 'error' or category == 'danger' %}
                            <i class="bi bi-x-circle-fill"></i>
                        {% elif category == 'info' %}
                            <i class="bi bi-info-circle-fill"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="login-container">
        <div class="login-info">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='img/logo1.png') }}" alt="Logo Sicoob">
            </div>
            <div class="welcome-text">Sistema de Monitoramento e Controle de Processos Automatizados</div>
            <div class="description">
                Faça login com suas credenciais de acesso ao computador para continuar.
            </div>
        </div>
        <div class="login-form">
            <form method="POST" action="{{ url_for('login') }}">
                <div class="mb-4">
                    <label for="username" class="form-label">Usuário</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">Senha</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                
                <input type="hidden" name="auth_type" value="ad">
                
                <button type="submit" class="btn btn-login w-100">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    Entrar
                </button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelectorAll('.alert').forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
</body>
</html>
