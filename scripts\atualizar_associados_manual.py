import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import logging
from datetime import datetime
import os

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_mysql_connection():
    """Estabelece conexão com o banco de dados"""
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except Error as e:
        logger.error(f"Erro ao conectar ao MySQL: {e}")
        raise

def tratar_renda(valor):
    """Converte o valor da renda para o formato correto"""
    try:
        # Converte para string e remove possíveis espaços
        valor_str = str(valor).strip()
        
        # Se o valor já estiver no formato com vírgula (ex: "2.000,00")
        if ',' in valor_str:
            # Remove o ponto de milhar e substitui a vírgula por ponto
            valor_str = valor_str.replace('.', '').replace(',', '.')
        else:
            # Se estiver no formato com ponto (ex: "2.000")
            valor_str = str(float(valor_str))
        
        return float(valor_str)
    except Exception as e:
        logger.error(f"Erro ao tratar valor da renda '{valor}': {str(e)}")
        raise

def processar_planilha(arquivo_excel):
    """Processa a planilha e atualiza o banco de dados"""
    try:
        # Ler a planilha
        logger.info(f"Lendo planilha: {arquivo_excel}")
        df = pd.read_excel(arquivo_excel)
        
        # Verificar se as colunas necessárias existem
        colunas_necessarias = ['CPF', 'RENDA', 'DATA']
        if not all(coluna in df.columns for coluna in colunas_necessarias):
            raise ValueError("A planilha deve conter as colunas: CPF, RENDA e DATA")
        
        # Limpar e formatar CPFs (remover pontos, traços e espaços)
        df['CPF'] = df['CPF'].astype(str).str.replace(r'[^\d]', '', regex=True)
        
        # Estabelecer conexão com o banco
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Contadores para o relatório
        atualizados = 0
        inseridos = 0
        erros = 0
        
        # Processar cada linha da planilha
        total_linhas = len(df)
        for index, row in df.iterrows():
            try:
                cpf = row['CPF']
                renda = tratar_renda(row['RENDA'])  # Usa a nova função para tratar a renda
                data_aprovacao = pd.to_datetime(row['DATA']).strftime('%Y-%m-%d %H:%M:%S')
                
                # Verificar se o CPF já existe
                cursor.execute("SELECT cpf FROM associados WHERE cpf = %s", (cpf,))
                existe = cursor.fetchone()
                
                if existe:
                    # Atualizar registro existente
                    cursor.execute("""
                        UPDATE associados 
                        SET renda = %s, 
                            data_aprovacao = %s, 
                            status = 3,
                            data_atualizacao = NOW()
                        WHERE cpf = %s
                    """, (renda, data_aprovacao, cpf))
                    atualizados += 1
                else:
                    # Inserir novo registro
                    cursor.execute("""
                        INSERT INTO associados 
                        (cpf, renda, data_aprovacao, status, data_atualizacao) 
                        VALUES (%s, %s, %s, 3, NOW())
                    """, (cpf, renda, data_aprovacao))
                    inseridos += 1
                
                # Log de progresso a cada 100 registros
                if (index + 1) % 100 == 0:
                    logger.info(f"Processados {index + 1} de {total_linhas} registros")
                
            except Exception as e:
                logger.error(f"Erro ao processar CPF {cpf}: {str(e)}")
                erros += 1
                continue
        
        # Commit das alterações
        conn.commit()
        
        # Relatório final
        logger.info("\n=== Relatório de Processamento ===")
        logger.info(f"Total de registros na planilha: {total_linhas}")
        logger.info(f"Registros atualizados: {atualizados}")
        logger.info(f"Registros inseridos: {inseridos}")
        logger.info(f"Registros com erro: {erros}")
        logger.info("================================")
        
    except Exception as e:
        logger.error(f"Erro durante o processamento: {str(e)}")
        if 'conn' in locals() and conn.is_connected():
            conn.rollback()
        raise
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn.is_connected():
            conn.close()
            logger.info("Conexão com o banco de dados fechada")

if __name__ == "__main__":
    try:
        # Caminho do arquivo
        arquivo_excel = r"C:\Users\<USER>\Downloads\cpfs_atualizados.xlsx"
        
        # Verificar se o arquivo existe
        if not os.path.exists(arquivo_excel):
            raise FileNotFoundError(f"Arquivo não encontrado: {arquivo_excel}")
        
        # Processar a planilha
        processar_planilha(arquivo_excel)
        
    except Exception as e:
        logger.error(f"Erro: {str(e)}")
        exit(1) 