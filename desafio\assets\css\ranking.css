/* Ranking Dashboard Styles */
:root {
    /* Primary Colors */
    --sicoob-turquoise: #00AE9D;
    --sicoob-dark-green: #003641;
    --sicoob-light-green: #C9D200;
    --sicoob-medium-green: #7DB61C;
    --sicoob-purple: #49479D;
    
    /* Neutral Colors */
    --sicoob-gray-100: #F5F5F5;
    --sicoob-gray-200: #EEEEEE;
    --sicoob-gray-300: #E0E0E0;
    --sicoob-gray-400: #BDBDBD;
    --sicoob-gray-500: #9E9E9E;
    --sicoob-gray-600: #757575;
    --sicoob-gray-700: #616161;
    --sicoob-gray-800: #424242;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
}

/* Base Styles */
body {
    font-family: 'Source Sans Pro', sans-serif;
    line-height: 1.5;
    color: var(--sicoob-gray-800);
    background: var(--sicoob-gray-100);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
header {
    background: var(--sicoob-dark-green);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
}

header .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

header h1 {
    color: white;
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.logo {
    height: 40px;
    width: auto;
}

/* Dashboard Grid */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Card Styles */
.dashboard-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: transform 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.card-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 3px solid;
}

.card-header i {
    font-size: 1.5rem;
}

.card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--sicoob-dark-green);
}

.card-content {
    padding: 1.5rem;
}

/* Tab Navigation */
.ranking-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.tab-button {
    padding: 0.75rem 1.5rem;
    border: none;
    background: var(--sicoob-gray-200);
    color: var(--sicoob-gray-700);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.tab-button:hover {
    background: var(--sicoob-gray-300);
}

.tab-button.active {
    background: var(--sicoob-turquoise);
    color: white;
}

/* Chart Styles */
.chart-container {
    margin-bottom: 1.5rem;
    height: 200px;
    position: relative;
}

/* Ranking List */
.ranking-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ranking-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--sicoob-gray-100);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: transform 0.2s ease;
}

.ranking-item:hover {
    transform: translateX(5px);
}

.position {
    font-weight: 700;
    color: var(--rank-color);
    width: 2rem;
}

.name {
    font-weight: 500;
}

.value {
    font-family: monospace;
    font-weight: 500;
    color: var(--sicoob-dark-green);
}

.quantity {
    color: var(--sicoob-gray-600);
    font-size: 0.75rem;
}

/* No Data State */
.no-data {
    text-align: center;
    color: var(--sicoob-gray-500);
    padding: 2rem;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-content.active {
    animation: slideIn 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr;
    }

    header .container {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    header h1 {
        font-size: 1.5rem;
    }

    .ranking-item {
        grid-template-columns: auto 1fr;
        grid-template-rows: auto auto;
    }

    .value, .quantity {
        grid-column: 1 / -1;
        text-align: right;
    }

    .tab-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .chart-container {
        height: 180px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
    }

    header {
        background: none;
        padding: 1rem 0;
    }

    header h1 {
        color: var(--sicoob-dark-green);
    }

    .dashboard-card {
        box-shadow: none;
        border: 1px solid var(--sicoob-gray-300);
        break-inside: avoid;
    }

    .chart-container {
        break-inside: avoid;
    }
} 