-- Cria tabela para armazenar configurações dos RPAs
CREATE TABLE IF NOT EXISTS rpa_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    process_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Nome do processo do RPA',
    display_name VARCHAR(100) NOT NULL COMMENT 'Nome de exibição do RPA',
    host VARCHAR(100) COMMENT 'Host onde o RPA roda',
    port INT COMMENT 'Porta do servidor de status',
    auto_restart BOOLEAN DEFAULT true COMMENT 'Se deve reiniciar automaticamente',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_process_name (process_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insere as configurações iniciais dos RPAs
INSERT INTO rpa_config (process_name, display_name, host, port, auto_restart) VALUES 
('Aprovar.exe', 'APROVAR', '**************', 5000, true),
('bot.exe', 'ATUALIZAÇÃO CADASTRAL', '**************', 5000, true)
ON DUPLICATE KEY UPDATE
    display_name = VALUES(display_name),
    host = VALUES(host),
    port = VALUES(port),
    auto_restart = VALUES(auto_restart); 