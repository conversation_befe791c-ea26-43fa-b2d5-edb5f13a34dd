import mysql.connector
from datetime import datetime
import logging
import json
import os
import requests
import time
import sys

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('monitor_rpas.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def get_rpas_config():
    try:
        with open('data/rpas.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Erro ao ler arquivo de configuração dos RPAs: {e}")
        return []

def get_rpa_status(host, port):
    try:
        url = f"http://{host}:{port}/status"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            status_data = response.json()
            return {
                'running': True,
                'status': status_data.get('status', 'running'),
                'pid': status_data.get('pid'),
                'memory_usage': status_data.get('memory_usage'),
                'cpu_usage': status_data.get('cpu_usage'),
                'uptime': status_data.get('uptime', 0)
            }
        return {'running': False, 'status': 'stopped'}
    except:
        return {'running': False, 'status': 'error'}

def get_current_executions():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT id, rpa_name, start_time, status, active_time, last_status_update
            FROM rpa_executions 
            WHERE status = 'running'
        """)
        
        executions = cursor.fetchall()
        cursor.close()
        conn.close()
        return executions
    except Exception as e:
        logger.error(f"Erro ao buscar execuções atuais: {e}")
        return []

def update_execution_status(execution_id, status, end_time=None):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        now = datetime.now()
        
        if status in ['stopped', 'error', 'completed']:
            cursor.execute("""
                UPDATE rpa_executions 
                SET status = %s,
                    end_time = %s,
                    last_status_update = %s
                WHERE id = %s
            """, (status, end_time or now, now, execution_id))
        else:
            cursor.execute("""
                UPDATE rpa_executions 
                SET last_status_update = %s
                WHERE id = %s
            """, (now, execution_id))
            
        # Registra no histórico
        cursor.execute("""
            INSERT INTO rpa_execution_history 
            (execution_id, status, start_time, end_time)
            VALUES (%s, %s, %s, %s)
        """, (execution_id, status, now, end_time if status != 'running' else None))
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Erro ao atualizar status da execução {execution_id}: {e}")

def register_new_execution(rpa_config, status_info):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        now = datetime.now()
        
        # Registra nova execução
        cursor.execute("""
            INSERT INTO rpa_executions 
            (rpa_name, start_time, status, active_time, last_status_update)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            rpa_config['name'],
            now,
            status_info['status'],
            0,
            now
        ))
        
        execution_id = cursor.lastrowid
        
        # Registra no histórico
        cursor.execute("""
            INSERT INTO rpa_execution_history 
            (execution_id, status, start_time)
            VALUES (%s, %s, %s)
        """, (
            execution_id,
            status_info['status'],
            now
        ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"""
Nova execução registrada:
ID: {execution_id}
RPA: {rpa_config['name']}
Host: {rpa_config['host']}
Processo: {rpa_config['process_name']}
Status: {status_info['status']}
Início: {now}
""")
        
        return execution_id
    except Exception as e:
        logger.error(f"Erro ao registrar nova execução para {rpa_config['name']}: {e}")
        return None

def monitor_rpas():
    logger.info("Iniciando monitoramento de RPAs...")
    
    while True:
        try:
            rpas_config = get_rpas_config()
            current_executions = get_current_executions()
            
            # Mapeia execuções atuais por nome do RPA
            executions_map = {exec['rpa_name']: exec for exec in current_executions}
            
            # Verifica cada RPA configurado
            for rpa in rpas_config:
                status_info = get_rpa_status(rpa['host'], rpa['port'])
                current_execution = executions_map.get(rpa['name'])
                
                if status_info['running']:
                    if not current_execution:
                        # RPA está rodando mas não tem registro - cria novo
                        register_new_execution(rpa, status_info)
                    else:
                        # Atualiza status da execução existente
                        update_execution_status(current_execution['id'], status_info['status'])
                else:
                    if current_execution:
                        # RPA parou - atualiza registro para stopped
                        update_execution_status(current_execution['id'], 'stopped')
                
                logger.info(f"""
Status do RPA {rpa['name']}:
Host: {rpa['host']}
Status: {status_info['status']}
Em execução: {'Sim' if status_info['running'] else 'Não'}
""")
            
            # Aguarda 30 segundos antes da próxima verificação
            time.sleep(30)
            
        except KeyboardInterrupt:
            logger.info("Monitoramento interrompido pelo usuário")
            break
        except Exception as e:
            logger.error(f"Erro durante o monitoramento: {e}")
            time.sleep(30)  # Aguarda antes de tentar novamente

if __name__ == "__main__":
    monitor_rpas() 