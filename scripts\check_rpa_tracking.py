from utils.db import get_mysql_connection
import time

def check_rpa_tracking(rpa_name):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, rpa_name, total_active_time, last_start_time, 
                   last_end_time, current_status, last_status_update
            FROM rpa_tracking 
            WHERE rpa_name = %s
        """, (rpa_name,))
        
        result = cursor.fetchone()
        
        print(f"\nRegistro do RPA {rpa_name}:")
        if result:
            print(f"ID: {result[0]}")
            print(f"Nome: {result[1]}")
            print(f"Tempo total ativo: {result[2]} segundos")
            print(f"Último início: {result[3]}")
            print(f"Último fim: {result[4]}")
            print(f"Status atual: {result[5]}")
            print(f"Última atualização: {result[6]}")
        else:
            print("Nenhum registro encontrado")
            
    except Exception as e:
        print(f"Erro ao verificar tracking: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    while True:
        check_rpa_tracking('APROVAR')
        time.sleep(5)  # Verifica a cada 5 segundos 