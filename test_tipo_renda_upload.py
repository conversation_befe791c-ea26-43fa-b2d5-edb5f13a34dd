import pandas as pd
import mysql.connector
import logging
from datetime import datetime

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True,
            connection_timeout=5,
            allow_local_infile=True,
            use_pure=True
        )
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def create_test_excel():
    """Cria um arquivo Excel de teste com dados de associados incluindo tipos de renda"""
    
    # Dados de teste com múltiplos tipos de renda para o mesmo CPF
    data = [
        {
            'Data Movimento': '2024-01-15',
            'Número PA': '12345',
            'CPF/CNPJ': '12345678901',
            'Nome Cliente': '<PERSON>',
            'Telefone': '11999999999',
            'Data da Última Renovação Cadastral': '2023-12-01',
            'Valor da Renda Bruta Mensal': 5000.00,
            'Código CNAE': '1234',
            'CNAE': 'Atividade Teste',
            'Tipo de Renda': 'SALÁRIO'
        },
        {
            'Data Movimento': '2024-01-15',
            'Número PA': '12345',
            'CPF/CNPJ': '12345678901',
            'Nome Cliente': 'João Silva',
            'Telefone': '11999999999',
            'Data da Última Renovação Cadastral': '2023-12-01',
            'Valor da Renda Bruta Mensal': 5000.00,
            'Código CNAE': '1234',
            'CNAE': 'Atividade Teste',
            'Tipo de Renda': 'RENDA PONDERADA'
        },
        {
            'Data Movimento': '2024-01-16',
            'Número PA': '67890',
            'CPF/CNPJ': '98765432100',
            'Nome Cliente': 'Maria Santos',
            'Telefone': '11888888888',
            'Data da Última Renovação Cadastral': '2023-11-15',
            'Valor da Renda Bruta Mensal': 3000.00,
            'Código CNAE': '5678',
            'CNAE': 'Outra Atividade',
            'Tipo de Renda': 'APOSENTADORIA'
        },
        {
            'Data Movimento': '2024-01-17',
            'Número PA': '11111',
            'CPF/CNPJ': '11111111111',
            'Nome Cliente': 'Pedro Costa',
            'Telefone': '11777777777',
            'Data da Última Renovação Cadastral': '2023-10-20',
            'Valor da Renda Bruta Mensal': 7000.00,
            'Código CNAE': '9999',
            'CNAE': 'Terceira Atividade',
            'Tipo de Renda': 'RENDA PONDERADA'
        }
    ]
    
    df = pd.DataFrame(data)
    df.to_excel('teste_associados_tipo_renda.xlsx', index=False)
    logger.info("Arquivo de teste 'teste_associados_tipo_renda.xlsx' criado com sucesso!")
    
    return df

def test_grouping_logic():
    """Testa a lógica de agrupamento por CPF"""
    
    logger.info("Testando lógica de agrupamento...")
    
    # Cria dados de teste
    df = create_test_excel()
    
    # Aplica a mesma lógica do código principal
    df_grouped = df.groupby('CPF/CNPJ').agg({
        'Data Movimento': 'max',
        'Número PA': 'first',
        'Nome Cliente': 'first',
        'Telefone': 'first',
        'Data da Última Renovação Cadastral': 'max',
        'Valor da Renda Bruta Mensal': 'first',
        'Código CNAE': 'first',
        'CNAE': 'first',
        'Tipo de Renda': lambda x: ' | '.join(sorted(set(str(val) for val in x if pd.notna(val) and str(val).strip() != '')))
    }).reset_index()
    
    logger.info("Resultado do agrupamento:")
    for _, row in df_grouped.iterrows():
        logger.info(f"CPF: {row['CPF/CNPJ']}, Nome: {row['Nome Cliente']}, Tipos de Renda: {row['Tipo de Renda']}")
        
        # Verifica se deve ter status 7
        tem_renda_ponderada = 'RENDA PONDERADA' in str(row['Tipo de Renda']).upper()
        status_esperado = 7 if tem_renda_ponderada else 1
        logger.info(f"  Status esperado: {status_esperado} ({'Renda Ponderada' if tem_renda_ponderada else 'Pendente'})")
    
    return df_grouped

def verify_database_structure():
    """Verifica se as colunas necessárias existem no banco"""
    
    logger.info("Verificando estrutura do banco de dados...")
    
    conn = get_mysql_connection()
    cursor = conn.cursor()
    
    # Verifica tabela associados
    cursor.execute("DESCRIBE associados")
    associados_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Colunas da tabela 'associados': {associados_columns}")
    
    if 'tipo_renda' not in associados_columns:
        logger.warning("Coluna 'tipo_renda' não encontrada na tabela 'associados'!")
    else:
        logger.info("✓ Coluna 'tipo_renda' encontrada na tabela 'associados'")
    
    # Verifica tabela associados_completa
    cursor.execute("DESCRIBE associados_completa")
    completa_columns = [row[0] for row in cursor.fetchall()]
    logger.info(f"Colunas da tabela 'associados_completa': {completa_columns}")
    
    if 'tipo_renda' not in completa_columns:
        logger.warning("Coluna 'tipo_renda' não encontrada na tabela 'associados_completa'!")
    else:
        logger.info("✓ Coluna 'tipo_renda' encontrada na tabela 'associados_completa'")
    
    # Verifica status
    cursor.execute("SELECT id, nome, descricao FROM status ORDER BY id")
    status_list = cursor.fetchall()
    logger.info("Status disponíveis:")
    for status in status_list:
        logger.info(f"  ID: {status[0]}, Nome: {status[1]}, Descrição: {status[2]}")
    
    status_ids = [status[0] for status in status_list]
    if 7 not in status_ids:
        logger.warning("Status 7 (renda_ponderada) não encontrado!")
    else:
        logger.info("✓ Status 7 (renda_ponderada) encontrado")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    logger.info("=== TESTE DE IMPLEMENTAÇÃO TIPO DE RENDA ===")
    
    # 1. Verifica estrutura do banco
    verify_database_structure()
    
    # 2. Testa lógica de agrupamento
    test_grouping_logic()
    
    logger.info("=== TESTE CONCLUÍDO ===")
    logger.info("Para aplicar as alterações no banco, execute:")
    logger.info("1. python add_tipo_renda_column.py")
    logger.info("2. python add_status_renda_ponderada.py")
