import json
import os

def atualizar_configuracao_rpa():
    # Configuração do RPA
    config = {
        "RPA_PROCESS_NAME": "RPA_ASSOCIADOS",
        "RPA_EXECUTABLE": r"C:\RPA\RPA_ASSOCIADOS\RPA_ASSOCIADOS.exe",
        "ASSOCIATED_PROCESSES": ["chrome.exe", "Sisbr 2.0.exe"]
    }
    
    # Lê o arquivo rpa_status_server.py
    with open('rpa_status_server.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Atualiza as configurações
    new_content = content
    
    # Atualiza RPA_PROCESS_NAME
    new_content = new_content.replace(
        'RPA_PROCESS_NAME = "Aprovar.exe"',
        f'RPA_PROCESS_NAME = "{config["RPA_PROCESS_NAME"]}"'
    )
    
    # Atualiza RPA_EXECUTABLE
    new_content = new_content.replace(
        'RPA_EXECUTABLE = r"C:\\RPA\\Aprovar\\Aprovar.exe"',
        f'RPA_EXECUTABLE = r"{config["RPA_EXECUTABLE"]}"'
    )
    
    # Atualiza ASSOCIATED_PROCESSES
    new_content = new_content.replace(
        'ASSOCIATED_PROCESSES = ["chrome.exe", "Sisbr 2.0.exe"]',
        f'ASSOCIATED_PROCESSES = {json.dumps(config["ASSOCIATED_PROCESSES"])}'
    )
    
    # Salva as alterações
    with open('rpa_status_server.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Configuração do RPA atualizada com sucesso!")
    print(f"Nome do processo: {config['RPA_PROCESS_NAME']}")
    print(f"Executável: {config['RPA_EXECUTABLE']}")
    print(f"Processos associados: {config['ASSOCIATED_PROCESSES']}")

if __name__ == "__main__":
    atualizar_configuracao_rpa() 