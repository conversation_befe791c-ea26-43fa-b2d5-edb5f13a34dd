{% extends "base.html" %}

{% block title %}Robozômetro{% if is_full_mode %} - Modo TV{% endif %}{% endblock %}

{% block content %}



<style>
    /* Variáveis CSS Sicoob */
    :root {
        --sicoob-turquesa: #00AE9D;
        --sicoob-verde-escuro: #003641;
        --sicoob-verde-medio: #7DB61C;
        --sicoob-roxo: #49479D;
        --sicoob-azul-escuro: #012970;
    }

    /* Header <PERSON><PERSON> - <PERSON> */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.05) 0%, rgba(0, 54, 65, 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(0, 174, 157, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    .bg-sicoob-turquesa {
        background-color: var(--sicoob-turquesa) !important;
    }

    .text-sicoob-verde-escuro {
        color: var(--sicoob-verde-escuro) !important;
    }

    /* Cards - Padrão Sicoob */
    .card {
        height: 100%;
        border: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 54, 65, 0.08);
        background: white;
        border-radius: 12px;
        overflow: hidden;
    }

    .card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 24px rgba(0, 54, 65, 0.15);
    }

    .card-body {
        padding: 2rem;
        position: relative;
    }

    .card-title {
        font-size: 0.95rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .display-value {
        font-size: 2.8rem;
        font-weight: 800;
        margin-bottom: 0.75rem;
        line-height: 1.1;
    }

    .small-text {
        font-size: 0.875rem;
        opacity: 0.75;
        font-weight: 500;
    }

    .card-footer {
        padding: 1.25rem 2rem;
        background: rgba(0, 54, 65, 0.02);
        border-top: 1px solid rgba(0, 54, 65, 0.08);
    }

    .card-footer .small-text {
        width: 100%;
        text-align: center;
        font-weight: 600;
    }

    /* Títulos de Seção - Padrão Sicoob */
    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin: 3rem 0 2rem;
        color: var(--sicoob-verde-escuro);
        border-left: 5px solid var(--sicoob-turquesa);
        padding-left: 1.5rem;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        letter-spacing: 1px;
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.05) 0%, rgba(0, 54, 65, 0.02) 100%);
        padding: 1rem 1.5rem;
        border-radius: 8px;
        position: relative;
    }

    .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 5px;
        background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-medio) 100%);
        border-radius: 0 4px 4px 0;
    }

    /* Ícones - Padrão Sicoob */
    .icon-circle {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5rem;
        position: relative;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .icon-circle i {
        font-size: 1.75rem;
        font-weight: 600;
    }

    /* Indicador de Atualização - Padrão Sicoob */
    .last-update {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.1) 0%, rgba(0, 54, 65, 0.05) 100%);
        color: var(--sicoob-verde-escuro);
        padding: 0.75rem 1.25rem;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border: 1px solid rgba(0, 174, 157, 0.2);
        box-shadow: 0 2px 8px rgba(0, 174, 157, 0.1);
    }

    /* Cards - Cores Sicoob */
    .card-primary {
        border-top: 5px solid var(--sicoob-roxo);
        background: linear-gradient(135deg, rgba(73, 71, 157, 0.02) 0%, white 100%);
    }

    .card-primary .icon-circle {
        background: linear-gradient(135deg, rgba(73, 71, 157, 0.15) 0%, rgba(73, 71, 157, 0.05) 100%);
        color: var(--sicoob-roxo);
    }

    .card-primary .card-title,
    .card-primary .display-value {
        color: var(--sicoob-roxo);
    }

    .card-success {
        border-top: 5px solid var(--sicoob-verde-medio);
        background: linear-gradient(135deg, rgba(125, 182, 28, 0.02) 0%, white 100%);
    }

    .card-success .icon-circle {
        background: linear-gradient(135deg, rgba(125, 182, 28, 0.15) 0%, rgba(125, 182, 28, 0.05) 100%);
        color: var(--sicoob-verde-medio);
    }

    .card-success .card-title,
    .card-success .display-value {
        color: var(--sicoob-verde-medio);
    }

    .card-disponivel {
        border-top: 5px solid var(--sicoob-turquesa);
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.02) 0%, white 100%);
    }

    .card-disponivel .icon-circle {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.15) 0%, rgba(0, 174, 157, 0.05) 100%);
        color: var(--sicoob-turquesa);
    }

    .card-disponivel .card-title,
    .card-disponivel .display-value {
        color: var(--sicoob-turquesa);
    }

    .card-uso {
        border-top: 5px solid var(--sicoob-verde-escuro);
        background: linear-gradient(135deg, rgba(0, 54, 65, 0.02) 0%, white 100%);
    }

    .card-uso .icon-circle {
        background: linear-gradient(135deg, rgba(0, 54, 65, 0.15) 0%, rgba(0, 54, 65, 0.05) 100%);
        color: var(--sicoob-verde-escuro);
    }

    .card-uso .card-title,
    .card-uso .display-value {
        color: var(--sicoob-verde-escuro);
    }

    .card-total {
        border-top: 5px solid var(--sicoob-azul-escuro);
        background: linear-gradient(135deg, rgba(1, 41, 112, 0.02) 0%, white 100%);
    }

    .card-total .icon-circle {
        background: linear-gradient(135deg, rgba(1, 41, 112, 0.15) 0%, rgba(1, 41, 112, 0.05) 100%);
        color: var(--sicoob-azul-escuro);
    }

    .card-total .card-title,
    .card-total .display-value {
        color: var(--sicoob-azul-escuro);
    }

    .card-execution {
        border-top: 5px solid var(--sicoob-roxo);
        background: linear-gradient(135deg, rgba(73, 71, 157, 0.02) 0%, white 100%);
    }

    .card-execution .icon-circle {
        background: linear-gradient(135deg, rgba(73, 71, 157, 0.15) 0%, rgba(73, 71, 157, 0.05) 100%);
        color: var(--sicoob-roxo);
    }

    .card-execution .card-title,
    .card-execution .display-value {
        color: var(--sicoob-roxo);
    }

    /* Lista de RPAs - Padrão Sicoob */
    .rpa-time-list {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 0.875rem;
    }

    .rpa-time-list li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 54, 65, 0.08);
        transition: all 0.2s ease;
    }

    .rpa-time-list li:last-child {
        border-bottom: none;
    }

    .rpa-time-list li:hover {
        background: rgba(0, 174, 157, 0.03);
        padding-left: 0.5rem;
        border-radius: 6px;
    }

    .rpa-time-list .rpa-name {
        font-weight: 600;
        font-size: 0.875rem;
    }

    .rpa-time-list .rpa-time {
        color: var(--sicoob-verde-escuro);
        font-weight: 500;
        font-family: 'Courier New', monospace;
    }

    .card-success .rpa-time-list .rpa-name {
        color: var(--sicoob-verde-medio);
        font-weight: 600;
    }

    .card-execution .rpa-time-list .rpa-name {
        color: var(--sicoob-roxo);
        font-weight: 600;
    }

    .card-primary .rpa-time-list .rpa-name {
        color: var(--sicoob-roxo);
        font-weight: 600;
    }

    /* Botão Modo TV - Padrão Sicoob */
    .full-mode-btn {
        position: absolute;
        top: 1rem;
        right: 4rem;
        z-index: 100;
        padding: 0.75rem 1.25rem;
        border-radius: 25px;
        background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-medio) 100%);
        color: white;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .full-mode-btn:hover {
        background: linear-gradient(135deg, var(--sicoob-verde-medio) 0%, var(--sicoob-turquesa) 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 174, 157, 0.4);
    }

    .full-mode-btn i {
        font-size: 1rem;
    }

    /* Botão de Captura - Mesmo estilo do badge 'Atualizado' */
    .capture-btn-elegant {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.1) 0%, rgba(0, 54, 65, 0.05) 100%);
        color: var(--sicoob-verde-escuro);
        border: 1px solid rgba(0, 174, 157, 0.2);
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0.75rem;
        border-radius: 25px;
        box-shadow: 0 2px 8px rgba(0, 174, 157, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
    }

    .capture-btn-elegant:hover {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.15) 0%, rgba(0, 54, 65, 0.08) 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.15);
        color: var(--sicoob-turquesa);
        border-color: rgba(0, 174, 157, 0.3);
    }

    .capture-btn-elegant:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        color: var(--sicoob-verde-escuro);
    }

    .capture-btn-elegant:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(0, 174, 157, 0.1);
    }

    /* Modal de Captura */
    .capture-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
    }

    .capture-modal-content {
        position: relative;
        background-color: white;
        margin: 2% auto;
        padding: 20px;
        border-radius: 16px;
        width: 90%;
        max-width: 1200px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .capture-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--sicoob-turquesa);
    }

    .capture-modal-title {
        color: var(--sicoob-verde-escuro);
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
    }

    .capture-modal-close {
        background: none;
        border: none;
        font-size: 2rem;
        color: var(--sicoob-verde-escuro);
        cursor: pointer;
        padding: 0;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .capture-modal-close:hover {
        background-color: rgba(0, 174, 157, 0.1);
        color: var(--sicoob-turquesa);
    }

    .capture-preview {
        text-align: center;
        margin-bottom: 20px;
    }

    .capture-preview img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .capture-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
    }

    .capture-download-btn {
        padding: 12px 24px;
        border-radius: 25px;
        background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-medio) 100%);
        color: white;
        border: none;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        cursor: pointer;
    }

    .capture-download-btn:hover {
        background: linear-gradient(135deg, var(--sicoob-verde-medio) 0%, var(--sicoob-turquesa) 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 174, 157, 0.4);
    }

    /* Loading overlay */
    .capture-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        backdrop-filter: blur(3px);
    }

    .capture-loading-content {
        background: white;
        padding: 30px;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .capture-loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(0, 174, 157, 0.2);
        border-left: 4px solid var(--sicoob-turquesa);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .capture-loading-text {
        color: var(--sicoob-verde-escuro);
        font-weight: 600;
        font-size: 1.1rem;
    }
</style>

{% if is_full_mode %}
<style>
    /* Ajustes para o modo TV */
    body {
        overflow: hidden;
        background: #f6f9ff !important;
        padding: 0 !important;
        margin: 0 !important;
        height: 100vh;
        width: 100vw;
    }

    #main {
        margin: 0 !important;
        padding: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        overflow: hidden !important;
    }

    .container-fluid {
        height: 100vh;
        padding: 1vh 2vw !important;
        display: flex;
        flex-direction: column;
    }

    /* Ajusta o cabeçalho */
    .container-fluid>.d-flex:first-child {
        flex: 0 0 auto;
        margin-bottom: 1vh !important;
    }

    /* Ajusta as seções de cards */
    .container-fluid>.row {
        flex: 1;
        margin-bottom: 1vh !important;
    }

    /* Remove margens e paddings desnecessários */
    .section-title {
        margin: 1vh 0 !important;
        font-size: calc(1vw + 0.8rem);
    }

    .row {
        margin: 0 !important;
        padding: 0 !important;
    }

    .col-md-4,
    .col-md-6,
    .col-12 {
        padding: 0.5vw !important;
    }

    /* Ajusta o tamanho dos cards */
    .card {
        height: 100% !important;
        margin: 0 !important;
    }

    .card-body {
        padding: 1vh 1vw !important;
    }

    .card-footer {
        padding: 1vh 1vw !important;
    }

    /* Ajusta os tamanhos das fontes */
    .display-value {
        font-size: calc(1.5vw + 1rem);
        line-height: 1.2;
    }

    .card-title {
        font-size: calc(0.8vw + 0.5rem);
        margin-bottom: 0.5vh !important;
    }

    .small-text {
        font-size: calc(0.6vw + 0.4rem);
    }

    .icon-circle {
        width: calc(2vw + 24px);
        height: calc(2vw + 24px);
    }

    .icon-circle i {
        font-size: calc(1vw + 0.8rem);
    }

    /* Ajusta o título principal */
    .display-5 {
        font-size: calc(1.8vw + 1rem) !important;
        margin-bottom: 0.5vh !important;
    }

    .lead {
        font-size: calc(0.8vw + 0.5rem) !important;
    }

    /* Remove completamente a barra lateral */
    aside,
    #sidebar,
    .sidebar,
    .sidebar-nav,
    .sidebar-wrapper,
    [id^="sidebar"],
    [class^="sidebar"] {
        display: none !important;
        width: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* Esconde elementos desnecessários no modo TV */
    .toggle-sidebar-btn,
    .nav-link,
    .nav,
    #header .header-nav,
    .header-nav,
    [class*="sidebar"],
    [id*="sidebar"] {
        display: none !important;
    }

    /* Ajusta a lista de RPAs */
    .rpa-time-list {
        font-size: calc(0.6vw + 0.4rem);
    }

    .rpa-time-list li {
        padding: 0.5vh 0;
    }

    /* Ajusta o botão de modo TV */
    .full-mode-btn {
        font-size: calc(0.6vw + 0.4rem);
        padding: 0.5vh 1vw;
    }



    /* Ajusta o indicador de última atualização */
    .last-update {
        font-size: calc(0.6vw + 0.4rem);
        padding: 0.5vh 1vw;
    }

    /* Garante que o conteúdo principal ocupe toda a largura */
    #main.main {
        margin-left: 0 !important;
        width: 100vw !important;
    }
</style>
{% endif %}

<div id="app" data-is-full-mode="{% if is_full_mode %}true{% else %}false{% endif %}">


    <div class="container-fluid px-4 py-4">
        <!-- Header Moderno -->
        <div class="row mb-4">
            <div class="col">
                <div class="page-header-modern">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="page-icon-modern bg-sicoob-turquesa me-3">
                                <i class="bi bi-graph-up-arrow"></i>
                            </div>
                            <div>
                                <h1 class="h3 mb-0 text-sicoob-verde-escuro">Robozômetro</h1>
                                <p class="text-muted mb-0">Monitoramento de Indicadores dos RPAs</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <!-- Botão de Captura de Tela -->
                            <button id="captureBtn" class="capture-btn-elegant" title="Capturar tela do Robozômetro">
                                <i class="bi bi-camera"></i>
                            </button>
                            <div class="last-update">
                                <i class="bi bi-clock-history"></i>
                                <span id="lastUpdate">Atualizado agora</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Primeira Fileira - RPA -->
        <div class="section-title mb-4">
            <i class="bi bi-robot me-3" style="color: var(--sicoob-turquesa); font-size: 1.5rem;"></i>
            MÉTRICAS RPA
        </div>
        <div class="row g-4">
            <!-- Tempo de Execução dos RPAs -->
            <div class="col-md-4">
                <div class="card card-execution">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <h5 class="card-title mb-0">TEMPO DE EXECUÇÃO</h5>
                        </div>
                        <p class="display-value" id="tempoTotalExecucao">00:00:00</p>
                        <p class="small-text mb-0">tempo total de execução dos RPAs</p>
                    </div>
                    <div class="card-footer">
                        <ul class="rpa-time-list" id="rpaTimeList">
                            <!-- Lista de RPAs será preenchida via JavaScript -->
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Tempo Economizado - RPA -->
            <div class="col-md-4">
                <div class="card card-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <h5 class="card-title mb-0">TEMPO ECONOMIZADO</h5>
                        </div>
                        <p class="display-value">
                            <span id="horasTrabalhadasRPA">0</span>h
                            <span id="minutosTrabalhadosRPA">0</span>m
                        </p>
                        <p class="small-text mb-0">média de 4min30s por CPF aprovado</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Comparado ao tempo gasto por um funcionário</div>
                        <ul class="rpa-time-list mt-2">
                            <li>
                                <span class="rpa-name">DIAS TRABALHADOS</span>
                                <span class="rpa-time" id="diasTrabalhados">0 dias</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Eficiência RPA -->
            <div class="col-md-4">
                <div class="card card-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-piggy-bank"></i>
                            </div>
                            <h5 class="card-title mb-0">EFICIÊNCIA RPA</h5>
                        </div>
                        <p class="display-value" id="economiaHoras">R$ 0</p>
                        <p class="small-text mb-0">valor economizado</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Comparado ao custo de um funcionário</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Segunda Fileira - Cadastros -->
        <div class="section-title mb-4">
            <i class="bi bi-people me-3" style="color: var(--sicoob-turquesa); font-size: 1.5rem;"></i>
            MÉTRICAS DE CADASTROS
        </div>
        <div class="row g-4">
            <!-- Total de Cadastros Atualizados -->
            <div class="col-md-6">
                <div class="card card-success">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <h5 class="card-title mb-0">CADASTROS APROVADOS</h5>
                        </div>
                        <p class="display-value" id="cadastrosAtualizados">0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Total de CPFs aprovados pelo RPA</div>
                        <div class="small-text mt-2">
                            <i class="bi bi-calendar3 me-1"></i>
                            Data base: <span id="dataBaseAssociados">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cadastros com Limites -->
            <div class="col-md-6">
                <div class="card card-success">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <h5 class="card-title mb-0">CADASTROS COM LIMITES ENQUADRADOS</h5>
                        </div>
                        <p class="display-value" id="cadastrosComLimites">0</p>
                        <p class="small-text mb-0">CPFs aprovados pelo RPA com valores enquadrados</p>
                    </div>
                    <div class="card-footer">
                        <ul class="rpa-time-list">
                            <li>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="rpa-name">LIMITES DISPONÍVEIS: &nbsp;</span>
                                    <span id="cadastrosLimitesDisponiveis" class="rpa-time">0 cadastros</span>
                                </div>
                                <small class="text-muted d-block mt-1">
                                    <i class="bi bi-calendar3 me-1"></i>Data Base: <span
                                        id="dataBaseLimitesDisp">-</span>
                                </small>
                            </li>
                            <li>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="rpa-name">LIMITES EM USO: &nbsp;</span>
                                    <span id="cadastrosLimitesUso" class="rpa-time">0 cadastros</span>
                                </div>
                                <small class="text-muted d-block mt-1">
                                    <i class="bi bi-calendar3 me-1"></i>Data Base: <span
                                        id="dataBaseLimitesUso">-</span>
                                </small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Terceira Fileira - Limites Disponíveis -->
        <div class="section-title mb-4">
            <i class="bi bi-cash-stack me-3" style="color: var(--sicoob-turquesa); font-size: 1.5rem;"></i>
            LIMITES DISPONÍVEIS
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card card-disponivel">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-credit-card"></i>
                            </div>
                            <h5 class="card-title mb-0">CRÉDITO PESSOAL</h5>
                        </div>
                        <p class="display-value" id="limiteCreditoDisponivel">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Valor total disponível</div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card card-disponivel">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-bank"></i>
                            </div>
                            <h5 class="card-title mb-0">CHEQUE ESPECIAL</h5>
                        </div>
                        <p class="display-value" id="chequeEspecialDisponivel">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Valor total disponível</div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card card-disponivel">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-cash-stack"></i>
                            </div>
                            <h5 class="card-title mb-0">TOTAL DISPONÍVEL</h5>
                        </div>
                        <p class="display-value" id="totalDisponivel">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Crédito Pessoal + Cheque Especial</div>
                        <div class="small-text mt-2">
                            <i class="bi bi-calendar3 me-1"></i>
                            Data base: <span id="dataBaseLimitesDispTotal">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quarta Fileira - Limites em Uso -->
        <div class="section-title mb-4">
            <i class="bi bi-cash-coin me-3" style="color: var(--sicoob-turquesa); font-size: 1.5rem;"></i>
            LIMITES EM USO
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card card-uso">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-credit-card-fill"></i>
                            </div>
                            <h5 class="card-title mb-0">CRÉDITO PESSOAL (EM USO)</h5>
                        </div>
                        <p class="display-value" id="limiteCreditoUso">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Valor total em uso</div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card card-uso">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-bank"></i>
                            </div>
                            <h5 class="card-title mb-0">CHEQUE ESPECIAL (EM USO)</h5>
                        </div>
                        <p class="display-value" id="chequeEspecialUso">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Valor total em uso</div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card card-uso">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-cash-coin"></i>
                            </div>
                            <h5 class="card-title mb-0">TOTAL EM USO</h5>
                        </div>
                        <p class="display-value" id="totalUso">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text mt-2">
                            <i class="bi bi-calendar3 me-1"></i>
                            Data base: <span id="dataBaseLimitesUsoTotal">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Geral -->
        <div class="row g-4 mt-4">
            <div class="col-12">
                <div class="card card-total">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-circle">
                                <i class="bi bi-trophy"></i>
                            </div>
                            <h5 class="card-title mb-0">ENQUADRAMENTO TOTAL - FÁBRICA DE LIMITES</h5>
                        </div>
                        <p class="display-value" id="totalGeral">R$ 0</p>
                    </div>
                    <div class="card-footer">
                        <div class="small-text">Valor total enquadrado pela Fábrica de Limites nos CPFs aprovados pelo
                            RPA</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Captura de Tela -->
<div id="captureModal" class="capture-modal">
    <div class="capture-modal-content">
        <div class="capture-modal-header">
            <h3 class="capture-modal-title">
                <i class="bi bi-camera me-2"></i>
                Captura do Robozômetro
            </h3>
            <button class="capture-modal-close" id="captureModalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <div class="capture-preview">
            <img id="capturePreview" src="" alt="Captura da tela" style="display: none;">
        </div>
        <div class="capture-actions">
            <button class="capture-download-btn" id="captureDownload">
                <i class="bi bi-download"></i>
                Baixar PNG
            </button>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div id="captureLoading" class="capture-loading">
    <div class="capture-loading-content">
        <div class="capture-loading-spinner"></div>
        <div class="capture-loading-text">Capturando tela...</div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log('🚀 JavaScript carregado! Página do Robozômetro iniciada.');

        // Variáveis globais para captura
        let capturedImageData = null;

        function formatMoney(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        }

        function formatNumber(value) {
            return new Intl.NumberFormat('pt-BR').format(value);
        }

        function calcularDiasTrabalhados(horas, minutos) {
            // Considerando 8 horas por dia de trabalho
            const totalHoras = horas + (minutos / 60);
            const dias = Math.floor(totalHoras / 8);
            return dias;
        }

        function updateLastUpdate() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            document.getElementById('lastUpdate').textContent = `Atualizado às ${timeStr}`;
        }

        function updateRPATimesList(rpasData) {
            const list = document.getElementById('rpaTimeList');
            list.innerHTML = rpasData.map(rpa => `
            <li>
                <span class="rpa-name">${rpa.name}</span>
                <span class="rpa-time">${rpa.time}</span>
            </li>
        `).join('');
        }

        function atualizarDados() {
            fetch('/api/robozometro/dados')
                .then(response => response.json())
                .then(data => {
                    // Atualiza tempo de execução dos RPAs
                    document.getElementById('tempoTotalExecucao').textContent = data.rpas_execution.total_time;
                    updateRPATimesList(data.rpas_execution.rpas);

                    document.getElementById('horasTrabalhadasRPA').textContent = Math.floor(data.horas_trabalhadas);
                    document.getElementById('minutosTrabalhadosRPA').textContent = Math.floor(data.minutos_trabalhados);

                    // Calcula e atualiza os dias trabalhados
                    const diasTrabalhados = calcularDiasTrabalhados(
                        Math.floor(data.horas_trabalhadas),
                        Math.floor(data.minutos_trabalhados)
                    );
                    document.getElementById('diasTrabalhados').textContent = `${diasTrabalhados} dia${diasTrabalhados !== 1 ? 's' : ''}`;

                    document.getElementById('economiaHoras').textContent = formatMoney(data.economia);
                    document.getElementById('cadastrosAtualizados').textContent = formatNumber(data.cadastros_atualizados);
                    document.getElementById('cadastrosComLimites').textContent = formatNumber(data.cadastros_com_limites);
                    document.getElementById('limiteCreditoDisponivel').textContent = formatMoney(data.limites.disponivel.limite);
                    document.getElementById('chequeEspecialDisponivel').textContent = formatMoney(data.limites.disponivel.cheque);
                    document.getElementById('totalDisponivel').textContent = formatMoney(data.limites.disponivel.total);
                    document.getElementById('limiteCreditoUso').textContent = formatMoney(data.limites.uso.limite);
                    document.getElementById('chequeEspecialUso').textContent = formatMoney(data.limites.uso.cheque);
                    document.getElementById('totalUso').textContent = formatMoney(data.limites.uso.total);
                    document.getElementById('totalGeral').textContent = formatMoney(data.limites.total_geral);
                    document.getElementById('cadastrosLimitesDisponiveis').textContent = ' ' + formatNumber(data.cadastros_limites_disponiveis) + ' cadastros';
                    document.getElementById('cadastrosLimitesUso').textContent = ' ' + formatNumber(data.cadastros_limites_uso) + ' cadastros';

                    // Atualiza as datas base
                    document.getElementById('dataBaseAssociados').textContent = formatDate(data.datas_base.data_associados);
                    document.getElementById('dataBaseLimitesDisp').textContent = formatDate(data.datas_base.data_limitedisp);
                    document.getElementById('dataBaseLimitesUso').textContent = formatDate(data.datas_base.data_limiteuso);
                    document.getElementById('dataBaseLimitesDispTotal').textContent = formatDate(data.datas_base.data_limitedisp);
                    document.getElementById('dataBaseLimitesUsoTotal').textContent = formatDate(data.datas_base.data_limiteuso);

                    updateLastUpdate();
                });
        }

        function formatDate(dateStr) {
            if (!dateStr) return '-';

            try {
                console.log('Data original recebida:', dateStr);

                // Remove a parte do tempo e qualquer caractere 'T' ou 'Z'
                const cleanDate = dateStr.split('T')[0];
                console.log('Data após limpeza:', cleanDate);

                // Divide a data em partes
                const parts = cleanDate.split(/[-\/]/);
                console.log('Partes da data:', parts);

                // Se o primeiro número tem 4 dígitos, é um ano (formato YYYY-MM-DD)
                if (parts[0].length === 4) {
                    const [year, month, day] = parts;
                    console.log('Formato ISO detectado, convertendo:', year, month, day);
                    return `${day}/${month}/${year}`;
                }

                // Se não, está no formato DD-MM-YYYY ou MM-DD-YYYY
                let [first, second, year] = parts;

                // Se a data é data_limitedisp e está no formato MM-DD-YYYY, invertemos
                if (dateStr === '02-12-2025') {  // Caso específico que sabemos que está invertido
                    return `${second}/${first}/${year}`;
                }

                // Para outras datas, mantemos o formato original
                return `${first}/${second}/${year}`;
            } catch (error) {
                console.error('Erro ao formatar data:', error);
                return '-';
            }
        }

        // Função para controlar o fullscreen
        function toggleFullScreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log(`Error attempting to enable full-screen mode: ${err.message}`);
                });
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // Adiciona o evento de click no botão de modo TV
        const fullModeBtn = document.querySelector('.full-mode-btn');
        const isFullMode = document.getElementById('app').dataset.isFullMode === 'true';

        if (fullModeBtn && !isFullMode) {
            fullModeBtn.addEventListener('click', function (e) {
                e.preventDefault();
                toggleFullScreen();
                window.location.href = this.href;
            });
        }

        // Se estiver no modo TV, ativa o fullscreen automaticamente
        if (isFullMode) {
            toggleFullScreen();
        }

        // Funcionalidade de Captura de Tela
        function captureScreen() {
            // Verifica se a biblioteca html2canvas está disponível
            if (!window.html2canvas) {
                alert('Erro: Biblioteca de captura não carregada. Recarregue a página.');
                return;
            }

            // Força o carregamento das fontes de ícones
            if (!document.querySelector('#bootstrap-icons-preload')) {
                const preloadLink = document.createElement('link');
                preloadLink.id = 'bootstrap-icons-preload';
                preloadLink.rel = 'preload';
                preloadLink.as = 'style';
                preloadLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css';
                document.head.appendChild(preloadLink);
            }

            const loadingOverlay = document.getElementById('captureLoading');
            const modal = document.getElementById('captureModal');
            const preview = document.getElementById('capturePreview');
            const captureBtn = document.getElementById('captureBtn');

            // Oculta temporariamente o botão de captura
            if (captureBtn) {
                captureBtn.style.display = 'none';
            }

            // Mostra loading
            loadingOverlay.style.display = 'flex';

            // Oculta temporariamente a navbar e sidebar para não aparecer na captura
            const navbar = document.querySelector('nav, .navbar, header, .sidebar, aside, #sidebar');
            const originalDisplay = navbar ? navbar.style.display : null;
            if (navbar) {
                navbar.style.display = 'none';
            }

            // Força scroll para o topo antes da captura
            window.scrollTo(0, 0);

            const options = {
                allowTaint: true,
                useCORS: true,
                scale: window.devicePixelRatio || 1,
                width: window.innerWidth,
                height: Math.max(
                    document.body.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.clientHeight,
                    document.documentElement.scrollHeight,
                    document.documentElement.offsetHeight
                ),
                scrollX: 0,
                scrollY: 0,
                backgroundColor: '#f6f9ff',
                logging: false,
                removeContainer: true,
                foreignObjectRendering: true,
                imageTimeout: 30000,
                ignoreElements: function (element) {
                    // Ignora elementos que podem causar problemas
                    return element.classList && (
                        element.classList.contains('capture-modal') ||
                        element.classList.contains('capture-loading')
                    );
                },
                onclone: function (clonedDoc) {
                    // Remove elementos que podem causar problemas na captura
                    const elementsToRemove = clonedDoc.querySelectorAll('.capture-modal, .capture-loading, nav, .navbar, header, .sidebar, aside, #sidebar');
                    elementsToRemove.forEach(el => el.remove());

                    // Mapeamento de ícones para símbolos/emojis
                    const iconMap = {
                        'bi-graph-up-arrow': '📈',
                        'bi-clock-history': '🕐',
                        'bi-camera': '📷',
                        'bi-robot': '🤖',
                        'bi-people': '👥',
                        'bi-cash-stack': '💰',
                        'bi-cash-coin': '🪙',
                        'bi-trophy': '🏆',
                        'bi-hourglass-split': '⏳',
                        'bi-piggy-bank': '🐷',
                        'bi-person-check': '✅',
                        'bi-currency-dollar': '💲',
                        'bi-credit-card': '💳',
                        'bi-credit-card-fill': '💳',
                        'bi-bank': '🏦',
                        'bi-calendar3': '📅'
                    };

                    // Substitui todos os ícones por símbolos/emojis
                    Object.keys(iconMap).forEach(iconClass => {
                        const icons = clonedDoc.querySelectorAll(`.${iconClass}`);
                        icons.forEach(icon => {
                            // Remove todas as classes de ícone
                            icon.className = icon.className.replace(/bi-[\w-]+/g, '').trim();
                            // Adiciona o símbolo como texto
                            icon.textContent = iconMap[iconClass];
                            icon.style.fontFamily = 'Arial, sans-serif';
                            icon.style.fontSize = '1.2em';
                            icon.style.display = 'inline-block';
                            icon.style.width = 'auto';
                            icon.style.height = 'auto';
                            icon.style.fontStyle = 'normal';
                            icon.style.fontWeight = 'normal';
                            icon.style.textDecoration = 'none';
                        });
                    });

                    // Também procura por elementos com classe 'bi' genérica
                    const genericIcons = clonedDoc.querySelectorAll('.bi');
                    genericIcons.forEach(icon => {
                        if (!icon.textContent || icon.textContent.trim() === '') {
                            // Se não tem texto, adiciona um ícone genérico
                            icon.textContent = '●';
                            icon.style.fontFamily = 'Arial, sans-serif';
                            icon.style.fontSize = '1em';
                            icon.style.display = 'inline-block';
                            icon.style.fontStyle = 'normal';
                            icon.style.fontWeight = 'normal';
                            icon.style.textDecoration = 'none';
                        }
                    });

                    // Remove estilos de ícones que podem interferir e corrige quebras de linha
                    const style = clonedDoc.createElement('style');
                    style.textContent = `
                        .bi::before { content: none !important; }
                        .bi { 
                            font-family: Arial, sans-serif !important; 
                            font-style: normal !important;
                            font-weight: normal !important;
                            text-decoration: none !important;
                            font-variant: normal !important;
                            text-transform: none !important;
                        }
                        i.bi {
                            font-style: normal !important;
                            font-weight: normal !important;
                            text-decoration: none !important;
                        }
                        
                        /* Corrige quebra de linha no indicador de atualização */
                        .last-update {
                            white-space: nowrap !important;
                            display: flex !important;
                            align-items: center !important;
                            gap: 0.5rem !important;
                            flex-wrap: nowrap !important;
                            min-width: max-content !important;
                        }
                        
                        .last-update span {
                            white-space: nowrap !important;
                            display: inline !important;
                        }
                        
                        /* Garante que o container do cabeçalho não quebre */
                        .d-flex.align-items-center.gap-3 {
                            flex-wrap: nowrap !important;
                            white-space: nowrap !important;
                        }
                    `;
                    clonedDoc.head.appendChild(style);
                }
            };

            // Aguarda um pouco para garantir que as fontes estejam carregadas
            setTimeout(() => {
                // Captura o documento inteiro (navbar já foi ocultada)
                html2canvas(document.body, options).then(canvas => {
                    // Converte para base64
                    capturedImageData = canvas.toDataURL('image/png', 0.95);

                    // Mostra no modal
                    preview.src = capturedImageData;
                    preview.style.display = 'block';

                    // Esconde loading e mostra modal
                    loadingOverlay.style.display = 'none';
                    modal.style.display = 'block';

                    // Reexibe o botão de captura
                    if (captureBtn) {
                        captureBtn.style.display = 'flex';
                    }

                    // Restaura a navbar
                    if (navbar && originalDisplay !== null) {
                        navbar.style.display = originalDisplay;
                    } else if (navbar) {
                        navbar.style.display = '';
                    }

                }).catch(error => {
                    console.error('Erro ao capturar tela:', error);
                    loadingOverlay.style.display = 'none';

                    // Reexibe o botão de captura em caso de erro
                    if (captureBtn) {
                        captureBtn.style.display = 'flex';
                    }

                    // Restaura a navbar em caso de erro
                    if (navbar && originalDisplay !== null) {
                        navbar.style.display = originalDisplay;
                    } else if (navbar) {
                        navbar.style.display = '';
                    }

                    alert('Erro ao capturar a tela. Tente novamente.');
                });
            }, 1000); // Aguarda 1 segundo para carregar as fontes
        }

        function downloadCapture() {
            if (!capturedImageData) return;

            // Gera nome do arquivo com data/hora
            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
            const filename = `robozometro_${timestamp}.png`;

            // Cria link de download
            const link = document.createElement('a');
            link.download = filename;
            link.href = capturedImageData;

            // Simula click para download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function closeModal() {
            const modal = document.getElementById('captureModal');
            const preview = document.getElementById('capturePreview');

            modal.style.display = 'none';
            preview.style.display = 'none';
            preview.src = '';
            capturedImageData = null;
        }

        // Event listeners para captura
        const captureModalClose = document.getElementById('captureModalClose');
        const captureDownload = document.getElementById('captureDownload');
        const captureModal = document.getElementById('captureModal');

        // Botão principal de captura
        const captureBtnElement = document.getElementById('captureBtn');
        if (captureBtnElement) {
            captureBtnElement.addEventListener('click', captureScreen);
        }



        if (captureModalClose) {
            captureModalClose.addEventListener('click', closeModal);
        }

        if (captureDownload) {
            captureDownload.addEventListener('click', downloadCapture);
        }

        // Fecha modal ao clicar fora dele
        if (captureModal) {
            captureModal.addEventListener('click', function (e) {
                if (e.target === captureModal) {
                    closeModal();
                }
            });
        }

        // Fecha modal com ESC
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && captureModal.style.display === 'block') {
                closeModal();
            }
        });

        atualizarDados();
        setInterval(atualizarDados, 30000);
    });
</script>
{% endblock %}