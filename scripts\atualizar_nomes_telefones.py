import sys
import os

# Adiciona o diretório raiz do projeto ao PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)

# Define o caminho do diretório atual do script
current_dir = os.path.dirname(os.path.abspath(__file__))

import pandas as pd
import mysql.connector
import logging
from utils.db import get_mysql_connection
from datetime import datetime

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def formatar_cpf(cpf):
    """Formata o CPF para garantir 11 dígitos com zeros à esquerda"""
    if pd.isna(cpf):
        return None
    
    # Remove caracteres não numéricos
    cpf = ''.join(filter(str.isdigit, str(cpf)))
    
    # Garante que tenha 11 dígitos com zeros à esquerda
    return cpf.zfill(11)

def atualizar_nomes_telefones():
    try:
        # Conectar ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Ler a planilha de Associados
        planilha_path = os.path.join(current_dir, 'Associados.xlsx')
        logger.info(f"Lendo planilha {planilha_path}...")
        
        # Lê a planilha especificando o tipo da coluna CPF como string
        df = pd.read_excel(planilha_path, dtype={'CPF': str})
        
        # Formata os CPFs na coluna
        df['CPF'] = df['CPF'].apply(formatar_cpf)
        
        # Remove linhas com CPF inválido ou nulo
        df = df.dropna(subset=['CPF'])
        
        # Remover duplicatas de CPF, mantendo a primeira ocorrência
        df = df.drop_duplicates(subset=['CPF'], keep='first')
        
        # Inicializar contadores e listas para relatório
        total_cpfs = len(df)
        cpfs_nao_encontrados = 0
        cpfs_ja_atualizados = 0
        cpfs_atualizados = 0
        
        # Listas para armazenar detalhes
        detalhes_atualizados = []
        detalhes_nao_atualizados = []
        
        # Debug: Mostrar primeiros CPFs da planilha
        logger.info("\nAmostras de CPFs da planilha:")
        for cpf in df['CPF'].head().values:
            logger.info(f"CPF da planilha: {cpf}")
        
        # Processar cada CPF da planilha
        for _, row in df.iterrows():
            cpf = formatar_cpf(row['CPF'])
            nome = str(row['Nome']).strip() if pd.notna(row['Nome']) else None
            telefone = str(row['Telefone']).strip() if pd.notna(row['Telefone']) else None
            
            # Debug: Mostrar CPF antes da consulta
            logger.debug(f"Processando CPF: {cpf}")
            
            # Verificar se o CPF existe na tabela associados
            cursor.execute("SELECT cpf, nome, telefone FROM associados WHERE cpf = %s", (cpf,))
            result = cursor.fetchone()
            
            if result:
                # Debug: Mostrar dados encontrados no banco
                logger.debug(f"Dados no banco para CPF {cpf}: {result}")
                
                # CPF encontrado, verificar se precisa atualizar
                precisa_atualizar = False
                update_fields = []
                update_values = []
                campos_atualizados = []
                
                # Verificar nome
                if result['nome'] is None and nome:
                    update_fields.append("nome = %s")
                    update_values.append(nome)
                    campos_atualizados.append('nome')
                    precisa_atualizar = True
                elif result['nome'] is None and not nome:
                    detalhes_nao_atualizados.append({
                        'cpf': cpf,
                        'motivo': 'Nome vazio na planilha',
                        'nome_banco': result['nome'],
                        'nome_planilha': nome
                    })
                
                # Verificar telefone
                if result['telefone'] is None and telefone:
                    update_fields.append("telefone = %s")
                    update_values.append(telefone)
                    campos_atualizados.append('telefone')
                    precisa_atualizar = True
                elif result['telefone'] is None and not telefone:
                    detalhes_nao_atualizados.append({
                        'cpf': cpf,
                        'motivo': 'Telefone vazio na planilha',
                        'telefone_banco': result['telefone'],
                        'telefone_planilha': telefone
                    })
                
                if precisa_atualizar:
                    # Construir e executar query de update
                    update_query = f"UPDATE associados SET {', '.join(update_fields)} WHERE cpf = %s"
                    update_values.append(cpf)
                    cursor.execute(update_query, tuple(update_values))
                    cpfs_atualizados += 1
                    detalhes_atualizados.append({
                        'cpf': cpf,
                        'campos': campos_atualizados,
                        'nome': nome,
                        'telefone': telefone
                    })
                    logger.info(f"CPF {cpf} atualizado com sucesso - Campos: {', '.join(campos_atualizados)}")
                else:
                    cpfs_ja_atualizados += 1
                    logger.info(f"CPF {cpf} já estava atualizado ou sem dados para atualizar")
            else:
                cpfs_nao_encontrados += 1
                logger.info(f"CPF {cpf} não encontrado na tabela associados")
                # Debug: Mostrar consulta que falhou
                logger.debug(f"Consulta falhou para CPF: {cpf}")
                # Verificar no banco diretamente
                cursor.execute("SELECT cpf FROM associados WHERE cpf LIKE %s", (f"%{cpf}%",))
                similares = cursor.fetchall()
                if similares:
                    logger.info(f"CPFs similares encontrados no banco: {[r['cpf'] for r in similares]}")
        
        # Commit das alterações
        conn.commit()
        
        # Log do resumo final
        logger.info("\nResumo do processamento:")
        logger.info(f"Total de CPFs processados: {total_cpfs}")
        logger.info(f"CPFs atualizados: {cpfs_atualizados}")
        logger.info(f"CPFs não encontrados: {cpfs_nao_encontrados}")
        logger.info(f"CPFs já atualizados: {cpfs_ja_atualizados}")
        
        # Log detalhado dos CPFs atualizados
        if detalhes_atualizados:
            logger.info("\nCPFs que foram atualizados:")
            for detalhe in detalhes_atualizados:
                logger.info(f"CPF: {detalhe['cpf']}")
                logger.info(f"   Campos atualizados: {', '.join(detalhe['campos'])}")
                logger.info(f"   Nome: {detalhe['nome']}")
                logger.info(f"   Telefone: {detalhe['telefone']}")
                logger.info("---")
        
        # Log dos CPFs que não foram atualizados mesmo tendo campos vazios
        if detalhes_nao_atualizados:
            logger.info("\nCPFs que não foram atualizados (campos vazios na planilha):")
            for detalhe in detalhes_nao_atualizados:
                logger.info(f"CPF: {detalhe['cpf']}")
                logger.info(f"   Motivo: {detalhe['motivo']}")
                if 'nome_banco' in detalhe:
                    logger.info(f"   Nome no banco: {detalhe['nome_banco']}")
                    logger.info(f"   Nome na planilha: {detalhe['nome_planilha']}")
                if 'telefone_banco' in detalhe:
                    logger.info(f"   Telefone no banco: {detalhe['telefone_banco']}")
                    logger.info(f"   Telefone na planilha: {detalhe['telefone_planilha']}")
                logger.info("---")
        
    except Exception as e:
        logger.error(f"Erro ao processar planilha: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    atualizar_nomes_telefones() 