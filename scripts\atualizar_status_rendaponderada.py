import pandas as pd
import mysql.connector
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except mysql.connector.Error as err:
        logger.error(f"Error connecting to MySQL: {err}")
        raise

def limpar_cpf(cpf):
    if pd.isna(cpf):
        return None
    return str(cpf).replace('.', '').replace('-', '').replace('/', '').zfill(11)

def processar_dados():
    try:
        # Read both spreadsheets
        logger.info("Reading spreadsheets...")
        df_cpfs = pd.read_excel('scripts/cpfs_rendaponderada.xlsx')
        df_pas = pd.read_excel('scripts/planilha_y.xlsx')
        
        # Clean CPFs in both dataframes
        df_cpfs['CPF'] = df_cpfs['CPF'].apply(limpar_cpf)
        df_pas['CPF'] = df_pas['CPF'].apply(limpar_cpf)
        
        # Merge dataframes to get PA values
        df = pd.merge(df_cpfs, df_pas[['CPF']], on='CPF', how='inner')
        
        # Initialize counters
        total_registros = 0
        registros_atualizados = 0
        registros_nao_encontrados = 0
        registros_com_erro = 0
        
        # Connect to database
        connection = get_mysql_connection()
        cursor = connection.cursor()
        
        # Process each record
        for _, row in df.iterrows():
            try:
                total_registros += 1
                cpf = row['CPF']
                
                # Check if CPF exists in associados table
                cursor.execute("SELECT id FROM associados WHERE cpf = %s", (cpf,))
                result = cursor.fetchone()
                
                if result:
                    # Update status to 7 for existing record
                    cursor.execute("""
                        UPDATE associados 
                        SET status = 7,
                            data_atualizacao = NOW()
                        WHERE cpf = %s
                    """, (cpf,))
                    registros_atualizados += 1
                    logger.info(f"Status atualizado para 7 - CPF: {cpf}")
                else:
                    registros_nao_encontrados += 1
                    logger.info(f"CPF não encontrado na tabela associados: {cpf}")
                
                connection.commit()
                
            except Exception as e:
                registros_com_erro += 1
                logger.error(f"Erro ao processar CPF {cpf}: {str(e)}")
                connection.rollback()
        
        # Log final results
        logger.info("\n=== Relatório de Processamento ===")
        logger.info(f"Total de registros processados: {total_registros}")
        logger.info(f"Registros atualizados para status 7: {registros_atualizados}")
        logger.info(f"CPFs não encontrados na tabela associados: {registros_nao_encontrados}")
        logger.info(f"Registros com erro: {registros_com_erro}")
        
    except Exception as e:
        logger.error(f"Erro durante o processamento: {str(e)}")
        raise
    finally:
        if 'connection' in locals():
            cursor.close()
            connection.close()
            logger.info("Conexão com o banco de dados fechada")

if __name__ == "__main__":
    processar_dados() 