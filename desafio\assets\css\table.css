/* Sicoob Brand Colors */
:root {
    /* Primary Colors */
    --sicoob-turquoise: #00AE9D;
    --sicoob-dark-green: #003641;
    --sicoob-white: #FFFFFF;
    
    /* Support Colors */
    --sicoob-light-green: #C9D200;
    --sicoob-medium-green: #7DB61C;
    --sicoob-purple: #49479D;
    
    /* Additional Colors for UI */
    --sicoob-gray-100: #F5F5F5;
    --sicoob-gray-200: #EEEEEE;
    --sicoob-gray-300: #E0E0E0;
    --sicoob-gray-400: #BDBDBD;
    --sicoob-gray-500: #9E9E9E;
    --sicoob-gray-600: #757575;
    --sicoob-gray-700: #616161;
    --sicoob-gray-800: #424242;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
}

/* Table Styles */
.table-container {
    background: var(--sicoob-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin: 2rem 0;
    overflow: hidden;
}

.table-header {
    background: var(--sicoob-dark-green);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h2 {
    color: var(--sicoob-white);
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.table-header h2 i {
    color: var(--sicoob-light-green);
}

.table-toolbar {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    position: relative;
    margin-right: 1rem;
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--sicoob-gray-500);
}

.search-box input {
    padding-left: 2.5rem;
    width: 250px;
    background: var(--sicoob-white);
    border: 1px solid var(--sicoob-gray-300);
    border-radius: var(--radius-md);
}

.total-records {
    color: var(--sicoob-white);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.total-records i {
    color: var(--sicoob-light-green);
}

.table-responsive {
    overflow-x: auto;
    margin: 0;
    padding: 0;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

th {
    background: var(--sicoob-gray-100);
    color: var(--sicoob-dark-green);
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    border-bottom: 2px solid var(--sicoob-gray-200);
    white-space: nowrap;
}

td {
    padding: 1rem;
    border-bottom: 1px solid var(--sicoob-gray-200);
    color: var(--sicoob-gray-800);
    transition: background-color 0.2s ease;
}

tr:hover td {
    background-color: var(--sicoob-gray-100);
}

/* Status and Value Formatting */
.currency {
    font-family: monospace;
    color: var(--sicoob-dark-green);
    font-weight: 500;
}

.status {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
}

.status-sim {
    background: var(--sicoob-light-green);
    color: var(--sicoob-dark-green);
}

.status-nao {
    background: var(--sicoob-gray-200);
    color: var(--sicoob-gray-600);
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--sicoob-turquoise);
    color: var(--sicoob-white);
}

.btn-primary:hover {
    background: #009688;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--sicoob-gray-200);
    color: var(--sicoob-gray-700);
}

.btn-secondary:hover {
    background: var(--sicoob-gray-300);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--sicoob-gray-100);
    border-top: 1px solid var(--sicoob-gray-200);
}

.pagination-info {
    color: var(--sicoob-gray-600);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    gap: 0.5rem;
}

.page-button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--sicoob-gray-300);
    border-radius: var(--radius-md);
    background: var(--sicoob-white);
    color: var(--sicoob-gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-button:hover {
    background: var(--sicoob-gray-100);
    border-color: var(--sicoob-gray-400);
}

.page-button.active {
    background: var(--sicoob-turquoise);
    color: var(--sicoob-white);
    border-color: var(--sicoob-turquoise);
}

.page-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--sicoob-white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    width: 100%;
    max-width: 500px;
    position: relative;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    color: var(--sicoob-dark-green);
    margin: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--sicoob-gray-700);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--sicoob-gray-300);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--sicoob-turquoise);
    box-shadow: 0 0 0 3px rgba(0, 174, 157, 0.1);
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23666' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
}

/* Ajuste do rodapé */
.product-option.has-sale .product-status {
    color: var(--sicoob-turquoise);
} 