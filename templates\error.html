{% extends "base.html" %}

{% block title %}Erro - RPA Monitor{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h1 class="h3 mb-3 text-danger">Oops! Algo deu errado</h1>
                    
                    <p class="text-muted mb-4">
                        Ocorreu um erro inesperado no sistema. Nossa equipe foi notificada e está trabalhando para resolver o problema.
                    </p>
                    
                    {% if error %}
                    <div class="alert alert-light border-0 bg-light text-start mb-4">
                        <small class="text-muted">
                            <strong>Detalhes técnicos:</strong><br>
                            {{ error }}
                        </small>
                    </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary me-md-2">
                            <i class="bi bi-house-fill me-2"></i>Voltar ao Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Página Anterior
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 15px;
}

.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.alert {
    border-radius: 10px;
    font-family: 'Courier New', monospace;
}

.bi-exclamation-triangle-fill {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
{% endblock %}
