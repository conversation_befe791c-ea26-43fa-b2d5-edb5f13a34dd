from utils.db import get_mysql_connection

def check_registros():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Total de registros
        cursor.execute('SELECT COUNT(*) as total FROM associados')
        total_registros = cursor.fetchone()[0]
        print(f'\nTotal de registros na tabela associados: {total_registros}')

        # Registros com nome vazio ou nulo
        cursor.execute('SELECT COUNT(*) as total FROM associados WHERE nome IS NULL OR nome = \'\'')
        total_sem_nome = cursor.fetchone()[0]
        print(f'Total de registros sem nome: {total_sem_nome}')
        print(f'Porcentagem de registros sem nome: {(total_sem_nome/total_registros)*100:.2f}%\n')

        # Detalhamento por status
        cursor.execute('''
            SELECT 
                s.nome as status,
                COUNT(*) as total,
                SUM(CASE WHEN a.nome IS NULL OR a.nome = \'\' THEN 1 ELSE 0 END) as sem_nome
            FROM associados a
            JOIN status s ON a.status = s.id
            GROUP BY s.nome
            ORDER BY COUNT(*) DESC
        ''')
        
        print("Detalhamento por status:")
        print("------------------------")
        for row in cursor.fetchall():
            status, total, sem_nome = row
            print(f"Status: {status}")
            print(f"- Total de registros: {total}")
            print(f"- Registros sem nome: {sem_nome}")
            print(f"- Porcentagem sem nome: {(sem_nome/total)*100:.2f}%")
            print("------------------------")

    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    check_registros() 