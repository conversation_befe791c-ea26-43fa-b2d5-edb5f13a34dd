from utils.db import get_mysql_connection
import time
from datetime import datetime

def format_seconds(seconds):
    """Formata segundos em horas, minutos e segundos"""
    if not seconds:
        return "00:00:00"
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def monitor_rpa_tracking():
    """Monitora em tempo real o registro do RPA na tabela rpa_tracking"""
    print("\nMonitorando RPA em tempo real (Ctrl+C para parar)...")
    print("-" * 80)
    
    try:
        while True:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # Lista todos os registros para verificar
            cursor.execute("""
                SELECT rpa_name, display_name, current_status, total_active_time, 
                       last_start_time, last_end_time, last_status_update
                FROM rpa_tracking
                ORDER BY id DESC
            """)
            
            results = cursor.fetchall()
            
            print(f"\033[2J\033[H")  # Limpa a tela
            print("Status dos RPAs:")
            print("-" * 80)
            
            if results:
                for result in results:
                    rpa_name, display_name, status, total_time, start_time, end_time, last_update = result
                    now = datetime.now()
                    
                    # Se está rodando, calcula o tempo adicional desde a última atualização
                    current_total = total_time or 0
                    if status == 'running' and last_update:
                        elapsed = (now - last_update).total_seconds()
                        current_total += elapsed
                    
                    print(f"\nRPA: {display_name or rpa_name}")
                    print("-" * 40)
                    print(f"Processo: {rpa_name}")
                    print(f"Status atual: {status or 'N/A'}")
                    print(f"Tempo total ativo: {format_seconds(current_total)}")
                    print(f"Início da execução: {start_time or 'N/A'}")
                    print(f"Último fim: {end_time if end_time else 'N/A'}")
                    print(f"Última atualização: {last_update or 'N/A'}")
                    if status == 'running' and last_update:
                        print(f"Tempo desde última atualização: {elapsed:.1f} segundos")
                    print()
            else:
                print("Nenhum registro encontrado na tabela rpa_tracking")
            
            cursor.close()
            conn.close()
            
            time.sleep(1)  # Atualiza a cada segundo
            
    except KeyboardInterrupt:
        print("\nMonitoramento encerrado pelo usuário.")
    except Exception as e:
        print(f"\nErro durante o monitoramento: {e}")

if __name__ == '__main__':
    monitor_rpa_tracking() 