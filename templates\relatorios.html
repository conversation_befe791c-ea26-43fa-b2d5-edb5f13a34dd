{% extends "base.html" %}

{% block title %}Relatórios{% endblock %}

{% block extra_css %}
<style>
    /* ===== CORES DA IDENTIDADE VISUAL SICOOB ===== */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
    }

    /* Classes utilitárias de cores */
    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* ===== HEADER MODERNO ===== */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(201, 210, 0, 0.05) 0%, rgba(125, 182, 28, 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(201, 210, 0, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(125, 182, 28, 0.3);
    }

    /* ===== BOTÕES MODERNOS ===== */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    .btn-sicoob-secondary {
        background: linear-gradient(135deg, var(--sicoob-cinza) 0%, #7a8599 100%);
        color: white;
    }

    .btn-sicoob-secondary:hover {
        color: white;
    }

    .btn-sicoob-success {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        color: white;
    }

    .btn-sicoob-success:hover {
        color: white;
    }

    .btn-sicoob-purple {
        background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
        color: white;
    }

    .btn-sicoob-purple:hover {
        color: white;
    }

    /* ===== CARDS DE RELATÓRIO MODERNOS ===== */
    .report-card-modern {
        background: white;
        border-radius: 16px;
        padding: 32px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .report-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        transition: all 0.3s ease;
    }

    .report-card-aprovados::before {
        background: linear-gradient(90deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
    }

    .report-card-pendentes::before {
        background: linear-gradient(90deg, var(--sicoob-azul) 0%, #008a7a 100%);
    }

    .report-card-atualizados::before {
        background: linear-gradient(90deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
    }

    .report-card-modern:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }

    .report-card-modern:hover::before {
        height: 6px;
    }

    .report-icon-modern {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        margin-bottom: 24px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    .report-content-modern {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .report-title-modern {
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .report-description-modern {
        color: var(--sicoob-cinza);
        margin-bottom: 24px;
        line-height: 1.6;
        flex: 1;
        font-size: 14px;
    }

    .btn-download-modern {
        width: 100%;
        margin-top: auto;
    }

    /* ===== LOADING MODERNO ===== */
    .loading-modern {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        backdrop-filter: blur(4px);
    }

    .loading-content-modern {
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
        max-width: 300px;
        width: 90%;
    }

    .loading-icon-modern {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 28px;
        color: white;
        box-shadow: 0 6px 20px rgba(125, 182, 28, 0.4);
    }

    .spinner-border-modern {
        width: 3rem;
        height: 3rem;
        border: 0.3em solid rgba(0, 174, 157, 0.2);
        border-right-color: var(--sicoob-azul);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text-modern {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .loading-subtext-modern {
        font-size: 14px;
        color: var(--sicoob-cinza);
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-header-modern .d-flex {
            flex-direction: column;
            gap: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .report-card-modern {
            padding: 24px;
            margin-bottom: 16px;
        }

        .report-icon-modern {
            width: 56px;
            height: 56px;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .report-title-modern {
            font-size: 18px;
        }

        .report-description-modern {
            font-size: 13px;
        }

        .loading-content-modern {
            padding: 32px 24px;
        }

        .loading-icon-modern {
            width: 56px;
            height: 56px;
            font-size: 24px;
        }

        .loading-text-modern {
            font-size: 16px;
        }
    }

    @media (max-width: 576px) {
        .report-card-modern {
            padding: 20px;
        }

        .btn-modern {
            padding: 10px 20px;
            font-size: 13px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="page-icon-modern bg-sicoob-verde-escuro me-3">
                            <i class="bi bi-file-earmark-bar-graph"></i>
                        </div>
                        <div>
                            <h1 class="h3 mb-0 text-sicoob-verde-escuro">Relatórios</h1>
                            <p class="text-muted mb-0">Selecione o tipo de relatório que deseja gerar</p>
                        </div>
                    </div>
                    {% if session.get('is_admin') %}
                    <div class="header-actions">
                        <a href="{{ url_for('verificar_cpfs_sem_nome') }}" class="btn-modern btn-sicoob-secondary">
                            <i class="bi bi-search me-2"></i>
                            <span>Verificar CPFs sem Nome</span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Relatório de Aprovados -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="report-card-modern report-card-aprovados">
                <div class="report-icon-modern bg-sicoob-verde-escuro">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="report-content-modern">
                    <h3 class="report-title-modern">Relatório de Aprovados</h3>
                    <p class="report-description-modern">Lista todos os cadastros aprovados pelo RPA com informações detalhadas de cada associado.</p>

                    <button class="btn-modern btn-sicoob-success btn-download-modern" onclick="downloadReport('aprovados')">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        <span>Baixar Excel</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Relatório de Pendentes -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="report-card-modern report-card-pendentes">
                <div class="report-icon-modern bg-sicoob-azul">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="report-content-modern">
                    <h3 class="report-title-modern">Relatório de Pendentes</h3>
                    <p class="report-description-modern">Lista todos os cadastros pendentes no sistema aguardando processamento ou aprovação.</p>

                    <button class="btn-modern btn-sicoob-primary btn-download-modern" onclick="downloadReport('pendentes')">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        <span>Baixar Excel</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Relatório de Cadastros Atualizados por PA -->
        <div class="col-12 mb-4">
            <div class="report-card-modern report-card-atualizados">
                <div class="report-icon-modern bg-sicoob-roxo">
                    <i class="bi bi-building"></i>
                </div>
                <div class="report-content-modern">
                    <h3 class="report-title-modern">Relatório de Cadastros Atualizados por PA</h3>
                    <p class="report-description-modern">Resumo sintético dos cadastros atualizados agrupados por Posto de Atendimento (PA) com estatísticas detalhadas.</p>

                    <button class="btn-modern btn-sicoob-purple btn-download-modern" onclick="downloadReport('atualizados_pa')">
                        <i class="bi bi-file-earmark-excel me-2"></i>
                        <span>Baixar Excel</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay Moderno -->
<div class="loading-modern" id="loadingOverlay">
    <div class="loading-content-modern">
        <div class="loading-icon-modern">
            <i class="bi bi-file-earmark-excel"></i>
        </div>
        <div class="spinner-border-modern" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <div class="loading-text-modern">Gerando relatório...</div>
        <div class="loading-subtext-modern">Por favor, aguarde</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // Adiciona animação de entrada
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);
    }

    function hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.opacity = '0';
        document.body.style.overflow = 'auto';

        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }

    async function downloadReport(reportType) {
        // Mostra loading com animação
        showLoading();

        try {
            // Inicia o download
            window.location.href = `/download/relatorio/${reportType}/excel`;

            // Feedback visual para o usuário
            const reportNames = {
                'aprovados': 'Aprovados',
                'pendentes': 'Pendentes',
                'atualizados_pa': 'Cadastros Atualizados por PA'
            };

            console.log(`Iniciando download do relatório: ${reportNames[reportType] || reportType}`);

        } catch (error) {
            console.error('Erro ao gerar relatório:', error);

            // Mostra mensagem de erro mais elegante
            hideLoading();

            // Cria um toast de erro
            showErrorToast('Erro ao gerar relatório. Por favor, tente novamente.');

        } finally {
            // Esconde loading após um tempo adequado
            setTimeout(() => {
                hideLoading();
            }, 1500);
        }
    }

    function showErrorToast(message) {
        // Cria um toast de erro moderno
        const toast = document.createElement('div');
        toast.className = 'toast-error';
        toast.innerHTML = `
            <div class="toast-content">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <span>${message}</span>
            </div>
        `;

        // Adiciona estilos inline para o toast
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(220, 53, 69, 0.3);
            z-index: 10000;
            font-weight: 600;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Anima a entrada
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Remove após 5 segundos
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 5000);
    }

    // Adiciona estilos para o loading com transição suave
    document.addEventListener('DOMContentLoaded', function() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.opacity = '0';
        overlay.style.transition = 'opacity 0.3s ease';
    });
</script>
{% endblock %}