import pandas as pd
import logging
import os

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def limpar_cpf(cpf):
    """Remove caracteres especiais do CPF"""
    return str(cpf).strip().replace('.', '').replace('-', '').replace(' ', '').zfill(11)

def processar_planilhas(planilha_x, planilha_y):
    """
    Processa as planilhas e atualiza a coluna PA
    planilha_x: Planilha com CPF, RENDA, DATA, PA (vazio)
    planilha_y: Planilha com CPF, PA
    """
    try:
        # Ler as planilhas
        logger.info(f"Lendo planilha X: {planilha_x}")
        df_x = pd.read_excel(planilha_x)
        logger.info(f"Colunas encontradas na planilha X: {df_x.columns.tolist()}")
        
        logger.info(f"Lendo planilha Y: {planilha_y}")
        df_y = pd.read_excel(planilha_y)
        logger.info(f"Colunas encontradas na planilha Y: {df_y.columns.tolist()}")
        
        # Verificar se as colunas necessárias existem
        colunas_x_necessarias = ['CPF', 'RENDA', 'DATA']
        colunas_x_faltando = [col for col in colunas_x_necessarias if col not in df_x.columns]
        if colunas_x_faltando:
            raise ValueError(f"Colunas faltando na planilha X: {colunas_x_faltando}")
            
        colunas_y_necessarias = ['CPF', 'PA']
        colunas_y_faltando = [col for col in colunas_y_necessarias if col not in df_y.columns]
        if colunas_y_faltando:
            raise ValueError(f"Colunas faltando na planilha Y: {colunas_y_faltando}")
        
        # Limpar CPFs nas duas planilhas
        logger.info("Padronizando formato dos CPFs...")
        df_x['CPF'] = df_x['CPF'].apply(limpar_cpf)
        df_y['CPF'] = df_y['CPF'].apply(limpar_cpf)
        
        # Verificar e tratar CPFs duplicados na planilha Y
        duplicados = df_y['CPF'].duplicated(keep=False)
        if duplicados.any():
            cpfs_duplicados = df_y[duplicados]['CPF'].unique()
            logger.info(f"Encontrados {len(cpfs_duplicados)} CPFs duplicados na planilha Y")
            logger.info("Mantendo apenas o último PA para cada CPF duplicado")
            
            # Manter apenas o último registro de cada CPF
            df_y = df_y.drop_duplicates(subset='CPF', keep='last')
        
        # Renomear a coluna PA da planilha Y para evitar conflito
        df_y = df_y.rename(columns={'PA': 'PA_Y'})
        
        # Fazer o merge das planilhas usando o CPF como chave
        logger.info("Mesclando dados das planilhas...")
        df_final = pd.merge(
            df_x,
            df_y[['CPF', 'PA_Y']],
            on='CPF',
            how='left'  # Mantém todos os registros da planilha X
        )
        
        # Atualizar a coluna PA com os valores da planilha Y
        df_final['PA'] = df_final['PA_Y']
        df_final = df_final.drop('PA_Y', axis=1)  # Remove a coluna temporária
        
        # Identificar CPFs não encontrados
        cpfs_nao_encontrados = df_final[df_final['PA'].isna()]['CPF'].tolist()
        
        # Contar quantos PAs foram preenchidos
        total_registros = len(df_final)
        pas_preenchidos = df_final['PA'].notna().sum()
        pas_nao_encontrados = total_registros - pas_preenchidos
        
        # Salvar nova planilha
        output_file = os.path.join(
            os.path.dirname(planilha_x),
            'planilha_atualizada.xlsx'
        )
        logger.info(f"Salvando planilha atualizada: {output_file}")
        df_final.to_excel(output_file, index=False)
        
        # Salvar lista de CPFs não encontrados
        if cpfs_nao_encontrados:
            cpfs_nao_encontrados_file = os.path.join(
                os.path.dirname(planilha_x),
                'cpfs_nao_encontrados.txt'
            )
            with open(cpfs_nao_encontrados_file, 'w') as f:
                for cpf in cpfs_nao_encontrados:
                    f.write(f"{cpf}\n")
            logger.info(f"Lista de CPFs não encontrados salva em: {cpfs_nao_encontrados_file}")
        
        # Relatório final
        logger.info("\n=== Relatório de Processamento ===")
        logger.info(f"Total de registros processados: {total_registros}")
        logger.info(f"PAs preenchidos com sucesso: {pas_preenchidos}")
        logger.info(f"CPFs não encontrados na planilha Y: {pas_nao_encontrados}")
        logger.info(f"Arquivo salvo em: {output_file}")
        logger.info("================================")
        
    except Exception as e:
        logger.error(f"Erro durante o processamento: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        # Caminhos das planilhas
        planilha_x = r"C:\Users\<USER>\Downloads\cpfs_atualizados.xlsx"  # Planilha com RENDA e DATA
        planilha_y = r"C:\Users\<USER>\Downloads\planilha_y.xlsx"  # Planilha com os PAs
        
        # Verificar se os arquivos existem
        if not os.path.exists(planilha_x):
            raise FileNotFoundError(f"Arquivo não encontrado: {planilha_x}")
        if not os.path.exists(planilha_y):
            raise FileNotFoundError(f"Arquivo não encontrado: {planilha_y}")
        
        # Processar as planilhas
        processar_planilhas(planilha_x, planilha_y)
        
    except Exception as e:
        logger.error(f"Erro: {str(e)}")
        exit(1) 