<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Verifica se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit();
}

// Obtém os dados do formulário
$cpf = $_POST['cpf'];
$produto = $_POST['produto'];
$valor = $produto === 'DEBITO_AUTOMATICO' ? 1 : floatval($_POST['valor']);
$usuario = $_SESSION['user_id'];
$pa = $_SESSION['pa'];
$tem_debito = $_POST['tem_debito'] === 'Sim';

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Verifica se o CPF existe na tabela de associados e está aprovado
    $stmt = $pdo->prepare("SELECT pa, debito_automatico FROM associados WHERE cpf = ? AND status IN (3, 8)");
    $stmt->execute([$cpf]);
    $associado = $stmt->fetch();
    
    if (!$associado) {
        throw new Exception("CPF não encontrado ou não está aprovado.");
    }

    // Verifica se o usuário tem permissão para registrar venda para este PA
    if ($pa !== '00' && $associado['pa'] !== $pa) {
        throw new Exception("Você não tem permissão para registrar venda para este PA.");
    }

    // Se for Débito Automático, verifica se já está ativo
    if ($produto === 'DEBITO_AUTOMATICO' && $tem_debito) {
        throw new Exception("Débito Automático já está ativo para este associado.");
    }

    // Verifica se já existe uma venda deste produto para este CPF
    $stmt = $pdo->prepare("SELECT id, valor FROM campanha_vendas WHERE cpf = ? AND produto = ?");
    $stmt->execute([$cpf, $produto]);
    $venda_existente = $stmt->fetch();

    if ($venda_existente) {
        // Atualiza o valor da venda existente
        $stmt = $pdo->prepare("UPDATE campanha_vendas SET valor = ?, data_atualizacao = NOW() WHERE id = ?");
        $stmt->execute([$valor, $venda_existente['id']]);
        $mensagem = "Valor da venda atualizado com sucesso!";
    } else {
        // Registra uma nova venda
        $stmt = $pdo->prepare("INSERT INTO campanha_vendas (cpf, produto, valor, pa, usuario) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$cpf, $produto, $valor, $pa, $usuario]);
        $mensagem = "Venda registrada com sucesso!";
    }

    // Confirma a transação
    $pdo->commit();

    // Redireciona com mensagem de sucesso
    $_SESSION['success_message'] = $mensagem;
    header('Location: index.php');
    exit();

} catch (Exception $e) {
    // Desfaz a transação em caso de erro
    $pdo->rollBack();
    
    // Redireciona com mensagem de erro
    $_SESSION['error_message'] = $e->getMessage();
    header('Location: index.php');
    exit();
} 