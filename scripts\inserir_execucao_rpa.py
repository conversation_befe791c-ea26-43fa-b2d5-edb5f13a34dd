import mysql.connector
from datetime import datetime, timedelta

def get_mysql_connection():
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Erro ao conectar ao MySQL: {err}")
        raise

def inserir_execucao():
    try:
        # Calcula a data da última sexta-feira
        hoje = datetime.now()
        dias_desde_sexta = (hoje.weekday() - 4) % 7
        ultima_sexta = hoje - timedelta(days=dias_desde_sexta)
        
        # Define horário de início (8:00) e fim baseado no tempo de execução
        start_time = ultima_sexta.replace(hour=8, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(seconds=323520)
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Insere o registro de execução
        cursor.execute("""
            INSERT INTO rpa_executions 
            (rpa_name, start_time, end_time, status, active_time, last_status_update)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            'RPA_ASSOCIADOS',  # Nome do RPA
            start_time,
            end_time,
            'completed',       # Status final
            323520,           # Tempo de execução em segundos
            end_time         # Última atualização
        ))
        
        execution_id = cursor.lastrowid
        
        # Insere o registro no histórico
        cursor.execute("""
            INSERT INTO rpa_execution_history 
            (execution_id, status, start_time, end_time, duration)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            execution_id,
            'completed',
            start_time,
            end_time,
            323520
        ))
        
        conn.commit()
        print("Registro de execução inserido com sucesso!")
        print(f"Data de início: {start_time}")
        print(f"Data de fim: {end_time}")
        print(f"Tempo total: {323520} segundos (aproximadamente {323520/3600:.2f} horas)")
        
    except Exception as e:
        print(f"Erro ao inserir registro: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    inserir_execucao() 