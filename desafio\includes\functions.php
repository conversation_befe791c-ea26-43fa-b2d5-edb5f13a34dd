<?php
// Função para mascarar CPF
function mask_cpf($cpf) {
    return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cpf);
}

// Função para normalizar o número do PA para dois dígitos
function normalize_pa($pa) {
    return str_pad($pa, 2, '0', STR_PAD_LEFT);
}

// Função para retornar o ícone adequado para cada produto
function get_product_icon($produto) {
    switch ($produto) {
        case 'CREDITO_PESSOAL':
            return 'fa-money-bill-wave';
        case 'CHEQUE_ESPECIAL':
            return 'fa-money-check';
        case 'CARTAO_CREDITO':
            return 'fa-credit-card';
        case 'DEBITO_AUTOMATICO':
            return 'fa-sync';
        default:
            return 'fa-tag';
    }
} 