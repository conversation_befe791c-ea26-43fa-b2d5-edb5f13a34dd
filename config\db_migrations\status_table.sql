-- Criar tabela de status
CREATE TABLE IF NOT EXISTS status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(50) NOT NULL UNIQUE
);

-- Inserir os status
INSERT INTO status (nome) VALUES 
    ('pendente'),
    ('encaminhado'),
    ('aprovado'),
    ('nao encaminhado'),
    ('nao aprovado'),
    ('removido');

-- Adicionar coluna status_id na tabela associados
ALTER TABLE associados ADD COLUMN status_id INT;

-- Atualizar status_id baseado no status atual
UPDATE associados a 
JOIN status s ON a.status = s.nome 
SET a.status_id = s.id;

-- Remover a coluna status antiga
ALTER TABLE associados DROP COLUMN status;

-- Renomear status_id para status
ALTER TABLE associados ADD CONSTRAINT fk_status 
FOREIGN KEY (status_id) REFERENCES status(id);

-- Renomear a coluna
ALTER TABLE associados CHANGE status_id status INT;
