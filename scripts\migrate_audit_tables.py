import os
import sys
import mysql.connector

# Adiciona o diretório raiz ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db import get_mysql_connection

def execute_migration():
    print("Iniciando migração das tabelas de auditoria...")
    
    # Lê o arquivo SQL
    migration_file = os.path.join('config', 'db_migrations', 'create_audit_logs.sql')
    
    try:
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_commands = f.read()
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Executa os comandos SQL
        for command in sql_commands.split(';'):
            if command.strip():
                print(f"Executando comando: {command[:100]}...")  # Mostra apenas os primeiros 100 caracteres
                cursor.execute(command)
        
        # Commit das alterações
        conn.commit()
        print("Migração concluída com sucesso!")
        
    except Exception as e:
        print(f"Erro durante a migração: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        raise
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    execute_migration() 