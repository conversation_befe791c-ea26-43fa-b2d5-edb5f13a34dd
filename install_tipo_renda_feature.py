import mysql.connector
import logging
import sys
import os

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Tentando conectar ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True,
            connection_timeout=5,
            allow_local_infile=True,
            use_pure=True
        )
        logger.info("Conexão MySQL estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def install_tipo_renda_feature():
    """Instala todas as alterações necessárias para a funcionalidade de tipo de renda"""
    
    logger.info("=== INSTALAÇÃO DA FUNCIONALIDADE TIPO DE RENDA ===")
    
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 1. Adicionar coluna tipo_renda na tabela associados
        logger.info("1. Verificando/adicionando coluna 'tipo_renda' na tabela 'associados'...")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'rpa' 
            AND TABLE_NAME = 'associados' 
            AND COLUMN_NAME = 'tipo_renda'
        """)
        
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE associados
                ADD COLUMN tipo_renda TEXT DEFAULT NULL COMMENT 'Tipos de renda do associado (concatenados se múltiplos)'
            """)
            logger.info("✓ Coluna 'tipo_renda' adicionada na tabela 'associados'")
            
            # Adicionar índice
            try:
                cursor.execute("CREATE INDEX idx_tipo_renda ON associados (tipo_renda(255))")
                logger.info("✓ Índice criado para 'tipo_renda' na tabela 'associados'")
            except mysql.connector.Error as err:
                if err.errno == 1061:  # Duplicate key name
                    logger.info("Índice já existe, ignorando...")
                else:
                    raise
        else:
            logger.info("✓ Coluna 'tipo_renda' já existe na tabela 'associados'")
        
        # 2. Adicionar coluna tipo_renda na tabela associados_completa
        logger.info("2. Verificando/adicionando coluna 'tipo_renda' na tabela 'associados_completa'...")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'rpa' 
            AND TABLE_NAME = 'associados_completa' 
            AND COLUMN_NAME = 'tipo_renda'
        """)
        
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE associados_completa
                ADD COLUMN tipo_renda VARCHAR(255) DEFAULT NULL COMMENT 'Tipo de renda do associado'
            """)
            logger.info("✓ Coluna 'tipo_renda' adicionada na tabela 'associados_completa'")
            
            # Adicionar índice
            try:
                cursor.execute("CREATE INDEX idx_tipo_renda_completa ON associados_completa (tipo_renda)")
                logger.info("✓ Índice criado para 'tipo_renda' na tabela 'associados_completa'")
            except mysql.connector.Error as err:
                if err.errno == 1061:  # Duplicate key name
                    logger.info("Índice já existe, ignorando...")
                else:
                    raise
        else:
            logger.info("✓ Coluna 'tipo_renda' já existe na tabela 'associados_completa'")
        
        # 3. Adicionar status 7 (renda_ponderada)
        logger.info("3. Verificando/adicionando status 7 (renda_ponderada)...")
        cursor.execute("SELECT COUNT(*) FROM status WHERE id = 7")
        
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO status (id, nome, descricao) 
                VALUES (7, 'renda_ponderada', 'Associado com renda ponderada')
            """)
            logger.info("✓ Status 7 (renda_ponderada) adicionado")
        else:
            logger.info("✓ Status 7 (renda_ponderada) já existe")
        
        conn.commit()
        
        # 4. Verificar estrutura final
        logger.info("4. Verificando estrutura final...")
        
        # Verificar tabela associados
        cursor.execute("DESCRIBE associados")
        associados_columns = [row[0] for row in cursor.fetchall()]
        logger.info(f"Colunas da tabela 'associados': {len(associados_columns)} colunas")
        if 'tipo_renda' in associados_columns:
            logger.info("✓ Coluna 'tipo_renda' confirmada na tabela 'associados'")
        else:
            logger.error("✗ Coluna 'tipo_renda' não encontrada na tabela 'associados'")
        
        # Verificar tabela associados_completa
        cursor.execute("DESCRIBE associados_completa")
        completa_columns = [row[0] for row in cursor.fetchall()]
        logger.info(f"Colunas da tabela 'associados_completa': {len(completa_columns)} colunas")
        if 'tipo_renda' in completa_columns:
            logger.info("✓ Coluna 'tipo_renda' confirmada na tabela 'associados_completa'")
        else:
            logger.error("✗ Coluna 'tipo_renda' não encontrada na tabela 'associados_completa'")
        
        # Verificar status
        cursor.execute("SELECT id, nome, descricao FROM status ORDER BY id")
        status_list = cursor.fetchall()
        logger.info("Status disponíveis:")
        for status in status_list:
            logger.info(f"  ID: {status[0]}, Nome: {status[1]}, Descrição: {status[2]}")
        
        cursor.close()
        conn.close()
        
        logger.info("=== INSTALAÇÃO CONCLUÍDA COM SUCESSO ===")
        logger.info("")
        logger.info("PRÓXIMOS PASSOS:")
        logger.info("1. Reinicie o servidor Flask (app.py)")
        logger.info("2. Teste o upload de uma planilha com a coluna 'Tipo de Renda'")
        logger.info("3. Verifique se os registros com 'RENDA PONDERADA' recebem status 7")
        logger.info("4. Execute 'python test_tipo_renda_upload.py' para testar a funcionalidade")
        
        return True
        
    except Exception as e:
        logger.error(f"Erro durante a instalação: {str(e)}")
        return False

if __name__ == "__main__":
    success = install_tipo_renda_feature()
    sys.exit(0 if success else 1)
