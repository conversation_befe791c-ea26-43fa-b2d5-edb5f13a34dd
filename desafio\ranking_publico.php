<?php
require_once 'config/config.php';

// Query para ranking por usuário por produto
$query_usuarios = "SELECT 
    u.username,
    u.pa,
    v.produto,
    COUNT(*) as total_vendas,
    SUM(v.valor) as valor_total
FROM campanha_users u
INNER JOIN campanha_vendas v ON u.username = v.usuario
GROUP BY u.username, u.pa, v.produto
ORDER BY v.produto, valor_total DESC";

// Query para ranking por PA por produto
$query_pas = "SELECT 
    v.pa,
    v.produto,
    COUNT(*) as total_vendas,
    SUM(v.valor) as valor_total
FROM campanha_vendas v
GROUP BY v.pa, v.produto
ORDER BY v.produto, valor_total DESC";

$stmt_usuarios = $pdo->query($query_usuarios);
$stmt_pas = $pdo->query($query_pas);

$rankings_usuarios = [];
$rankings_pas = [];

// Organiza os rankings por produto
while ($row = $stmt_usuarios->fetch()) {
    $rankings_usuarios[$row['produto']][] = $row;
}

while ($row = $stmt_pas->fetch()) {
    $rankings_pas[$row['produto']][] = $row;
}

// Lista de produtos com seus ícones e nomes formatados
$produtos = [
    'CREDITO_PESSOAL' => [
        'nome' => 'Crédito Pessoal',
        'icone' => 'fa-money-bill-wave',
        'cor' => '#00AE9D'
    ],
    'CHEQUE_ESPECIAL' => [
        'nome' => 'Cheque Especial',
        'icone' => 'fa-money-check',
        'cor' => '#003641'
    ],
    'CARTAO_CREDITO' => [
        'nome' => 'Cartão de Crédito',
        'icone' => 'fa-credit-card',
        'cor' => '#C9D200'
    ],
    'DEBITO_AUTOMATICO' => [
        'nome' => 'Débito Automático',
        'icone' => 'fa-sync',
        'cor' => '#7DB61C'
    ]
];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ranking da Campanha - Sicoob Credilivre</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/ranking.css">
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="ranking-page">
    <header>
        <div class="container">
            <img src="assets/images/logo1.png" alt="Sicoob" class="logo">
            <h1>Ranking da Campanha</h1>
        </div>
    </header>

    <main class="container">
        <div class="dashboard">
            <?php foreach ($produtos as $codigo => $produto): ?>
                <div class="dashboard-card">
                    <div class="card-header" style="border-color: <?php echo $produto['cor']; ?>">
                        <i class="fas <?php echo $produto['icone']; ?>" style="color: <?php echo $produto['cor']; ?>"></i>
                        <h2><?php echo $produto['nome']; ?></h2>
                    </div>

                    <div class="card-content">
                        <div class="ranking-tabs">
                            <button class="tab-button active" onclick="showTab(this, 'pa-<?php echo $codigo; ?>')">
                                <i class="fas fa-building"></i> Por PA
                            </button>
                            <button class="tab-button" onclick="showTab(this, 'user-<?php echo $codigo; ?>')">
                                <i class="fas fa-users"></i> Por Usuário
                            </button>
                        </div>

                        <div class="tab-content active" id="pa-<?php echo $codigo; ?>">
                            <div class="chart-container">
                                <canvas id="chart-pa-<?php echo $codigo; ?>"></canvas>
                            </div>
                            <div class="ranking-list">
                                <?php if (isset($rankings_pas[$codigo])): ?>
                                    <?php foreach (array_slice($rankings_pas[$codigo], 0, 5) as $pos => $pa): ?>
                                        <div class="ranking-item" style="--rank-color: <?php echo $produto['cor']; ?>">
                                            <span class="position"><?php echo $pos + 1; ?>º</span>
                                            <span class="name">PA <?php echo $pa['pa']; ?></span>
                                            <span class="value">R$ <?php echo number_format($pa['valor_total'], 2, ',', '.'); ?></span>
                                            <span class="quantity"><?php echo $pa['total_vendas']; ?> vendas</span>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="no-data">Nenhuma venda registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="tab-content" id="user-<?php echo $codigo; ?>">
                            <div class="chart-container">
                                <canvas id="chart-user-<?php echo $codigo; ?>"></canvas>
                            </div>
                            <div class="ranking-list">
                                <?php if (isset($rankings_usuarios[$codigo])): ?>
                                    <?php foreach (array_slice($rankings_usuarios[$codigo], 0, 5) as $pos => $usuario): ?>
                                        <div class="ranking-item" style="--rank-color: <?php echo $produto['cor']; ?>">
                                            <span class="position"><?php echo $pos + 1; ?>º</span>
                                            <span class="name"><?php echo $usuario['username']; ?></span>
                                            <span class="value">R$ <?php echo number_format($usuario['valor_total'], 2, ',', '.'); ?></span>
                                            <span class="quantity"><?php echo $usuario['total_vendas']; ?> vendas</span>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="no-data">Nenhuma venda registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </main>

    <style>
    .ranking-page {
        background: var(--sicoob-gray-100);
        min-height: 100vh;
    }

    header {
        background: white;
        padding: 1.5rem 0;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
    }

    header .container {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    header h1 {
        color: var(--sicoob-dark-green);
        margin: 0;
        font-size: 2rem;
    }

    .logo {
        height: 60px;
        width: auto;
    }

    .dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .dashboard-card {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        border-bottom: 3px solid;
    }

    .card-header i {
        font-size: 1.5rem;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
        color: var(--sicoob-dark-green);
    }

    .card-content {
        padding: 1.5rem;
    }

    .ranking-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: var(--sicoob-gray-200);
        color: var(--sicoob-gray-700);
        border-radius: var(--radius-md);
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
    }

    .tab-button.active {
        background: var(--sicoob-turquoise);
        color: white;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .chart-container {
        margin-bottom: 1.5rem;
        height: 200px;
    }

    .ranking-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .ranking-item {
        display: grid;
        grid-template-columns: auto 1fr auto auto;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        background: var(--sicoob-gray-100);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        transition: transform 0.2s ease;
    }

    .ranking-item:hover {
        transform: translateX(5px);
    }

    .position {
        font-weight: 700;
        color: var(--rank-color);
        width: 2rem;
    }

    .name {
        font-weight: 500;
    }

    .value {
        font-family: monospace;
        font-weight: 500;
        color: var(--sicoob-dark-green);
    }

    .quantity {
        color: var(--sicoob-gray-600);
        font-size: 0.75rem;
    }

    .no-data {
        text-align: center;
        color: var(--sicoob-gray-500);
        padding: 2rem;
    }

    @media (max-width: 768px) {
        .dashboard {
            grid-template-columns: 1fr;
        }

        header .container {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        header h1 {
            font-size: 1.5rem;
        }

        .ranking-item {
            grid-template-columns: auto 1fr;
            grid-template-rows: auto auto;
        }

        .value, .quantity {
            grid-column: 1 / -1;
            text-align: right;
        }
    }
    </style>

    <script>
    function showTab(button, tabId) {
        // Remove active class from all buttons and tabs in the same card
        const card = button.closest('.dashboard-card');
        card.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        card.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));

        // Add active class to clicked button and corresponding tab
        button.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // Inicializa os gráficos
    <?php foreach ($produtos as $codigo => $produto): ?>
        // Dados para o gráfico de PA
        <?php if (isset($rankings_pas[$codigo])): ?>
            new Chart(document.getElementById('chart-pa-<?php echo $codigo; ?>'), {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode(array_map(function($pa) { return 'PA ' . $pa['pa']; }, array_slice($rankings_pas[$codigo], 0, 5))); ?>,
                    datasets: [{
                        label: 'Valor Total (R$)',
                        data: <?php echo json_encode(array_map(function($pa) { return $pa['valor_total']; }, array_slice($rankings_pas[$codigo], 0, 5))); ?>,
                        backgroundColor: '<?php echo $produto['cor']; ?>',
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        }
                    }
                }
            });
        <?php endif; ?>

        // Dados para o gráfico de Usuários
        <?php if (isset($rankings_usuarios[$codigo])): ?>
            new Chart(document.getElementById('chart-user-<?php echo $codigo; ?>'), {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode(array_map(function($user) { return $user['username']; }, array_slice($rankings_usuarios[$codigo], 0, 5))); ?>,
                    datasets: [{
                        label: 'Valor Total (R$)',
                        data: <?php echo json_encode(array_map(function($user) { return $user['valor_total']; }, array_slice($rankings_usuarios[$codigo], 0, 5))); ?>,
                        backgroundColor: '<?php echo $produto['cor']; ?>',
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        }
                    }
                }
            });
        <?php endif; ?>
    <?php endforeach; ?>

    // Auto refresh a cada 5 minutos
    setTimeout(function() {
        window.location.reload();
    }, 300000);
    </script>
</body>
</html> 