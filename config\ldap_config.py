from ldap3 import Server, Connection, NTLM, SUBTREE, ALL_ATTRIBUTES
import logging
import os
import hashlib
from passlib.hash import nthash  # Adicionando passlib para suporte a NTLM

# Configuração do logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuração do LDAP
LDAP_CONFIG = {
    'SERVER': 'ldap://172.32.100.150',
    'DOMAIN': 'administracao.local',
    'BASE_DN': 'DC=administracao,DC=local',
    # Filtro de busca de usuários menos restritivo
    'USER_SEARCH_FILTER': '(objectClass=user)',
    'USER_ATTRIBUTES': ['sAMAccountName', 'displayName', 'mail', 'memberOf', 'userAccountControl', 'distinguishedName'],
    'PORT': 389,
    'USE_SSL': False,
    'TIMEOUT': 10,
    # Conta de serviço para operações de leitura
    'SERVICE_USER': 'administrador',
    'SERVICE_PASSWORD': 'admin'
}

def get_ldap_connection(username=None, password=None):
    """
    Cria uma conexão LDAP com as credenciais fornecidas.
    Se username e password não forem fornecidos, usa a conta de serviço.
    """
    try:
        server_url = LDAP_CONFIG['SERVER']
        logger.info(f"Tentando conectar ao servidor LDAP: {server_url}")
        
        server = Server(
            server_url,
            port=LDAP_CONFIG['PORT'],
            use_ssl=LDAP_CONFIG['USE_SSL'],
            get_info=ALL_ATTRIBUTES
        )
        
        if username and password:
            # Formata o username com o domínio se necessário
            if '@' not in username and '\\' not in username:
                user = f"{LDAP_CONFIG['DOMAIN']}\\{username}"
            else:
                user = username
            
            logger.info(f"Tentando conectar ao LDAP com usuário: {user}")
        else:
            # Usa a conta de serviço
            username = LDAP_CONFIG['SERVICE_USER']
            password = LDAP_CONFIG['SERVICE_PASSWORD']
            user = f"{LDAP_CONFIG['DOMAIN']}\\{username}"
            logger.info("Usando conta de serviço para conexão LDAP")
        
        # Tenta conexão com as credenciais
        conn = Connection(
            server,
            user=user,
            password=password,
            authentication=NTLM
        )
        
        if not conn.bind():
            logger.error(f"Erro ao conectar ao LDAP: {conn.result}")
            return None
        
        logger.info("Conexão LDAP estabelecida com sucesso")
        return conn
    
    except Exception as e:
        logger.error(f"Erro ao conectar ao LDAP: {str(e)}")
        return None

def search_user(conn, username):
    """
    Pesquisa um usuário no LDAP.
    Tenta múltiplos formatos de nome de usuário.
    Retorna os atributos do usuário se encontrado, None caso contrário.
    """
    try:
        # Lista de formatos de nome de usuário para tentar
        username_formats = [
            username,  # Nome de usuário original
            f"{username}@{LDAP_CONFIG['DOMAIN']}",  # UPN (User Principal Name)
            f"{LDAP_CONFIG['DOMAIN']}\\{username}",  # Formato de domínio
        ]
        
        for attempt_username in username_formats:
            logger.info(f"Tentando pesquisar usuário: {attempt_username}")
            
            # Construir filtro de busca
            search_filter = f"(|(sAMAccountName={attempt_username})(userPrincipalName={attempt_username}))"
            
            conn.search(
                LDAP_CONFIG['BASE_DN'],
                search_filter,
                search_scope=SUBTREE,
                attributes=LDAP_CONFIG['USER_ATTRIBUTES']
            )
            
            if len(conn.entries) > 0:
                logger.info(f"Usuário {attempt_username} encontrado no LDAP")
                return conn.entries[0]
        
        logger.warning(f"Usuário {username} não encontrado em nenhum formato")
        return None
    except Exception as e:
        logger.error(f"Erro ao pesquisar usuário no LDAP: {str(e)}")
        return None

def get_all_users(conn=None):
    """
    Retorna todos os usuários do domínio.
    """
    try:
        close_conn = False
        if not conn:
            logger.info("Criando nova conexão LDAP para buscar usuários")
            conn = get_ldap_connection()  # Usa a conta de serviço
            close_conn = True
            if not conn:
                logger.error("Não foi possível estabelecer conexão LDAP")
                return []
        
        logger.info(f"Buscando usuários com filtro: {LDAP_CONFIG['USER_SEARCH_FILTER']}")
        logger.info(f"Base DN: {LDAP_CONFIG['BASE_DN']}")
        
        # Busca todos os usuários
        conn.search(
            LDAP_CONFIG['BASE_DN'],
            LDAP_CONFIG['USER_SEARCH_FILTER'],
            search_scope=SUBTREE,
            attributes=['sAMAccountName', 'userAccountControl', 'distinguishedName']
        )
        
        users = []
        for entry in conn.entries:
            try:
                username = str(entry['sAMAccountName'])
                uac = int(str(entry['userAccountControl']))
                
                # Log detalhado sobre cada usuário
                logger.info(f"Usuário encontrado: {username}")
                logger.info(f"Distinguished Name: {entry['distinguishedName']}")
                
                # Verificar se o usuário está desabilitado
                is_disabled = bool(uac & 0x0002)
                logger.info(f"Usuário {username} está {'desabilitado' if is_disabled else 'habilitado'}")
                
                # Adicionar usuário se não for o administrador padrão
                if username and username.lower() != 'administrador':
                    users.append(username)
            except Exception as user_error:
                logger.error(f"Erro ao processar entrada de usuário: {str(user_error)}")
        
        logger.info(f"Encontrados {len(users)} usuários no domínio")
        
        if close_conn:
            conn.unbind()
            logger.info("Conexão LDAP fechada")
        
        return users
    except Exception as e:
        logger.error(f"Erro ao buscar usuários do domínio: {str(e)}")
        if close_conn and conn:
            try:
                conn.unbind()
                logger.info("Conexão LDAP fechada após erro")
            except:
                pass
        return []

def check_user_status(user_entry):
    """
    Verifica o status detalhado de um usuário no Active Directory.
    Retorna um dicionário com informações de status.
    """
    try:
        # Decodificar o userAccountControl
        uac = int(str(user_entry['userAccountControl']))
        
        # Definições de flags de status de usuário
        flags = {
            0x0001: "SCRIPT",
            0x0002: "ACCOUNTDISABLE",
            0x0008: "HOMEDIR_REQUIRED",
            0x0010: "LOCKOUT",
            0x0020: "NO_PASSWD_REQD",
            0x0040: "CAN_PASSWD_CHANGE",
            0x0080: "PASSWD_EXPIRES",
            0x0100: "PASSWD_CHANGE_ALLOWED",
            0x0200: "ENCRYPTED_TEXT_PWD_ALLOWED",
            0x0800: "INTERDOMAIN_TRUST_ACCOUNT",
            0x1000: "WORKSTATION_TRUST_ACCOUNT",
            0x2000: "SERVER_TRUST_ACCOUNT",
            0x10000: "DONT_EXPIRE_PASSWD",
            0x20000: "MNS_LOGON_ACCOUNT",
            0x40000: "SMARTCARD_REQUIRED",
            0x80000: "TRUSTED_FOR_DELEGATION",
            0x100000: "NOT_DELEGATED",
            0x200000: "USE_DES_KEY_ONLY",
            0x400000: "DONT_REQ_PREAUTH",
            0x800000: "PASSWORD_EXPIRED",
            0x1000000: "TRUSTED_TO_AUTH_FOR_DELEGATION",
            0x04000000: "PARTIAL_SECRETS_ACCOUNT"
        }
        
        # Verificar status do usuário
        status_details = {
            'is_enabled': not bool(uac & 0x0002),  # Usuário não está desabilitado
            'is_locked_out': bool(uac & 0x0010),   # Usuário está bloqueado
            'password_expired': bool(uac & 0x800000),  # Senha expirou
            'account_flags': []
        }
        
        # Adicionar flags ativas
        for flag_value, flag_name in flags.items():
            if uac & flag_value:
                status_details['account_flags'].append(flag_name)
        
        logger.info(f"Status do usuário: {status_details}")
        return status_details
    
    except Exception as e:
        logger.error(f"Erro ao verificar status do usuário: {str(e)}")
        return None

def verify_ad_password(username, password):
    """
    Verifica se as credenciais do usuário são válidas no AD.
    Tenta múltiplos formatos de nome de usuário.
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Lista de formatos de nome de usuário para tentar
        username_formats = [
            username,  # Nome de usuário original
            f"{username}@{LDAP_CONFIG['DOMAIN']}",  # UPN (User Principal Name)
            f"{LDAP_CONFIG['DOMAIN']}\\{username}",  # Formato de domínio
        ]
        
        for attempt_username in username_formats:
            logger.info(f"Tentando autenticar usuário: {attempt_username}")
            
            # Tenta conectar com as credenciais do usuário
            try:
                conn = get_ldap_connection(attempt_username, password)
                if not conn:
                    logger.warning(f"Falha na conexão com {attempt_username}")
                    continue
                
                # Pesquisa detalhes do usuário
                user_entry = search_user(conn, username)
                if not user_entry:
                    logger.error(f"Usuário {attempt_username} não encontrado")
                    conn.unbind()
                    continue
                
                # Verifica status do usuário
                user_status = check_user_status(user_entry)
                if not user_status:
                    logger.error("Não foi possível verificar o status do usuário")
                    conn.unbind()
                    continue
                
                # Log detalhado do status do usuário
                logger.info(f"Status do usuário {attempt_username}:")
                logger.info(f"Habilitado: {user_status['is_enabled']}")
                logger.info(f"Bloqueado: {user_status['is_locked_out']}")
                logger.info(f"Senha expirada: {user_status['password_expired']}")
                logger.info(f"Flags da conta: {user_status['account_flags']}")
                
                # Verifica condições de login
                if not user_status['is_enabled']:
                    logger.warning(f"Usuário {attempt_username} está desabilitado")
                    conn.unbind()
                    continue
                
                if user_status['is_locked_out']:
                    logger.warning(f"Usuário {attempt_username} está bloqueado")
                    conn.unbind()
                    continue
                
                if user_status['password_expired']:
                    logger.warning(f"Senha do usuário {attempt_username} expirou")
                    conn.unbind()
                    continue
                
                # Se chegou até aqui, as credenciais são válidas
                conn.unbind()
                return True
            
            except Exception as auth_error:
                logger.error(f"Erro ao autenticar {attempt_username}: {str(auth_error)}")
        
        # Se nenhum formato de usuário funcionou
        logger.error("Falha na autenticação para todos os formatos de usuário")
        return False
        
    except Exception as e:
        logger.error(f"Erro geral ao verificar senha no AD: {str(e)}")
        return False

def verify_ntlm_hash(ntlm_hash):
    """
    Verifica se o hash NTLM fornecido é válido.
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Verifica se o hash NTLM é válido
        nthash.verify(ntlm_hash, 'password')
        return True
        
    except Exception as e:
        logger.error(f"Erro ao verificar hash NTLM: {str(e)}")
        return False
