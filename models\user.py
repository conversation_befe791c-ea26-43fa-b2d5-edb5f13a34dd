import hashlib
import os
import hmac
from datetime import datetime
from utils.db import get_mysql_connection
import mysql.connector
import logging
import re
from typing import Optional, List, Dict, Any, Union

logging.basicConfig(level=logging.INFO)

def hash_password(password: str) -> str:
    """Gera um hash seguro para a senha usando PBKDF2 com SHA256"""
    if not password:
        raise ValueError("A senha não pode estar vazia")
        
    salt = os.urandom(32)
    key = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt,
        100000,  # Número de iterações aumentado para maior segurança
        dklen=128
    )
    return salt.hex() + key.hex()

def verify_password(stored_password: str, provided_password: str) -> bool:
    """Verifica se a senha fornecida corresponde ao hash armazenado"""
    if not stored_password or not provided_password:
        logging.warning("Tentativa de verificação com senha vazia")
        return False
        
    try:
        # Log para debug
        logging.info(f"Verificando senha. Hash armazenado tem {len(stored_password)} caracteres")
        logging.info(f"Primeiros 64 caracteres do hash (salt): {stored_password[:64]}")
        
        if len(stored_password) < 64:
            logging.error("Hash armazenado é muito curto")
            return False
            
        stored_salt = bytes.fromhex(stored_password[:64])
        stored_key = bytes.fromhex(stored_password[64:])
        
        key = hashlib.pbkdf2_hmac(
            'sha256',
            provided_password.encode('utf-8'),
            stored_salt,
            100000,
            dklen=128
        )
        
        # Comparação em tempo constante para prevenir timing attacks
        result = hmac.compare_digest(key, stored_key)
        logging.info(f"Resultado da verificação da senha: {result}")
        return result
        
    except Exception as e:
        logging.error(f"Erro ao verificar senha: {str(e)}")
        return False

class User:
    def __init__(self, id: Optional[int] = None, name: Optional[str] = None,
                 username: Optional[str] = None, password: Optional[str] = None,
                 is_admin: int = 0, first_login: int = 1, pa: Union[str, List[str]] = None,
                 is_active: int = 1, is_ad_user: int = 0, email: Optional[str] = None,
                 department: Optional[str] = None, updated_at: Optional[datetime] = None):
        self.id = id
        self.name = name
        self.username = username
        self.password = password
        self.is_admin = is_admin
        self.first_login = first_login
        self.pa = pa if isinstance(pa, list) else [pa] if pa else ['0']
        self.is_active = is_active
        self.is_ad_user = is_ad_user
        self.email = email
        self.department = department
        self.created_at = datetime.now()
        self.permissions: List[str] = []
        self.last_login: Optional[datetime] = None
        self.updated_at = updated_at
        self._validate()

    def _validate(self) -> None:
        """Valida os dados do usuário"""
        if self.name and (len(self.name) < 2 or len(self.name) > 255):
            raise ValueError("Nome deve ter entre 2 e 255 caracteres")
            
        if self.username:
            if len(self.username) < 3 or len(self.username) > 255:
                raise ValueError("Nome de usuário deve ter entre 3 e 255 caracteres")
            if not re.match("^[a-zA-Z0-9_@.-]+$", self.username):
                raise ValueError("Nome de usuário contém caracteres inválidos")
                
        valid_pas = ['0', '2', '3', '4', '5', '6', '7', '9', '10', '11', '12', 
                     '15', '16', '17', '18', '20', '21', '22', '23', '24', '25', '26', '97']
        for pa in self.pa:
            if str(pa) not in valid_pas:
                raise ValueError(f"PA inválido: {pa}")

    @staticmethod
    def get_by_id(user_id: int) -> Optional['User']:
        """Busca um usuário pelo ID"""
        if not isinstance(user_id, int) or user_id < 1:
            return None
            
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute('''
                SELECT id, name, username, password, is_admin, first_login,
                       is_active, is_ad_user, email, department
                FROM users WHERE id = %s
            ''', (user_id,))
            user_data = cursor.fetchone()
            
            if user_data:
                # Busca os PAs do usuário
                cursor.execute('SELECT pa FROM user_pas WHERE user_id = %s', (user_id,))
                pas = [row['pa'] for row in cursor.fetchall()]
                
                user = User(
                    id=user_data['id'],
                    name=user_data['name'],
                    username=user_data['username'],
                    password=user_data['password'],
                    is_admin=user_data['is_admin'],
                    first_login=user_data['first_login'],
                    pa=pas if pas else ['0'],
                    is_active=user_data['is_active'],
                    is_ad_user=user_data['is_ad_user'],
                    email=user_data['email'],
                    department=user_data['department']
                )
                
                cursor.execute('SELECT module FROM user_permissions WHERE user_id = %s', (user_id,))
                user.permissions = [row['module'] for row in cursor.fetchall()]
                
                return user
            return None
            
        except mysql.connector.Error as e:
            logging.error(f"Erro ao buscar usuário por ID: {str(e)}")
            return None
        finally:
            conn.close()

    @staticmethod
    def get_by_username(username: str) -> Optional['User']:
        """Busca um usuário pelo nome de usuário"""
        if not username or not isinstance(username, str):
            return None
            
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute('''
                SELECT id, name, username, password, is_admin, first_login,
                       is_active, is_ad_user, email, department
                FROM users WHERE username = %s
            ''', (username,))
            user_data = cursor.fetchone()
            
            if user_data:
                # Busca os PAs do usuário
                cursor.execute('SELECT pa FROM user_pas WHERE user_id = %s', (user_data['id'],))
                pas = [row['pa'] for row in cursor.fetchall()]
                
                user = User(
                    id=user_data['id'],
                    name=user_data['name'],
                    username=user_data['username'],
                    password=user_data['password'],
                    is_admin=user_data['is_admin'],
                    first_login=user_data['first_login'],
                    pa=pas if pas else ['0'],
                    is_active=user_data['is_active'],
                    is_ad_user=user_data['is_ad_user'],
                    email=user_data['email'],
                    department=user_data['department']
                )
                
                cursor.execute('SELECT module FROM user_permissions WHERE user_id = %s', (user_data['id'],))
                user.permissions = [row['module'] for row in cursor.fetchall()]
                
                return user
            return None
            
        except mysql.connector.Error as e:
            logging.error(f"Erro ao buscar usuário por username: {str(e)}")
            return None
        finally:
            conn.close()

    @staticmethod
    def get_all() -> List['User']:
        """Retorna todos os usuários"""
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute('''
                SELECT id, name, username, password, is_admin, first_login,
                       is_active, is_ad_user, email, department, updated_at
                FROM users ORDER BY username
            ''')
            users = []
            
            for user_data in cursor.fetchall():
                # Busca os PAs do usuário
                cursor.execute('SELECT pa FROM user_pas WHERE user_id = %s', (user_data['id'],))
                pas = [row['pa'] for row in cursor.fetchall()]
                
                user = User(
                    id=user_data['id'],
                    name=user_data['name'],
                    username=user_data['username'],
                    password=user_data['password'],
                    is_admin=user_data['is_admin'],
                    first_login=user_data['first_login'],
                    pa=pas if pas else ['0'],
                    is_active=user_data['is_active'],
                    is_ad_user=user_data['is_ad_user'],
                    email=user_data['email'],
                    department=user_data['department'],
                    updated_at=user_data['updated_at']
                )
                
                cursor.execute('SELECT module FROM user_permissions WHERE user_id = %s', (user.id,))
                user.permissions = [row['module'] for row in cursor.fetchall()]
                
                users.append(user)
            
            return users
            
        except mysql.connector.Error as e:
            logging.error(f"Erro ao listar usuários: {str(e)}")
            return []
        finally:
            conn.close()

    def save(self) -> bool:
        """Salva ou atualiza um usuário"""
        try:
            self._validate()
            conn = get_mysql_connection()
            cursor = conn.cursor(dictionary=True)
            
            logging.info(f"Salvando usuário {self.username}:")
            logging.info(f"Nome: {self.name}")
            logging.info(f"Is Admin: {self.is_admin}")
            logging.info(f"PAs: {self.pa}")
            logging.info(f"Permissões: {self.permissions}")
            
            if self.id:
                # Atualizar usuário existente
                cursor.execute('''
                    UPDATE users 
                    SET name = %s, username = %s, is_admin = %s, first_login = %s,
                        is_active = %s, is_ad_user = %s, email = %s, department = %s
                    WHERE id = %s
                ''', (
                    self.name, self.username, self.is_admin, self.first_login,
                    self.is_active, self.is_ad_user, self.email, self.department,
                    self.id
                ))
                
                if self.password:
                    cursor.execute('''
                        UPDATE users 
                        SET password = %s,
                            first_login = %s
                        WHERE id = %s
                    ''', (self.password, self.first_login, self.id))
                
                if self.last_login:
                    cursor.execute('''
                        UPDATE users 
                        SET last_login = %s
                        WHERE id = %s
                    ''', (self.last_login, self.id))
            else:
                # Criar novo usuário
                if not self.password and not self.is_ad_user:
                    raise ValueError("Senha é obrigatória para novos usuários locais")
                
                cursor.execute('''
                    INSERT INTO users (
                        name, username, password, is_admin, first_login,
                        is_active, is_ad_user, email, department
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    self.name, self.username, self.password, self.is_admin,
                    self.first_login, self.is_active, self.is_ad_user,
                    self.email, self.department
                ))
                self.id = cursor.lastrowid
            
            # Atualiza os PAs
            cursor.execute('DELETE FROM user_pas WHERE user_id = %s', (self.id,))
            for pa in self.pa:
                cursor.execute('INSERT INTO user_pas (user_id, pa) VALUES (%s, %s)', 
                             (self.id, pa))
            
            # Atualiza as permissões
            cursor.execute('DELETE FROM user_permissions WHERE user_id = %s', (self.id,))
            for module in self.permissions:
                cursor.execute('INSERT INTO user_permissions (user_id, module) VALUES (%s, %s)',
                             (self.id, module))
                logging.info(f"Adicionada permissão {module} para usuário {self.username}")
            
            conn.commit()
            return True
            
        except Exception as e:
            logging.error(f"Erro ao salvar usuário: {str(e)}")
            if 'conn' in locals():
                conn.rollback()
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def delete(self) -> bool:
        """Exclui um usuário"""
        if not self.id:
            return False
            
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        try:
            # A exclusão dos PAs e permissões é automática devido à chave estrangeira com CASCADE
            cursor.execute('DELETE FROM users WHERE id = %s', (self.id,))
            conn.commit()
            return True
            
        except mysql.connector.Error as e:
            logging.error(f"Erro ao excluir usuário: {str(e)}")
            conn.rollback()
            return False

    def has_permission(self, module: str) -> bool:
        """Verifica se o usuário tem permissão para um módulo"""
        if self.is_admin:
            return True  # Administradores têm acesso a todos os módulos
        return module in self.permissions

    def check_password(self, password: str) -> bool:
        """Verifica se a senha fornecida está correta"""
        try:
            if not password:
                logging.warning("Tentativa de verificação com senha vazia")
                return False
            
            if not self.password:
                logging.error("Usuário não possui hash de senha armazenado")
                return False
            
            # Log para debug
            logging.info(f"Verificando senha para usuário {self.username}")
            logging.info(f"Tamanho do hash armazenado: {len(self.password)} caracteres")
            logging.info(f"Hash armazenado: {self.password[:64]}... (primeiros 64 caracteres)")
            
            result = verify_password(self.password, password)
            logging.info(f"Resultado da verificação: {result}")
            return result
        
        except Exception as e:
            logging.error(f"Erro ao verificar senha do usuário {self.username}: {str(e)}")
            return False

    def set_password(self, password: str) -> None:
        """Define uma nova senha para o usuário"""
        if not password:
            raise ValueError("A senha não pode estar vazia")
            
        # Validação de força da senha
        if len(password) < 8:
            raise ValueError("A senha deve ter pelo menos 8 caracteres. Por favor, escolha uma senha mais longa.")
        if not re.search("[a-z]", password):
            raise ValueError("A senha deve conter pelo menos uma letra minúscula. Por favor, adicione uma letra minúscula.")
        if not re.search("[A-Z]", password):
            raise ValueError("A senha deve conter pelo menos uma letra maiúscula. Por favor, adicione uma letra maiúscula.")
        if not re.search("[0-9]", password):
            raise ValueError("A senha deve conter pelo menos um número. Por favor, adicione um número.")
        if not re.search("[!@#$%^&*(),.?\":{}|<>]", password):
            raise ValueError("A senha deve conter pelo menos um caractere especial (!@#$%^&*(),.?\":{}|<>). Por favor, adicione um caractere especial.")
            
        self.password = hash_password(password)

    def to_dict(self) -> Dict[str, Any]:
        """Converte o usuário para um dicionário"""
        return {
            'id': self.id,
            'name': self.name,
            'username': self.username,
            'is_admin': self.is_admin,
            'first_login': self.first_login,
            'pa': self.pa,
            'permissions': self.permissions
        }

    def update(self, name: str, username: str, password: Optional[str] = None,
               is_admin: bool = False, permissions: List[str] = None, 
               pas: List[str] = None) -> bool:
        """Atualiza um usuário existente"""
        try:
            self.name = name
            self.username = username
            self.is_admin = 1 if is_admin else 0
            self.permissions = permissions or []
            self.pa = pas if pas else ['0']
            
            if password:
                # Garante que a senha seja hasheada antes de salvar
                self.set_password(password)
                self.first_login = 1
            
            success = self.save()
            if not success:
                raise ValueError("Erro ao salvar usuário no banco de dados")
            
            return True
        except Exception as e:
            logging.error(f"Erro ao atualizar usuário: {str(e)}")
            return False

    @staticmethod
    def create(name: str, username: str, password: str, is_admin: bool = False, 
               permissions: List[str] = None, pas: List[str] = None) -> Optional['User']:
        """Cria um novo usuário"""
        user = User(
            name=name,
            username=username,
            is_admin=1 if is_admin else 0,
            pa=pas if pas else ['0']
        )
        user.set_password(password)
        user.permissions = permissions or []
        
        if user.save():
            return user
        return None

    @staticmethod
    def authenticate(username: str, password: str) -> Optional['User']:
        """Autentica um usuário"""
        if not username or not password:
            return None
            
        user = User.get_by_username(username)
        if user and user.check_password(password):
            return user
        return None

def init_db() -> None:
    """Inicializa o banco de dados"""
    conn = get_mysql_connection()
    cursor = conn.cursor()
    
    try:
        # Cria a tabela de usuários se não existir
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                username VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(512) NOT NULL,
                is_admin TINYINT(1) DEFAULT 0,
                first_login TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY uk_username (username)
            )
        ''')
        
        # Cria a tabela de permissões se não existir
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                module VARCHAR(50) NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY uk_user_module (user_id, module)
            )
        ''')
        
        # Cria a tabela de PAs se não existir
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_pas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                pa VARCHAR(10) NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY uk_user_pa (user_id, pa)
            )
        ''')
        
        conn.commit()
        
    except mysql.connector.Error as e:
        logging.error(f"Erro ao inicializar banco de dados: {str(e)}")
        raise
    finally:
        conn.close()

@staticmethod
def delete(user_id: int) -> bool:
    """Exclui um usuário"""
    if not isinstance(user_id, int) or user_id < 1:
        return False
        
    conn = get_mysql_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM users WHERE id = %s', (user_id,))
        conn.commit()
        return cursor.rowcount > 0
    except mysql.connector.Error as e:
        logging.error(f"Erro ao excluir usuário: {str(e)}")
        return False
    finally:
        conn.close()
