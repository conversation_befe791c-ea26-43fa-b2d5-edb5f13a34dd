import os
import sys

# Adiciona o diretório raiz ao PYTHONPATH
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_dir)

from utils.db import get_mysql_connection
import logging

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def execute_migration():
    """Executa a migração para adicionar a coluna receita na tabela associados"""
    conn = None
    cursor = None
    
    try:
        # Lê o arquivo SQL
        migration_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            'config',
            'db_migrations',
            'add_receita_column.sql'
        )
        
        with open(migration_file, 'r') as f:
            sql_commands = f.read()
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Executa os comandos SQL
        for command in sql_commands.split(';'):
            if command.strip():
                cursor.execute(command)
                logging.info(f"Comando SQL executado: {command.strip()}")
        
        # Commit das alterações
        conn.commit()
        logging.info("Migração da coluna receita executada com sucesso")
        
        # Verifica se a coluna foi criada
        cursor.execute("SHOW COLUMNS FROM associados LIKE 'receita'")
        if cursor.fetchone():
            logging.info("Coluna 'receita' criada com sucesso!")
        else:
            logging.error("Erro: Coluna 'receita' não foi criada!")
        
    except Exception as e:
        logging.error(f"Erro durante a migração da coluna receita: {str(e)}")
        if conn:
            conn.rollback()
        raise
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == '__main__':
    execute_migration() 