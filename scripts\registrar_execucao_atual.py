import mysql.connector
from datetime import datetime
import logging
import json
import os
import requests

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Conectando ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        logger.info("Conexão estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def get_rpas_config():
    try:
        with open('data/rpas.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Erro ao ler arquivo de configuração dos RPAs: {e}")
        return []

def check_rpa_status(host, port):
    try:
        url = f"http://{host}:{port}/status"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return True
        return False
    except:
        return False

def get_running_rpa():
    rpas = get_rpas_config()
    for rpa in rpas:
        if check_rpa_status(rpa['host'], rpa['port']):
            return rpa
    return None

def registrar_execucao_atual():
    try:
        # Busca RPA em execução
        rpa_atual = get_running_rpa()
        if not rpa_atual:
            logger.warning("Nenhum RPA encontrado em execução!")
            return

        logger.info(f"RPA encontrado em execução: {rpa_atual['name']}")

        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Verifica se já existe uma execução em andamento
        logger.info("Verificando execuções em andamento...")
        cursor.execute("""
            SELECT id, rpa_name, start_time 
            FROM rpa_executions 
            WHERE status = 'running'
        """)
        
        execucao_atual = cursor.fetchone()
        
        if execucao_atual:
            logger.info(f"""
Já existe uma execução em andamento:
ID: {execucao_atual[0]}
RPA: {execucao_atual[1]}
Início: {execucao_atual[2]}
""")
            return
        
        # Se não existe execução em andamento, registra uma nova
        now = datetime.now()
        logger.info("Registrando nova execução...")
        
        cursor.execute("""
            INSERT INTO rpa_executions 
            (rpa_name, start_time, status, active_time, last_status_update)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            rpa_atual['name'],  # Nome do RPA da configuração
            now,               # Hora atual como início
            'running',         # Status inicial
            0,                # Tempo ativo inicial
            now               # Última atualização
        ))
        
        execution_id = cursor.lastrowid
        
        # Registra o início no histórico
        cursor.execute("""
            INSERT INTO rpa_execution_history 
            (execution_id, status, start_time)
            VALUES (%s, %s, %s)
        """, (
            execution_id,
            'running',
            now
        ))
        
        conn.commit()
        logger.info(f"""
Execução registrada com sucesso!
ID: {execution_id}
RPA: {rpa_atual['name']}
Host: {rpa_atual['host']}
Início: {now}
""")

    except Exception as e:
        logger.error(f"Erro ao registrar execução: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    registrar_execucao_atual() 