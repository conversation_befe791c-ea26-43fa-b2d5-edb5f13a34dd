<?php
echo "Iniciando configuração do LDAP...\n\n";

// Verifica se está rodando como administrador
if (!isRunAsAdministrator()) {
    die("Este script precisa ser executado como administrador!\n");
}

// Encontra o php.ini
$php_ini_path = php_ini_loaded_file();
if (!$php_ini_path) {
    die("Não foi possível encontrar o php.ini!\n");
}

echo "PHP.ini encontrado em: " . $php_ini_path . "\n";

// Lê o conteúdo do php.ini
$php_ini_content = file_get_contents($php_ini_path);
if ($php_ini_content === false) {
    die("Não foi possível ler o php.ini!\n");
}

// Verifica se o LDAP já está habilitado
if (strpos($php_ini_content, 'extension=ldap') !== false && strpos($php_ini_content, ';extension=ldap') === false) {
    echo "LDAP já está habilitado no php.ini\n";
} else {
    // Habilita o LDAP
    $php_ini_content = str_replace(';extension=ldap', 'extension=ldap', $php_ini_content);
    
    // Salva as alterações
    if (file_put_contents($php_ini_path, $php_ini_content) === false) {
        die("Não foi possível modificar o php.ini!\n");
    }
    echo "LDAP habilitado no php.ini\n";
}

// Verifica os arquivos DLL necessários
$xampp_path = dirname(dirname($php_ini_path)); // Pasta raiz do XAMPP
$php_ext_path = $xampp_path . '/php/ext';
$apache_bin_path = $xampp_path . '/apache/bin';

// Lista de arquivos necessários
$required_files = [
    ['source' => $xampp_path . '/php/libeay32.dll', 'dest' => $apache_bin_path . '/libeay32.dll'],
    ['source' => $xampp_path . '/php/ssleay32.dll', 'dest' => $apache_bin_path . '/ssleay32.dll'],
    ['source' => 'https://windows.php.net/downloads/pecl/releases/ldap/latest/php_ldap.dll', 'dest' => $php_ext_path . '/php_ldap.dll']
];

// Verifica e copia/baixa os arquivos necessários
foreach ($required_files as $file) {
    echo "\nVerificando " . basename($file['dest']) . "...\n";
    
    if (file_exists($file['dest'])) {
        echo "Arquivo já existe em " . $file['dest'] . "\n";
        continue;
    }
    
    if (strpos($file['source'], 'http') === 0) {
        // Download do arquivo
        echo "Baixando " . basename($file['dest']) . "...\n";
        $content = @file_get_contents($file['source']);
        if ($content === false) {
            echo "AVISO: Não foi possível baixar " . basename($file['dest']) . "\n";
            continue;
        }
        if (file_put_contents($file['dest'], $content) === false) {
            echo "AVISO: Não foi possível salvar " . $file['dest'] . "\n";
            continue;
        }
    } else {
        // Copia local
        if (!file_exists($file['source'])) {
            echo "AVISO: Arquivo fonte não encontrado: " . $file['source'] . "\n";
            continue;
        }
        if (!copy($file['source'], $file['dest'])) {
            echo "AVISO: Não foi possível copiar para " . $file['dest'] . "\n";
            continue;
        }
    }
    echo "Arquivo copiado com sucesso para " . $file['dest'] . "\n";
}

echo "\nConfigurações concluídas!\n";
echo "Por favor, reinicie o servidor PHP para aplicar as alterações.\n";

// Função para verificar se está rodando como administrador
function isRunAsAdministrator() {
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows
        $command = 'net session 2>&1';
        $output = [];
        exec($command, $output, $status);
        return $status === 0;
    } else {
        // Linux/Unix
        return posix_getuid() === 0;
    }
} 