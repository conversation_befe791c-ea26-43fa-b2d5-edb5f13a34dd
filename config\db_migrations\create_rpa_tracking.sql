-- Cria tabela para rastrear RPAs de forma simplificada
CREATE TABLE IF NOT EXISTS rpa_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rpa_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Nome único do RPA',
    total_active_time INT DEFAULT 0 COMMENT 'Tempo total ativo em segundos',
    last_start_time DATETIME DEFAULT NULL COMMENT 'In<PERSON>cio da última execução',
    last_end_time DATETIME DEFAULT NULL COMMENT 'Fim da última execução',
    current_status VARCHAR(50) DEFAULT NULL COMMENT 'Status atual (running, completed, error, stopped)',
    last_status_update DATETIME DEFAULT NULL COMMENT 'Última atualização de status',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rpa_name (rpa_name),
    INDEX idx_current_status (current_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insere os RPAs conhecidos
INSERT INTO rpa_tracking (rpa_name) VALUES 
('ATUALIZAÇÃO CADASTRAL'),
('APROVAR')
ON DUPLICATE KEY UPDATE rpa_name = VALUES(rpa_name); 