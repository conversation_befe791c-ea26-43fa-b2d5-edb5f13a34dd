import os
import re
import hashlib
import secrets
import sqlite3
import json

class UserManager:
    def __init__(self, db_path=None):
        """
        Inicializa o gerenciamento de usuários com banco de dados SQLite.
        """
        if db_path is None:
            db_path = os.path.join(os.path.dirname(__file__), 'users.db')
        
        self.db_path = db_path
        self._create_database()

    def _create_database(self):
        """
        Cria o banco de dados e tabelas necessárias se não existirem.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Cria tabela de usuários se não existir
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    username TEXT PRIMARY KEY,
                    full_name TEXT,
                    password_hash TEXT,
                    salt TEXT,
                    is_admin BOOLEAN DEFAULT 0,
                    is_first_login BOOLEAN DEFAULT 1,
                    permissions TEXT DEFAULT '["cpfs"]',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    added_by TEXT
                )
            ''')
            conn.commit()
            
            # Cria usuário administrador padrão se não existir
            self.create_default_admin()

    def _hash_password(self, password, salt=None):
        """
        Gera hash de senha com salt.
        """
        if not salt:
            salt = secrets.token_hex(16)
        
        # Concatena a senha com o salt e gera hash
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        
        return password_hash, salt

    def validate_password(self, password):
        """
        Valida se a senha atende aos requisitos mínimos.
        """
        # Verifica o comprimento da senha
        if len(password) < 8:
            return False, "Senha deve ter pelo menos 8 caracteres"
        
        # Verifica se contém letra maiúscula
        if not re.search(r'[A-Z]', password):
            return False, "Senha deve conter pelo menos uma letra maiúscula"
        
        # Verifica se contém letra minúscula
        if not re.search(r'[a-z]', password):
            return False, "Senha deve conter pelo menos uma letra minúscula"
        
        # Verifica se contém caractere especial
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "Senha deve conter pelo menos um caractere especial"
        
        return True, "Senha válida"

    def add_user(self, username, full_name, password, is_admin=False, is_first_login=True, added_by=None):
        """
        Adiciona um novo usuário ao sistema.
        """
        print(f"[DEBUG] Tentando adicionar usuário: {username}")
        print(f"[DEBUG] Detalhes - Nome Completo: {full_name}, Admin: {is_admin}, Primeiro Login: {is_first_login}, Adicionado Por: {added_by}")
        
        # Valida a nova senha
        if password:
            password_valid, msg = self.validate_password(password)
            if not password_valid:
                print(f"[DEBUG] Erro de validação de senha: {msg}")
                return False, msg
        else:
            # Se não foi definida senha, gera uma temporária
            password = self.generate_temporary_password()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Verifica se o usuário já existe
                cursor.execute('SELECT 1 FROM users WHERE username = ?', (username,))
                if cursor.fetchone():
                    print(f"[DEBUG] Usuário {username} já existe")
                    return False, "Usuário já existe"
                
                # Gera hash de senha
                password_hash, salt = self._hash_password(password)
                
                # Converte is_admin para inteiro (0 ou 1)
                is_admin_int = 1 if is_admin else 0
                
                # Insere o novo usuário
                cursor.execute('''
                    INSERT INTO users 
                    (username, full_name, password_hash, salt, is_admin, is_first_login, added_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (username, full_name, password_hash, salt, is_admin_int, is_first_login, added_by))
                
                conn.commit()
                print(f"[DEBUG] Usuário {username} adicionado com sucesso")
                return True, "Usuário adicionado com sucesso"
        
        except sqlite3.Error as e:
            print(f"[DEBUG] Erro ao adicionar usuário: {str(e)}")
            return False, f"Erro ao adicionar usuário: {str(e)}"

    def is_admin(self, username):
        """
        Verifica se um usuário é administrador.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT is_admin FROM users WHERE username = ?', (username,))
                result = cursor.fetchone()
                
                return bool(result[0]) if result else False
        
        except sqlite3.Error:
            return False

    def user_exists(self, username):
        """
        Verifica se um usuário já existe.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT 1 FROM users WHERE username = ?', (username,))
                return cursor.fetchone() is not None
        
        except sqlite3.Error:
            return False

    def is_first_login(self, username):
        """
        Verifica se é o primeiro login do usuário.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT is_first_login FROM users WHERE username = ?', (username,))
                result = cursor.fetchone()
                
                return bool(result[0]) if result else True
        
        except sqlite3.Error:
            return True

    def update_user_permissions(self, admin_username, username, modules=None, is_admin=None):
        """
        Atualiza permissões de um usuário.
        Apenas administradores podem fazer isso.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Verifica se o usuário existe
                cursor.execute('SELECT 1 FROM users WHERE username = ?', (username,))
                if not cursor.fetchone():
                    return False, "Usuário não encontrado"
                
                # Atualiza módulos
                if modules is not None:
                    cursor.execute('''
                        UPDATE users 
                        SET permissions = ?, is_first_login = 0 
                        WHERE username = ?
                    ''', (json.dumps(modules), username))
                
                # Atualiza status de administrador
                if is_admin is not None:
                    is_admin_int = 1 if is_admin else 0
                    cursor.execute('''
                        UPDATE users 
                        SET is_admin = ? 
                        WHERE username = ?
                    ''', (is_admin_int, username))
                
                conn.commit()
                return True, "Permissões atualizadas com sucesso"
        
        except sqlite3.Error as e:
            return False, f"Erro ao atualizar permissões: {str(e)}"

    def create_default_admin(self):
        """
        Cria um usuário administrador padrão se não existir.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Verifica se já existe um usuário administrador
                cursor.execute('SELECT 1 FROM users WHERE username = ?', ('administrador',))
                if not cursor.fetchone():
                    # Gera uma senha temporária forte
                    temp_password = self.generate_temporary_password()
                    
                    # Gera hash de senha
                    password_hash, salt = self._hash_password(temp_password)
                    
                    # Insere o usuário administrador
                    cursor.execute('''
                        INSERT INTO users 
                        (username, full_name, password_hash, salt, is_admin, is_first_login, permissions) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        'administrador', 
                        'Administrador do Sistema', 
                        password_hash, 
                        salt, 
                        1,  # is_admin 
                        1,  # is_first_login
                        json.dumps(list(AVAILABLE_MODULES.keys()))  # Todos os módulos
                    ))
                    
                    conn.commit()
                    print(f"[ADMIN] Usuário administrador criado. Senha temporária: {temp_password}")
        
        except sqlite3.Error as e:
            print(f"[ADMIN] Erro ao criar usuário administrador: {str(e)}")

    def generate_temporary_password(self):
        """
        Gera uma senha temporária forte.
        """
        # Gera uma senha com 16 caracteres, incluindo letras maiúsculas, 
        # minúsculas, números e caracteres especiais
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+'
        return ''.join(secrets.choice(chars) for _ in range(16))

# Módulos disponíveis no sistema
AVAILABLE_MODULES = {
    'cpfs': 'Consulta de CPFs',
    'rpa_monitor': 'Monitoramento de RPAs',
    'dashboard': 'Painel Principal'
}

# Cria uma instância global para facilitar importação
user_manager = UserManager()
user_manager.create_default_admin()
