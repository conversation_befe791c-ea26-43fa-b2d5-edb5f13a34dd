import os
import sys

# Adiciona o diretório raiz ao PYTHONPATH
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_dir)

from utils.db import get_mysql_connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_table():
    conn = None
    cursor = None
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se a coluna receita já existe
        cursor.execute("SHOW COLUMNS FROM associados LIKE 'receita'")
        receita_exists = cursor.fetchone() is not None
        
        if receita_exists:
            logger.info("A coluna 'receita' já existe na tabela")
        else:
            logger.info("A coluna 'receita' não existe na tabela")
            
        # Mostra todas as colunas
        cursor.execute("DESCRIBE associados")
        columns = cursor.fetchall()
        logger.info("Estrutura atual da tabela:")
        for column in columns:
            logger.info(f"Coluna: {column[0]}, Tipo: {column[1]}")
            
    except Exception as e:
        logger.error(f"Erro ao verificar tabela: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    check_table() 