from utils.db import get_mysql_connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_rpa_time():
    """
    Atualiza o tempo total do RPA bot.exe para incluir o histórico anterior
    """
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Atualiza o tempo total para o RPA bot.exe
        cursor.execute("""
            UPDATE rpa_tracking 
            SET total_active_time = 417998
            WHERE rpa_name = 'bot.exe'
        """)
        
        conn.commit()
        
        # Verifica se a atualização foi feita
        cursor.execute("""
            SELECT total_active_time 
            FROM rpa_tracking 
            WHERE rpa_name = 'bot.exe'
        """)
        
        result = cursor.fetchone()
        if result:
            total_time = result[0]
            hours = int(total_time // 3600)
            minutes = int((total_time % 3600) // 60)
            seconds = int(total_time % 60)
            
            logger.info(f"""
Tempo do RPA bot.exe atualizado com sucesso!
Tempo total: {total_time} segundos
Equivalente a: {hours:02d}:{minutes:02d}:{seconds:02d}
""")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Erro ao atualizar tempo do RPA: {e}")

if __name__ == '__main__':
    update_rpa_time() 