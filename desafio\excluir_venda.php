<?php
session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// Verifica se o usuário está autenticado
check_auth();

// Verifica se o ID foi fornecido
if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$venda_id = $_GET['id'];
$is_admin = isset($_GET['admin']) && $_GET['admin'] === '1';

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Busca a venda
    $stmt = $pdo->prepare("SELECT usuario, pa FROM campanha_vendas WHERE id = ?");
    $stmt->execute([$venda_id]);
    $venda = $stmt->fetch();

    if (!$venda) {
        throw new Exception("Venda não encontrada.");
    }

    // Verifica se o usuário tem permissão para excluir a venda
    if (!$is_admin && $venda['usuario'] !== $_SESSION['user_id']) {
        throw new Exception("Você não tem permissão para excluir esta venda.");
    }

    // Exclui a venda
    $stmt = $pdo->prepare("DELETE FROM campanha_vendas WHERE id = ?");
    $stmt->execute([$venda_id]);

    // Confirma a transação
    $pdo->commit();

    // Redireciona com mensagem de sucesso
    $_SESSION['success_message'] = "Venda excluída com sucesso!";
    header('Location: ' . ($is_admin ? 'admin/vendas.php' : 'minhas_vendas.php'));
    exit();

} catch (Exception $e) {
    // Desfaz a transação em caso de erro
    $pdo->rollBack();
    
    // Redireciona com mensagem de erro
    $_SESSION['error_message'] = $e->getMessage();
    header('Location: ' . ($is_admin ? 'admin/vendas.php' : 'minhas_vendas.php'));
    exit();
} 