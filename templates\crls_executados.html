{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center mb-3">
                    <div class="page-icon-modern bg-sicoob-azul me-3">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div>
                        <h1 class="h3 mb-0 text-sicoob-verde-escuro">CRLs Executados</h1>
                        <p class="text-muted mb-0">Acompanhe as CRLs executadas, com anotação impeditiva e erros</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Controles Modernos -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="control-group-modern">
                <label class="control-label-modern">Itens por página:</label>
                <select class="form-select-modern" id="perPageSelect" onchange="changePerPage(this.value)">
                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                    <option value="500" {% if per_page == 500 %}selected{% endif %}>500</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stats-summary-modern">
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-sicoob-azul">{{ total_executados }}</span>
                    <span class="stat-label-modern">Executados</span>
                </div>
                <div class="stat-divider-modern"></div>
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-sicoob-cinza">{{ total_impeditivos }}</span>
                    <span class="stat-label-modern">Impeditivos</span>
                </div>
                <div class="stat-divider-modern"></div>
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-sicoob-roxo">{{ total_erros }}</span>
                    <span class="stat-label-modern">Erros</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Abas Modernas -->
    <div class="tabs-container-modern">
        <ul class="nav nav-tabs-modern" id="crlTabs" role="tablist">
            <li class="nav-item-modern" role="presentation">
                <button class="nav-link-modern active" id="executados-tab" data-bs-toggle="tab" data-bs-target="#executados" type="button" role="tab">
                    <div class="tab-content-modern">
                        <i class="bi bi-check-circle tab-icon-modern"></i>
                        <span class="tab-text-modern">Executados</span>
                        <span class="badge-modern bg-sicoob-azul">{{ total_executados }}</span>
                    </div>
                </button>
            </li>
            <li class="nav-item-modern" role="presentation">
                <button class="nav-link-modern" id="impeditivos-tab" data-bs-toggle="tab" data-bs-target="#impeditivos" type="button" role="tab">
                    <div class="tab-content-modern">
                        <i class="bi bi-exclamation-triangle tab-icon-modern"></i>
                        <span class="tab-text-modern">Impeditivos</span>
                        <span class="badge-modern bg-sicoob-cinza">{{ total_impeditivos }}</span>
                    </div>
                </button>
            </li>
            <li class="nav-item-modern" role="presentation">
                <button class="nav-link-modern" id="erros-tab" data-bs-toggle="tab" data-bs-target="#erros" type="button" role="tab">
                    <div class="tab-content-modern">
                        <i class="bi bi-x-circle tab-icon-modern"></i>
                        <span class="tab-text-modern">Erros</span>
                        <span class="badge-modern bg-sicoob-roxo">{{ total_erros }}</span>
                    </div>
                </button>
            </li>
        </ul>
    </div>

    <style>
    /* Cores da Identidade Visual */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
        --sicoob-verde-claro-rgb: 201, 210, 0;
        --sicoob-verde-escuro-rgb: 125, 182, 28;
    }

    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* Header Moderno */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(var(--sicoob-verde-claro-rgb), 0.05) 0%, rgba(var(--sicoob-verde-escuro-rgb), 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(var(--sicoob-verde-claro-rgb), 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    /* Controles Modernos */
    .control-group-modern {
        display: flex;
        align-items: center;
        gap: 12px;
        background: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .control-label-modern {
        font-weight: 600;
        color: #495057;
        margin: 0;
        white-space: nowrap;
    }

    .form-select-modern {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        min-width: 80px;
    }

    .form-select-modern:focus {
        border-color: var(--sicoob-azul);
        box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        background-color: white;
    }

    /* Estatísticas Modernas */
    .stats-summary-modern {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
        background: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .stat-item-modern {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .stat-number-modern {
        font-size: 24px;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 4px;
    }

    .stat-label-modern {
        font-size: 12px;
        font-weight: 500;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-divider-modern {
        width: 1px;
        height: 40px;
        background: linear-gradient(to bottom, transparent, #e9ecef, transparent);
    }

    /* Abas Modernas */
    .tabs-container-modern {
        margin-bottom: 24px;
    }

    .nav-tabs-modern {
        border: none;
        display: flex;
        gap: 8px;
        background: #f8f9fa;
        padding: 6px;
        border-radius: 12px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    }

    .nav-item-modern {
        flex: 1;
    }

    .nav-link-modern {
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        background: transparent;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .nav-link-modern:hover {
        background: rgba(0, 174, 157, 0.1);
        transform: translateY(-1px);
    }

    .nav-link-modern.active {
        background: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .tab-content-modern {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-weight: 600;
    }

    .tab-icon-modern {
        font-size: 16px;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .tab-text-modern {
        color: #495057;
        transition: color 0.3s ease;
    }

    .nav-link-modern.active .tab-icon-modern {
        color: var(--sicoob-azul);
    }

    .nav-link-modern.active .tab-text-modern {
        color: #212529;
    }

    .badge-modern {
        font-size: 11px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
        color: white;
        min-width: 24px;
        text-align: center;
    }

    /* Tabelas Modernas */
    .table-responsive {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;
        overflow: hidden;
    }

    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        padding: 16px 20px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
    }

    .table tbody td {
        padding: 16px 20px;
        border-top: 1px solid #f1f3f4;
        color: #495057;
        font-weight: 500;
        vertical-align: middle;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(0, 174, 157, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    /* Filtros Modernos */
    .column-filter {
        position: relative;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .filter-icon {
        cursor: pointer;
        color: #6c757d;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 6px;
        background: rgba(108, 117, 125, 0.1);
    }

    .filter-icon:hover {
        background: rgba(0, 174, 157, 0.2);
        color: var(--sicoob-azul);
    }

    .filter-icon.has-filter {
        background: rgba(0, 174, 157, 0.2);
        color: var(--sicoob-azul);
    }

    .clear-filter {
        cursor: pointer;
        display: none;
        color: #dc3545;
        font-size: 12px;
    }

    .filter-icon.has-filter .clear-filter {
        display: inline-block;
    }

    .filter-input {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        z-index: 1000;
        display: none;
        padding: 12px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        min-width: 220px;
    }

    .filter-input.show {
        display: block !important;
        animation: filterSlideIn 0.3s ease;
    }

    @keyframes filterSlideIn {
        from {
            opacity: 0;
            transform: translateY(-8px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .filter-input input {
        width: 100%;
        padding: 10px 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-input input:focus {
        border-color: var(--sicoob-azul);
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        background-color: white;
    }

    .filter-input input::placeholder {
        color: #adb5bd;
        font-weight: 400;
    }

    /* Botões de Ação Modernos */
    .btn-action-modern {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-action-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-action-modern:hover::before {
        opacity: 1;
    }

    .btn-action-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-warning.btn-action-modern {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .btn-action-modern:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .btn-action-modern:disabled:hover {
        transform: none;
        box-shadow: none;
    }

    /* Paginação Moderna */
    .pagination {
        gap: 4px;
    }

    .page-link {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        margin: 0;
    }

    .page-link:hover {
        background: rgba(0, 174, 157, 0.1);
        border-color: var(--sicoob-azul);
        color: var(--sicoob-azul);
        transform: translateY(-1px);
    }

    .page-item.active .page-link {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
    }

    /* Informações de Paginação */
    .pagination-info-modern {
        background: white;
        padding: 12px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        color: #6c757d;
        font-weight: 500;
        font-size: 14px;
    }

    /* Filtros de Data Modernos */
    .filter-controls-modern {
        background: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        margin-bottom: 20px;
    }

    .btn-filter-modern {
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
        background: #f8f9fa;
        color: #495057;
    }

    .btn-filter-modern:hover {
        background: rgba(0, 174, 157, 0.1);
        border-color: var(--sicoob-azul);
        color: var(--sicoob-azul);
        transform: translateY(-1px);
    }

    .btn-filter-modern.active {
        background: var(--sicoob-azul);
        border-color: var(--sicoob-azul);
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .stats-summary-modern {
            flex-direction: column;
            gap: 12px;
        }

        .stat-divider-modern {
            width: 100%;
            height: 1px;
        }

        .control-group-modern {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
        }
    }
    </style>

    <!-- Conteúdo das Abas -->
    <div class="tab-content mt-3" id="crlTabsContent">
        <!-- Aba Executados -->
        <div class="tab-pane fade show active" id="executados" role="tabpanel">
            <!-- Controles de Filtro -->
            <div class="filter-controls-modern">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-filter-modern" onclick="showDataFilterModal()">
                        <i class="bi bi-funnel me-2"></i>
                        <span class="filtro-data-status">Filtrar por Data</span>
                    </button>
                    <button class="btn btn-filter-modern" onclick="filtrarHoje()" title="Filtrar data de hoje">
                        <i class="bi bi-calendar-event me-2"></i>
                        Hoje
                    </button>
                    <button class="btn btn-filter-modern d-none" id="limparFiltroBtn" onclick="limparFiltroData()">
                        <i class="bi bi-x-lg me-2"></i>
                        Limpar
                    </button>
                </div>
            </div>

            <!-- Paginação Superior -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_executados|length }} de {{ total_executados }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page) }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page) }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page) }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Tabela Executados -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="column-filter">
                                    CPF
                                    <div class="filter-icon" data-column="cpf">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar CPF..." data-column="cpf">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    PA
                                    <div class="filter-icon" data-column="pa">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar PA..." data-column="pa">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    Nome
                                    <div class="filter-icon" data-column="nome">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar Nome..." data-column="nome">
                                    </div>
                                </div>
                            </th>
                            <th>Data Execução</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for crl in crls_executados %}
                        <tr>
                            <td>{{ crl['cpf'][:3] }}.{{ crl['cpf'][3:6] }}.{{ crl['cpf'][6:9] }}-{{ crl['cpf'][9:] }}</td>
                            <td>{{ crl['pa'] }}</td>
                            <td>{{ crl['nome'] if crl['nome'] else '-' }}</td>
                            <td>{{ crl['data_crl'].strftime('%d/%m/%Y %H:%M:%S') if crl['data_crl'] else '-' }}</td>
                            <td>
                                <span class="badge bg-success">Executado</span>
                            </td>
                            <td>
                                <button class="btn btn-warning btn-action-modern" onclick="reprocessarCRL('{{ crl['cpf'] }}')" title="Reprocessar CRL">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Inferior -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_executados|length }} de {{ total_executados }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page) }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page) }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page) }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Aba Impeditivos -->
        <div class="tab-pane fade" id="impeditivos" role="tabpanel">
            <!-- Controles de Filtro -->
            <div class="filter-controls-modern">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-filter-modern" onclick="showDataFilterModal()">
                        <i class="bi bi-funnel me-2"></i>
                        <span class="filtro-data-status">Filtrar por Data</span>
                    </button>
                    <button class="btn btn-filter-modern" onclick="filtrarHoje()" title="Filtrar data de hoje">
                        <i class="bi bi-calendar-event me-2"></i>
                        Hoje
                    </button>
                    <button class="btn btn-filter-modern d-none" id="limparFiltroBtn" onclick="limparFiltroData()">
                        <i class="bi bi-x-lg me-2"></i>
                        Limpar
                    </button>
                    <button class="btn btn-warning" onclick="retornarTodosImpeditivos()">
                        <i class="bi bi-arrow-counterclockwise me-2"></i>
                        Retornar Todos
                    </button>
                </div>
            </div>

            <!-- Paginação Superior -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_impeditivos|length }} de {{ total_impeditivos }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page, tab='impeditivos') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page, tab='impeditivos') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page, tab='impeditivos') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Tabela Impeditivos -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="column-filter">
                                    CPF
                                    <div class="filter-icon" data-column="cpf">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar CPF..." data-column="cpf">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    PA
                                    <div class="filter-icon" data-column="pa">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar PA..." data-column="pa">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    Nome
                                    <div class="filter-icon" data-column="nome">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar Nome..." data-column="nome">
                                    </div>
                                </div>
                            </th>
                            <th>Data Execução</th>
                            <th>Anotação</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for crl in crls_impeditivos %}
                        <tr>
                            <td>{{ crl['cpf'][:3] }}.{{ crl['cpf'][3:6] }}.{{ crl['cpf'][6:9] }}-{{ crl['cpf'][9:] }}</td>
                            <td>{{ crl['pa'] }}</td>
                            <td>{{ crl['nome'] if crl['nome'] else '-' }}</td>
                            <td>{{ crl['data_crl'].strftime('%d/%m/%Y %H:%M:%S') if crl['data_crl'] else '-' }}</td>
                            <td>
                                <span class="badge bg-warning text-dark">Impeditivo</span>
                            </td>
                            <td>
                                <button class="btn btn-warning btn-action-modern" onclick="retornarCRL('{{ crl['cpf'] }}')" title="Retornar para Pendentes">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Inferior -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_impeditivos|length }} de {{ total_impeditivos }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page, tab='impeditivos') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page, tab='impeditivos') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page, tab='impeditivos') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Aba Erros -->
        <div class="tab-pane fade" id="erros" role="tabpanel">
            <!-- Controles de Filtro -->
            <div class="filter-controls-modern">
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-filter-modern" onclick="showDataFilterModal()">
                        <i class="bi bi-funnel me-2"></i>
                        <span class="filtro-data-status">Filtrar por Data</span>
                    </button>
                    <button class="btn btn-filter-modern" onclick="filtrarHoje()" title="Filtrar data de hoje">
                        <i class="bi bi-calendar-event me-2"></i>
                        Hoje
                    </button>
                    <button class="btn btn-filter-modern d-none" id="limparFiltroBtn" onclick="limparFiltroData()">
                        <i class="bi bi-x-lg me-2"></i>
                        Limpar
                    </button>
                    <button class="btn btn-warning" onclick="retornarTodosErros()">
                        <i class="bi bi-arrow-counterclockwise me-2"></i>
                        Retornar Todos
                    </button>
                </div>
            </div>

            <!-- Paginação Superior -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_erros|length }} de {{ total_erros }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page, tab='erros') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page, tab='erros') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page, tab='erros') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Tabela Erros -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="column-filter">
                                    CPF
                                    <div class="filter-icon" data-column="cpf">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar CPF..." data-column="cpf">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    PA
                                    <div class="filter-icon" data-column="pa">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar PA..." data-column="pa">
                                    </div>
                                </div>
                            </th>
                            <th>
                                <div class="column-filter">
                                    Nome
                                    <div class="filter-icon" data-column="nome">
                                        <i class="bi bi-search"></i>
                                        <i class="bi bi-x-lg clear-filter"></i>
                                    </div>
                                    <div class="filter-input">
                                        <input type="text" placeholder="Filtrar Nome..." data-column="nome">
                                    </div>
                                </div>
                            </th>
                            <th>Data Execução</th>
                            <th>Erro</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for crl in crls_erros %}
                        <tr>
                            <td>{{ crl['cpf'][:3] }}.{{ crl['cpf'][3:6] }}.{{ crl['cpf'][6:9] }}-{{ crl['cpf'][9:] }}</td>
                            <td>{{ crl['pa'] }}</td>
                            <td>{{ crl['nome'] if crl['nome'] else '-' }}</td>
                            <td>{{ crl['data_crl'].strftime('%d/%m/%Y %H:%M:%S') if crl['data_crl'] else '-' }}</td>
                            <td>
                                <span class="badge bg-danger">Erro</span>
                            </td>
                            <td>
                                <button class="btn btn-warning btn-action-modern" onclick="retornarCRL('{{ crl['cpf'] }}')" title="Retornar para Pendentes">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Paginação Inferior -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info-modern">
                    Mostrando {{ crls_erros|length }} de {{ total_erros }} CRLs
                </div>
                <nav aria-label="Navegação de páginas">
                    <ul class="pagination mb-0">
                        <li class="page-item {% if page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page-1, per_page=per_page, tab='erros') }}" aria-label="Anterior">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% for p in range(start_page, end_page + 1) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=p, per_page=per_page, tab='erros') }}">{{ p }}</a>
                        </li>
                        {% endfor %}
                        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('crls_executados', page=page+1, per_page=per_page, tab='erros') }}" aria-label="Próximo">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Modal de Filtro de Data -->
    <div class="modal fade" id="dataFilterModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Filtrar por Data</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dataFilterForm">
                        <div class="mb-3">
                            <label for="data_inicio" class="form-label">Data Início</label>
                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="{{ data_inicio }}">
                        </div>
                        <div class="mb-3">
                            <label for="data_fim" class="form-label">Data Fim</label>
                            <input type="date" class="form-control" id="data_fim" name="data_fim" value="{{ data_fim }}">
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="preencherDataHoje()">
                                <i class="bi bi-calendar-event me-1"></i>Hoje
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="aplicarFiltroData()">Aplicar Filtro</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function reprocessarCRL(cpf) {
    if (confirm('Tem certeza que deseja reprocessar esta CRL?')) {
        // Mostrar indicador de carregamento
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        button.disabled = true;

        fetch(`/api/crls/${cpf}/reprocessar`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                // Mostrar feedback de sucesso
                button.innerHTML = '<i class="bi bi-check-lg"></i>';
                button.classList.remove('btn-warning');
                button.classList.add('btn-success');

                // Recarregar após delay para mostrar feedback
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                const errorMsg = data ? (data.error || 'Erro desconhecido') : 'Resposta inválida do servidor';
                alert('Erro ao reprocessar CRL: ' + errorMsg);

                // Restaurar botão
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Erro completo:', error);
            alert('Erro ao processar a requisição: ' + error.message);

            // Restaurar botão
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

function retornarCRL(cpf) {
    if (confirm('Tem certeza que deseja retornar este CPF para a lista de CRLs pendentes?')) {
        // Mostrar indicador de carregamento
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        button.disabled = true;

        fetch(`/api/crls/${cpf}/retornar`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                // Mostrar feedback de sucesso
                button.innerHTML = '<i class="bi bi-check-lg"></i>';
                button.classList.remove('btn-warning');
                button.classList.add('btn-success');

                // Recarregar após delay para mostrar feedback
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                const errorMsg = data ? (data.error || 'Erro desconhecido') : 'Resposta inválida do servidor';
                alert('Erro ao retornar CRL: ' + errorMsg);

                // Restaurar botão
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Erro completo:', error);
            alert('Erro ao processar a requisição: ' + error.message);

            // Restaurar botão
            button.innerHTML = originalContent;
            button.disabled = false;
        });
    }
}

function retornarTodosImpeditivos() {
    if (confirm('Tem certeza que deseja retornar todos os CPFs com anotação impeditiva para pendentes?')) {
        fetch('/api/crls/retornar-impeditivos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro ao retornar CRLs: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao retornar CRLs');
        });
    }
}

function retornarTodosErros() {
    if (confirm('Tem certeza que deseja retornar todos os CPFs com erro para pendentes?')) {
        fetch('/api/crls/retornar-erros', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro ao retornar CRLs: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao retornar CRLs');
        });
    }
}

function changePerPage(value) {
    const url = new URL(window.location.href);
    url.searchParams.set('per_page', value);
    url.searchParams.set('page', 1);  // Volta para a primeira página
    window.location.href = url.toString();
}

// Funções para filtro de data
function showDataFilterModal() {
    const modal = new bootstrap.Modal(document.getElementById('dataFilterModal'));
    modal.show();
}

function preencherDataHoje() {
    const hoje = new Date().toISOString().split('T')[0];
    document.getElementById('data_inicio').value = hoje;
    document.getElementById('data_fim').value = hoje;
}

function aplicarFiltroData() {
    const dataInicio = document.getElementById('data_inicio').value;
    const dataFim = document.getElementById('data_fim').value;

    const url = new URL(window.location.href);
    if (dataInicio) url.searchParams.set('data_inicio', dataInicio);
    if (dataFim) url.searchParams.set('data_fim', dataFim);
    url.searchParams.set('page', 1);  // Volta para a primeira página

    window.location.href = url.toString();
}

function limparFiltroData() {
    const url = new URL(window.location.href);
    url.searchParams.delete('data_inicio');
    url.searchParams.delete('data_fim');
    url.searchParams.set('page', 1);  // Volta para a primeira página
    window.location.href = url.toString();
}

function filtrarHoje() {
    const hoje = new Date().toISOString().split('T')[0];
    const url = new URL(window.location.href);
    url.searchParams.set('data_inicio', hoje);
    url.searchParams.set('data_fim', hoje);
    url.searchParams.set('page', 1);  // Volta para a primeira página
    window.location.href = url.toString();
}

// Mantém a aba selecionada após recarregar a página
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    if (tab === 'impeditivos') {
        const impeditivos = new bootstrap.Tab(document.getElementById('impeditivos-tab'));
        impeditivos.show();
    } else if (tab === 'erros') {
        const errosTab = new bootstrap.Tab(document.getElementById('erros-tab'));
        errosTab.show();
    }

    // Atualizar texto do filtro de data
    atualizarTextoFiltroData();

    // Gerenciamento dos filtros de coluna
    const filterIcons = document.querySelectorAll('.filter-icon');

    filterIcons.forEach(icon => {
        const searchIcon = icon.querySelector('.bi-search');
        const clearIcon = icon.querySelector('.clear-filter');
        const column = icon.dataset.column;

        searchIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            const filterInput = icon.nextElementSibling;

            // Fecha todos os outros filtros
            document.querySelectorAll('.filter-input').forEach(input => {
                if (input !== filterInput) {
                    input.classList.remove('show');
                }
            });

            // Toggle do filtro atual
            filterInput.classList.toggle('show');
            if (filterInput.classList.contains('show')) {
                filterInput.querySelector('input').focus();
            }
        });

        // Limpar filtro ao clicar no X
        clearIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const filterInput = icon.nextElementSibling;
            const input = filterInput.querySelector('input');

            // Limpa o valor do input
            input.value = '';

            // Remove classes de estado
            icon.classList.remove('has-filter');
            filterInput.classList.remove('show');

            // Recarrega a página com o filtro limpo
            const url = new URL(window.location.href);
            url.searchParams.delete(`filter_${column}`);
            window.location.href = url.toString();
        });
    });

    // Fechar filtro ao clicar fora
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.column-filter')) {
            document.querySelectorAll('.filter-input').forEach(input => {
                input.classList.remove('show');
            });
        }
    });

    // Lógica de filtro em tempo real
    document.querySelectorAll('.filter-input input').forEach(input => {
        input.addEventListener('input', function() {
            const column = this.dataset.column;
            const value = this.value.toLowerCase();

            const url = new URL(window.location.href);
            if (value) {
                url.searchParams.set(`filter_${column}`, value);
            } else {
                url.searchParams.delete(`filter_${column}`);
            }

            // Aplicar filtro via AJAX ou recarregar página
            window.history.pushState({}, '', url);
        });
    });

    // Adicionar listeners para as abas
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (e) {
            const activeTab = e.target.getAttribute('data-bs-target').substring(1);
            const url = new URL(window.location.href);

            if (activeTab === 'impeditivos') {
                url.searchParams.set('tab', 'impeditivos');
            } else if (activeTab === 'erros') {
                url.searchParams.set('tab', 'erros');
            } else {
                url.searchParams.delete('tab');
            }

            window.history.pushState({}, '', url);
        });
    });
});

function atualizarTextoFiltroData() {
    const dataInicio = '{{ data_inicio }}';
    const dataFim = '{{ data_fim }}';
    const filtroStatus = document.querySelectorAll('.filtro-data-status');
    const limparBtn = document.getElementById('limparFiltroBtn');

    if ((dataInicio && dataInicio !== 'None') || (dataFim && dataFim !== 'None')) {
        let texto = 'Filtro: ';
        if (dataInicio && dataInicio !== 'None') texto += `De ${formatarData(dataInicio)}`;
        if (dataFim && dataFim !== 'None') texto += ` Até ${formatarData(dataFim)}`;
        filtroStatus.forEach(el => el.textContent = texto);
        if (limparBtn) limparBtn.classList.remove('d-none');
    } else {
        filtroStatus.forEach(el => el.textContent = 'Filtrar por Data');
        if (limparBtn) limparBtn.classList.add('d-none');
    }
}

function formatarData(data) {
    if (!data || data === 'None' || data === 'undefined') {
        return '';
    }
    const [ano, mes, dia] = data.split('-');
    return `${dia}/${mes}/${ano}`;
}
</script>
{% endblock %}