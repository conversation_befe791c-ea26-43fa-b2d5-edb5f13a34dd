<?php
function check_auth() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
}

function check_admin() {
    check_auth();
    if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        header('Location: index.php');
        exit();
    }
}

function get_user_pa() {
    return isset($_SESSION['pa']) ? $_SESSION['pa'] : null;
}

// Função para criar as tabelas necessárias se não existirem
function setup_database() {
    global $pdo;
    
    // Tabela de usuários da campanha
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        pa VARCHAR(10) NOT NULL,
        is_admin BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Tabela de vendas da campanha
    $pdo->exec("CREATE TABLE IF NOT EXISTS campanha_vendas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        cpf VARCHAR(11) NOT NULL,
        produto VARCHAR(100) NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        pa VARCHAR(10) NOT NULL,
        usuario VARCHAR(100) NOT NULL,
        data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP NULL,
        FOREIGN KEY (usuario) REFERENCES campanha_users(username)
    )");

    // Inserir usuário admin padrão se não existir
    $stmt = $pdo->prepare("INSERT IGNORE INTO campanha_users (username, pa, is_admin) VALUES (?, ?, ?)");
    $stmt->execute(['admin', '00', true]);
}

// Criar as tabelas ao carregar o arquivo
setup_database(); 