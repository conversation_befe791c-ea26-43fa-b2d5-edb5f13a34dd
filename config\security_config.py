import os
import base64

def generate_secret_key():
    """Gera uma chave secreta aleatória forte"""
    return base64.b64encode(os.urandom(32)).decode('utf-8')

def get_or_create_secret_key():
    """Obtém a chave secreta existente ou cria uma nova"""
    key_file = os.path.join(os.path.dirname(__file__), 'secret_key')
    
    # Se o arquivo já existe, lê a chave
    if os.path.exists(key_file):
        with open(key_file, 'r') as f:
            return f.read().strip()
    
    # Se não existe, gera uma nova chave e salva
    secret_key = generate_secret_key()
    os.makedirs(os.path.dirname(key_file), exist_ok=True)
    with open(key_file, 'w') as f:
        f.write(secret_key)
    
    return secret_key
