import pandas as pd
import mysql.connector
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except mysql.connector.Error as err:
        logger.error(f"Error connecting to MySQL: {err}")
        raise

def limpar_cpf(cpf):
    if pd.isna(cpf):
        return None
    return str(cpf).replace('.', '').replace('-', '').replace('/', '').zfill(11)

def processar_dados():
    try:
        # Read both spreadsheets
        logger.info("Reading spreadsheets...")
        df_cpfs = pd.read_excel('scripts/cpfs_atualizados.xlsx')
        df_pas = pd.read_excel('scripts/planilha_y.xlsx')
        
        # Clean CPFs in both dataframes
        df_cpfs['CPF'] = df_cpfs['CPF'].apply(limpar_cpf)
        df_pas['CPF'] = df_pas['CPF'].apply(limpar_cpf)
        
        # Drop the PA column from df_cpfs since it's all NaN
        if 'PA' in df_cpfs.columns:
            df_cpfs = df_cpfs.drop('PA', axis=1)
        
        # Merge dataframes to get PA values
        df = pd.merge(df_cpfs, df_pas[['CPF', 'PA']], on='CPF', how='inner')
        
        # Convert date format
        df['DATA'] = pd.to_datetime(df['DATA'], format='%d/%m/%Y %H:%M:%S')
        df['DATA'] = df['DATA'].dt.strftime('%Y-%m-%d %H:%M:%S')  # Convert to MySQL datetime format
        
        # Initialize counters
        total_registros = 0
        registros_atualizados = 0
        novos_registros = 0
        registros_com_erro = 0
        
        # Connect to database
        connection = get_mysql_connection()
        cursor = connection.cursor()
        
        # Process each record
        for _, row in df.iterrows():
            try:
                total_registros += 1
                
                cpf = row['CPF']
                renda = float(row['RENDA'])
                data = row['DATA']
                pa = int(row['PA'])  # Convert to int explicitly
                
                # Check if CPF exists
                cursor.execute("SELECT id FROM associados WHERE cpf = %s", (cpf,))
                result = cursor.fetchone()
                
                if result:
                    # Update existing record
                    cursor.execute("""
                        UPDATE associados 
                        SET renda = %s, data_aprovacao = %s, pa = %s, status = 3 
                        WHERE cpf = %s
                    """, (renda, data, pa, cpf))
                    registros_atualizados += 1
                else:
                    # Insert new record
                    cursor.execute("""
                        INSERT INTO associados (cpf, renda, data_aprovacao, pa, status) 
                        VALUES (%s, %s, %s, %s, 3)
                    """, (cpf, renda, data, pa))
                    novos_registros += 1
                
                connection.commit()
                
            except Exception as e:
                registros_com_erro += 1
                logger.error(f"Erro ao processar CPF {cpf}: {str(e)}")
                connection.rollback()
        
        # Log final results
        logger.info("\n=== Relatório de Processamento ===")
        logger.info(f"Total de registros processados: {total_registros}")
        logger.info(f"Registros atualizados: {registros_atualizados}")
        logger.info(f"Novos registros inseridos: {novos_registros}")
        logger.info(f"Registros com erro: {registros_com_erro}")
        
    except Exception as e:
        logger.error(f"Erro durante o processamento: {str(e)}")
        raise
    finally:
        if 'connection' in locals():
            cursor.close()
            connection.close()
            logger.info("Conexão com o banco de dados fechada")

if __name__ == "__main__":
    processar_dados() 