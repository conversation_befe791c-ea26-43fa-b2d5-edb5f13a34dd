import os
import logging
from utils.db import get_mysql_connection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_rpa_config():
    """
    Cria a tabela rpa_config e insere as configurações iniciais dos RPAs
    """
    try:
        # Lê o arquivo SQL
        migration_file = os.path.join('config', 'db_migrations', 'create_rpa_config.sql')
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_commands = f.read()

        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Executa os comandos SQL
        for command in sql_commands.split(';'):
            if command.strip():
                cursor.execute(command)
                
        conn.commit()
        
        # Verifica se a migração foi bem sucedida
        cursor.execute("SELECT process_name, display_name FROM rpa_config")
        results = cursor.fetchall()
        
        logger.info("\nConfiguração dos RPAs:")
        logger.info("-" * 50)
        
        for process_name, display_name in results:
            logger.info(f"RPA: {process_name}")
            logger.info(f"Nome de exibição: {display_name}")
            logger.info("-" * 30)
        
        cursor.close()
        conn.close()
        
        logger.info("Migração concluída com sucesso!")

    except Exception as e:
        logger.error(f"Erro durante a migração: {e}")
        raise

if __name__ == '__main__':
    migrate_rpa_config() 