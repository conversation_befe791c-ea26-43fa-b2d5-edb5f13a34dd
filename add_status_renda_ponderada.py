import mysql.connector
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Tentando conectar ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True,
            connection_timeout=5,
            allow_local_infile=True,
            use_pure=True
        )
        logger.info("Conexão MySQL estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def add_status_renda_ponderada():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Verifica se o status 7 já existe
        cursor.execute("SELECT COUNT(*) FROM status WHERE id = 7")
        status_exists = cursor.fetchone()[0] > 0

        if status_exists:
            logger.info("Status 7 (renda_ponderada) já existe")
        else:
            # Adiciona o status 7
            logger.info("Adicionando status 7 (renda_ponderada)...")
            cursor.execute("""
                INSERT INTO status (id, nome, descricao) 
                VALUES (7, 'renda_ponderada', 'Associado com renda ponderada')
            """)
            logger.info("Status 7 adicionado com sucesso!")

        conn.commit()

        # Mostra todos os status existentes
        logger.info("Status existentes na tabela:")
        cursor.execute("SELECT id, nome, descricao FROM status ORDER BY id")
        for row in cursor.fetchall():
            logger.info(f"  ID: {row[0]}, Nome: {row[1]}, Descrição: {row[2]}")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"Erro ao adicionar status: {str(e)}")
        raise

if __name__ == "__main__":
    add_status_renda_ponderada()
