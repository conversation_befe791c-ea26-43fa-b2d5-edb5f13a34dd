# IMPLEMENTAÇÃO DA FUNCIONALIDADE TIPO DE RENDA

## Resumo das Alterações

Esta implementação adiciona suporte para a coluna "Tipo de Renda" no módulo de uploads de associados, com as seguintes funcionalidades:

1. **Nova coluna "Tipo de Renda"** na planilha de upload (coluna J)
2. **Agrupamento automático** de registros do mesmo CPF com múltiplos tipos de renda
3. **Status especial (7)** para associados com "RENDA PONDERADA"
4. **Concatenação de tipos de renda** quando um associado possui múltiplos tipos

## Arquivos Criados/Modificados

### Novos Arquivos:
- `add_tipo_renda_column.sql` - Script SQL para adicionar coluna
- `add_tipo_renda_column.py` - Script Python para adicionar coluna
- `add_status_renda_ponderada.sql` - Script SQL para adicionar status 7
- `add_status_renda_ponderada.py` - Script Python para adicionar status 7
- `install_tipo_renda_feature.py` - Script de instalação completa
- `test_tipo_renda_upload.py` - Script de teste da funcionalidade

### Arquivos Modificados:
- `app.py` - Lógica principal de upload e processamento
- `update_associados_from_completa.py` - Função de atualização de associados

## Instalação

### Passo 1: Executar Script de Instalação
```bash
python install_tipo_renda_feature.py
```

Este script irá:
- Adicionar a coluna `tipo_renda` nas tabelas `associados` e `associados_completa`
- Criar índices para otimização
- Adicionar o status 7 (renda_ponderada) na tabela `status`
- Verificar se todas as alterações foram aplicadas corretamente

### Passo 2: Reiniciar o Servidor
Reinicie o servidor Flask para carregar as alterações no código:
```bash
python app.py
```

### Passo 3: Testar a Funcionalidade
Execute o script de teste:
```bash
python test_tipo_renda_upload.py
```

## Regras de Negócio Implementadas

### 1. Mapeamento de Colunas
A nova coluna "Tipo de Renda" aceita as seguintes variações de nome:
- "Tipo de Renda"
- "Tipo_de_Renda" 
- "TipoDeRenda"
- "Tipo Renda"
- "TipoRenda"

### 2. Agrupamento por CPF
Quando existem múltiplos registros para o mesmo CPF:
- Os tipos de renda são concatenados com " | " como separador
- Os dados do registro mais recente são mantidos
- Exemplo: "SALÁRIO | RENDA PONDERADA | APOSENTADORIA"

### 3. Status Especial para Renda Ponderada
- Se o tipo de renda contém "RENDA PONDERADA": status = 7
- Caso contrário: status = 1 (pendente) ou mantém o status existente

### 4. Atualização de Registros Existentes
- Sempre atualiza o campo `tipo_renda` com os novos dados
- Respeita as regras de data (data_movimento vs data_aprovacao)
- Atualiza o status conforme as regras de renda ponderada

## Estrutura do Banco de Dados

### Tabela `associados`
Nova coluna adicionada:
```sql
tipo_renda TEXT DEFAULT NULL COMMENT 'Tipos de renda do associado (concatenados se múltiplos)'
```

### Tabela `associados_completa`
Nova coluna adicionada:
```sql
tipo_renda VARCHAR(255) DEFAULT NULL COMMENT 'Tipo de renda do associado'
```

### Tabela `status`
Novo status adicionado:
```sql
INSERT INTO status (id, nome, descricao) 
VALUES (7, 'renda_ponderada', 'Associado com renda ponderada');
```

## Exemplo de Planilha

A planilha de upload deve conter as seguintes colunas (incluindo a nova):

| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| Data Movimento | Número PA | CPF/CNPJ | Nome Cliente | Telefone | Data da Última Renovação Cadastral | Valor da Renda Bruta Mensal | Código CNAE | CNAE | **Tipo de Renda** |
| 2024-01-15 | 12345 | 12345678901 | João Silva | 11999999999 | 2023-12-01 | 5000.00 | 1234 | Atividade | SALÁRIO |
| 2024-01-15 | 12345 | 12345678901 | João Silva | 11999999999 | 2023-12-01 | 5000.00 | 1234 | Atividade | RENDA PONDERADA |

**Resultado após processamento:**
- CPF: 12345678901
- Tipo de Renda: "SALÁRIO | RENDA PONDERADA"
- Status: 7 (renda_ponderada)

## Fluxo de Processamento

1. **Upload da Planilha**: Usuário faz upload do arquivo Excel
2. **Validação**: Sistema verifica se a coluna "Tipo de Renda" existe
3. **Formatação**: CPF é formatado, datas convertidas
4. **Agrupamento**: Registros do mesmo CPF são agrupados
5. **Concatenação**: Tipos de renda são concatenados com " | "
6. **Inserção/Atualização**: Dados são salvos na tabela `associados_completa`
7. **Transferência**: Registros com score >= 500 são transferidos para `associados`
8. **Status**: Status é definido baseado no tipo de renda

## Logs e Monitoramento

O sistema gera logs detalhados durante o processamento:
- Quantidade de registros antes e depois do agrupamento
- CPFs processados e seus tipos de renda
- Status atribuídos
- Estatísticas de inserção/atualização

## Troubleshooting

### Erro: Coluna 'tipo_renda' não encontrada
**Solução**: Execute `python install_tipo_renda_feature.py`

### Erro: Status 7 não existe
**Solução**: Execute `python add_status_renda_ponderada.py`

### Planilha não processa tipos de renda
**Verificações**:
1. A coluna "Tipo de Renda" existe na planilha?
2. O nome da coluna está correto (aceita variações)?
3. Os dados não estão vazios?

### Registros não recebem status 7
**Verificações**:
1. O tipo de renda contém exatamente "RENDA PONDERADA"?
2. O status 7 existe na tabela `status`?
3. Verifique os logs para mensagens de erro

## Testes

Execute os testes para verificar se tudo está funcionando:

```bash
# Teste completo da funcionalidade
python test_tipo_renda_upload.py

# Verificar estrutura do banco
python -c "
import mysql.connector
conn = mysql.connector.connect(host='rpa.sicoobcredilivre.com.br', user='rpa', password='sicoob@123', database='rpa')
cursor = conn.cursor()
cursor.execute('DESCRIBE associados')
print('Colunas associados:', [row[0] for row in cursor.fetchall()])
cursor.execute('SELECT * FROM status WHERE id = 7')
print('Status 7:', cursor.fetchall())
"
```

## Suporte

Para dúvidas ou problemas:
1. Verifique os logs do sistema
2. Execute os scripts de teste
3. Consulte este README
4. Verifique a estrutura do banco de dados
