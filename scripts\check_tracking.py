from utils.db import get_mysql_connection

def check_tracking():
    """Verifica os registros atuais na tabela rpa_tracking"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT rpa_name, current_status, total_active_time 
            FROM rpa_tracking
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        
        print("\nRegistros atuais na tabela rpa_tracking:")
        print("-" * 50)
        
        if results:
            for rpa_name, status, total_time in results:
                print(f"RPA: {rpa_name}")
                print(f"Status: {status or 'N/A'}")
                print(f"Tempo total: {total_time or 0} segundos")
                print("-" * 30)
        else:
            print("Nenhum registro encontrado")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Erro ao verificar registros: {e}")

if __name__ == '__main__':
    check_tracking() 