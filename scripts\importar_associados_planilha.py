import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>
import logging
import os
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def conectar_banco():
    """Estabelece conexão com o banco de dados"""
    try:
        connection = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        return connection
    except Error as e:
        logger.error(f"Erro ao conectar ao banco de dados: {e}")
        raise

def tratar_data(data):
    """
    Trata a data para o formato correto do MySQL (YYYY-MM-DD)
    """
    try:
        if isinstance(data, str):
            # Tenta converter string para datetime
            data = pd.to_datetime(data)
        if isinstance(data, pd.Timestamp):
            # Converte Timestamp para datetime
            data = data.to_pydatetime()
        if isinstance(data, datetime):
            # Formata a data para o formato MySQL
            return data.strftime('%Y-%m-%d')
        return None
    except Exception as e:
        logger.error(f"Erro ao tratar data: {data} - Erro: {str(e)}")
        return None

def importar_dados(planilha):
    """
    Importa os dados da planilha para a tabela associados
    """
    try:
        # Ler a planilha
        logger.info(f"Lendo planilha: {planilha}")
        df = pd.read_excel(planilha)
        
        # Verificar colunas necessárias
        colunas_necessarias = ['CPF', 'RENDA', 'DATA', 'PA']
        for coluna in colunas_necessarias:
            if coluna not in df.columns:
                raise ValueError(f"Coluna {coluna} não encontrada na planilha")
        
        # Estabelecer conexão com o banco
        connection = conectar_banco()
        cursor = connection.cursor()
        
        # Contadores para o relatório
        atualizados = 0
        inseridos = 0
        erros_data = 0
        
        # Processar cada registro
        for _, row in df.iterrows():
            cpf = row['CPF']
            renda = float(row['RENDA'])
            data = tratar_data(row['DATA'])
            pa = row['PA']
            
            # Se a data for inválida, registra o erro e continua
            if data is None:
                logger.error(f"Data inválida para o CPF {cpf}: {row['DATA']}")
                erros_data += 1
                continue
            
            # Verificar se o CPF já existe
            cursor.execute("SELECT id FROM associados WHERE cpf = %s", (cpf,))
            resultado = cursor.fetchone()
            
            if resultado:
                # Atualizar registro existente
                update_query = """
                UPDATE associados 
                SET pa = %s, renda = %s, data_aprovacao = %s, status = 3
                WHERE cpf = %s
                """
                cursor.execute(update_query, (pa, renda, data, cpf))
                atualizados += 1
            else:
                # Inserir novo registro
                insert_query = """
                INSERT INTO associados (cpf, pa, renda, data_aprovacao, status)
                VALUES (%s, %s, %s, %s, 3)
                """
                cursor.execute(insert_query, (cpf, pa, renda, data))
                inseridos += 1
        
        # Commit das alterações
        connection.commit()
        
        # Relatório final
        logger.info("\n=== Relatório de Importação ===")
        logger.info(f"Total de registros processados: {len(df)}")
        logger.info(f"Registros atualizados: {atualizados}")
        logger.info(f"Novos registros inseridos: {inseridos}")
        logger.info(f"Registros com erro na data: {erros_data}")
        logger.info("==============================")
        
    except Exception as e:
        logger.error(f"Erro durante a importação: {str(e)}")
        if 'connection' in locals() and connection.is_connected():
            connection.rollback()
        raise
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    try:
        # Caminho da planilha atualizada
        planilha = r"C:\Users\<USER>\Downloads\planilha_atualizada.xlsx"
        
        if not os.path.exists(planilha):
            raise FileNotFoundError(f"Arquivo não encontrado: {planilha}")
        
        importar_dados(planilha)
        
    except Exception as e:
        logger.error(f"Erro: {str(e)}")
        exit(1) 