document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips do Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Gerenciar estado do administrador e permissões
    document.querySelectorAll('#is_admin, #edit_is_admin').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            var moduleCheckboxes = this.closest('form').querySelectorAll('input[name="modules"]');
            moduleCheckboxes.forEach(function(moduleCheckbox) {
                moduleCheckbox.disabled = checkbox.checked;
                if (checkbox.checked) {
                    moduleCheckbox.checked = true;
                }
            });
        });
    });

    // Validação de formulário
    document.querySelectorAll('form').forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});

// Função para editar usuário
function editarUsuario(userId) {
    // Limpar checkboxes
    document.querySelectorAll('.edit-module').forEach(checkbox => checkbox.checked = false);
    
    // Carregar dados do usuário
    fetch(`/usuarios/editar/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Erro ao carregar dados do usuário');
                return;
            }
            
            document.getElementById('edit_name').value = data.name;
            document.getElementById('edit_username').value = data.username;
            const paSelect = document.getElementById('edit_pa');
            paSelect.value = data.pa;
            paSelect.options[paSelect.selectedIndex].selected = true;
            document.getElementById('edit_is_admin').checked = data.is_admin;
            
            // Marcar permissões
            data.permissions.forEach(module => {
                const checkbox = document.querySelector(`.edit-module[value="${module}"]`);
                if (checkbox) checkbox.checked = true;
            });
            
            // Atualizar action do formulário
            document.getElementById('editarUsuarioForm').action = `/usuarios/editar/${userId}`;
            
            // Mostrar modal
            new bootstrap.Modal(document.getElementById('editarUsuarioModal')).show();
        })
        .catch(error => {
            console.error('Erro ao carregar dados:', error);
            alert('Erro ao carregar dados do usuário');
        });
}

// Função para excluir usuário
function excluirUsuario(userId, username) {
    document.getElementById('excluirUsuarioNome').textContent = username;
    document.getElementById('excluirUsuarioForm').action = `/usuarios/excluir/${userId}`;
    new bootstrap.Modal(document.getElementById('excluirUsuarioModal')).show();
}

// Função para limpar formulário
function limparFormulario(formId) {
    document.getElementById(formId).reset();
    document.querySelectorAll(`#${formId} input[name="modules"]`).forEach(function(checkbox) {
        checkbox.disabled = false;
    });
}
