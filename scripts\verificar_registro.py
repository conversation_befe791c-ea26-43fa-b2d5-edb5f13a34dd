import mysql.connector
from datetime import datetime
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Conectando ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True
        )
        logger.info("Conexão estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def verificar_registro():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Verifica a execução atual
        logger.info("Verificando execução atual...")
        cursor.execute("""
            SELECT 
                e.id,
                e.rpa_name,
                e.start_time,
                e.status,
                e.active_time,
                e.last_status_update,
                COUNT(h.id) as historico_count
            FROM rpa_executions e
            LEFT JOIN rpa_execution_history h ON h.execution_id = e.id
            WHERE e.status = 'running'
            GROUP BY e.id
            ORDER BY e.start_time DESC
            LIMIT 1
        """)
        
        execucao = cursor.fetchone()
        
        if execucao:
            logger.info(f"""
Detalhes da execução atual:
ID: {execucao[0]}
RPA: {execucao[1]}
Início: {execucao[2]}
Status: {execucao[3]}
Tempo Ativo: {execucao[4]} segundos
Última Atualização: {execucao[5]}
Registros no Histórico: {execucao[6]}
""")
        else:
            logger.warning("Nenhuma execução em andamento encontrada!")

    except Exception as e:
        logger.error(f"Erro ao verificar registro: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    verificar_registro() 