from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, send_file, Blueprint, g
from flask_session import Session
import json
import os
import uuid
import requests
import time
import logging
import mysql.connector
from datetime import datetime, timedelta
from models.user import User, init_db, verify_password
from utils.db import get_mysql_connection
from utils.audit_logger import audit_logger
from routes.audit import audit_bp
import functools
import sqlite3
import pandas as pd
import numpy as np
from werkzeug.utils import secure_filename
from update_associados_from_completa import update_associados_from_completa
import re
from io import BytesIO
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from utils.rpa_execution import get_total_execution_time
from utils.rpa_execution_tracker import handle_rpa_status_change
from utils.audit_decorators import audit_log
import traceback
from models.ad_auth import ADAuth
# import smtplib  # Removido: não é mais necessário
# from email.mime.text import MIMEText  # Removido: não é mais necessário
# from email.mime.multipart import MIMEMultipart  # Removido: não é mais necessário
import random
import string
from msgraph_mail import enviar_email_automatico

# Configuração de log
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Adiciona log handler para arquivo
file_handler = logging.FileHandler('app.log')
file_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
logging.getLogger().addHandler(file_handler)

# Definir módulos disponíveis
AVAILABLE_MODULES = {
    'dashboard': 'Painel Principal',
    'cpfs': 'Módulo de CPFs',
    'rpa_monitor': 'Monitoramento de RPAs',
    'uploads': 'Upload de Planilhas',
    'robozometro': 'Robozômetro',
    'relatorios': 'Relatórios',
    'audit': 'Logs de Auditoria',
    'crls': 'Módulo de CRLs'
}

# Constantes
AVAILABLE_PAS = ['0', '2', '3', '4', '5', '6', '7', '9', '10', '11', '12', '15', '16', '17', '18', '20', '21', '22', '23', '24', '25', '26', '97']
MAX_LOGIN_ATTEMPTS = 3
LOGIN_TIMEOUT = 300  # 5 minutos em segundos

# Constantes para o Robozômetro
CUSTO_HORA_FUNCIONARIO = 38.35  # Custo médio por hora de um funcionário

# Configurações SMTP (não utilizadas)
# SMTP_HOST = 'smtp.office365.com'
# SMTP_PORT = 587
# SMTP_USER = '<EMAIL>'
# SMTP_PASS = '123456'
# SMTP_FROM_EMAIL = '<EMAIL>'

app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
app.config['DATA_DIR'] = os.path.join(os.path.dirname(__file__), 'data')
app.config['UPLOAD_FOLDER'] = os.path.join(app.config['DATA_DIR'], 'uploads')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)
app.config['SESSION_COOKIE_SECURE'] = True  # Requer HTTPS para produção
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_FILE_DIR'] = os.path.join(app.config['DATA_DIR'], 'flask_session')
app.config['SESSION_FILE_THRESHOLD'] = 500  # Número máximo de arquivos de sessão
app.config['SESSION_PERMANENT'] = True
app.jinja_env.globals.update(url_for=url_for)

# --- Integração Microsoft Graph API ---
from msgraph_routes import msgraph_bp
app.register_blueprint(msgraph_bp)
# ---

def format_currency(value):
    """Format a number as Brazilian currency (R$)"""
    try:
        if value is None:
            return ''
        return f"R$ {float(value):,.2f}".replace(',', '_').replace('.', ',').replace('_', '.')
    except (ValueError, TypeError):
        return value

# Register the custom filter
app.jinja_env.filters['format_currency'] = format_currency

# Garante que o diretório de sessões existe
os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

# Inicializa o Flask-Session
Session(app)

# Registra os blueprints
app.register_blueprint(audit_bp)

# Inicializa o banco de dados
init_db()

# Dicionário para controlar tentativas de login
login_attempts = {}

# Rota para favicon.ico
@app.route('/favicon.ico')
def favicon():
    return '', 204

# Handler para erros não tratados
@app.errorhandler(500)
def handle_internal_error(error):
    app.logger.error(f"Erro interno não tratado: {str(error)}")
    app.logger.error(f"Traceback: {traceback.format_exc()}")
    return jsonify({'success': False, 'error': 'Erro interno do servidor'}), 500

@app.errorhandler(Exception)
def handle_exception(error):
    app.logger.error(f"Exceção não tratada: {str(error)}")
    app.logger.error(f"Traceback: {traceback.format_exc()}")
    if request.path.startswith('/api/'):
        return jsonify({'success': False, 'error': 'Erro interno do servidor'}), 500
    try:
        return render_template('error.html', error=str(error)), 500
    except:
        # Se não conseguir renderizar o template de erro, retorna uma resposta simples
        return f"<h1>Erro 500</h1><p>Ocorreu um erro interno: {str(error)}</p>", 500

def login_required(f):
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            app.logger.warning('Acesso negado: usuário não está na sessão')
            return redirect(url_for('login'))
            
        user = User.get_by_id(session['user_id'])
        if not user:
            app.logger.warning('Acesso negado: usuário não encontrado no banco')
            session.clear()
            return redirect(url_for('login'))
            
        if not user.is_active:
            app.logger.warning('Acesso negado: usuário inativo')
            session.clear()
            return redirect(url_for('login'))
            
        # Atualiza o timestamp da última atividade
        session['last_activity'] = datetime.now().timestamp()
        
        # Adiciona o usuário ao contexto global do template
        g.user = user
        
        return f(*args, **kwargs)
            
    return decorated_function

def admin_required(f):
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or not User.get_by_id(session['user_id']).is_admin:
            flash('Acesso negado. Apenas administradores podem acessar esta página.', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def module_required(module_name):
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                app.logger.warning("Acesso negado: usuário não está na sessão")
                return redirect(url_for('login'))
            
            try:
                user = User.get_by_id(session['user_id'])
                if not user:
                    app.logger.warning(f"Usuário não encontrado: {session['user_id']}")
                    session.clear()
                    return redirect(url_for('login'))
                
                user_permissions = user.permissions
                
                # Verifica se o usuário tem permissão para o módulo
                if module_name not in user_permissions and not user.is_admin:
                    app.logger.warning(f"Acesso negado ao módulo {module_name} para usuário {user.username}")
                    flash('Acesso negado. Você não tem permissão para acessar este módulo.', 'error')
                    return redirect(url_for('dashboard'))
                
                # Atualiza as permissões na sessão
                session['permissions'] = user_permissions
                session['is_admin'] = user.is_admin
                session['pa'] = user.pa
                session.modified = True
                
                return f(*args, **kwargs)
                
            except Exception as e:
                app.logger.error(f"Erro ao verificar permissões: {str(e)}")
                session.clear()
                flash('Erro ao verificar permissões. Por favor, faça login novamente.', 'error')
                return redirect(url_for('login'))
                
        return decorated_function
    return decorator

def migrate_config_to_data():
    """Migra os RPAs do config.json para data/rpas.json"""
    config_file = os.path.join(os.path.dirname(__file__), 'config.json')
    rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
    
    if os.path.exists(config_file) and not os.path.exists(rpas_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                
            if 'rpas' in config:
                rpas_list = []
                for rpa_id, rpa_data in config['rpas'].items():
                    rpa_data['id'] = rpa_id
                    rpas_list.append(rpa_data)
                
                os.makedirs(app.config['DATA_DIR'], exist_ok=True)
                with open(rpas_file, 'w') as f:
                    json.dump(rpas_list, f, indent=4)
                
                # Remove a seção rpas do config.json
                config.pop('rpas', None)
                with open(config_file, 'w') as f:
                    json.dump(config, f, indent=4)
                
                print("Migração dos RPAs concluída com sucesso!")
        except Exception as e:
            print(f"Erro durante a migração: {e}")

migrate_config_to_data()  # Adiciona esta linha

# Carrega a configuração do arquivo
def load_config():
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        if os.path.exists(rpas_file):
            with open(rpas_file, 'r') as f:
                rpas = json.load(f)
                return {'rpas': {rpa['id']: rpa for rpa in rpas}}
        return {'rpas': {}}
    except Exception as e:
        logging.error(f"Erro ao carregar configuração: {str(e)}")
        return {'rpas': {}}

# Salva a configuração no arquivo
def save_config(config):
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        rpas_list = [dict(rpa, id=rpa_id) for rpa_id, rpa in config['rpas'].items()]
        with open(rpas_file, 'w') as f:
            json.dump(rpas_list, f, indent=4)
    except Exception as e:
        logging.error(f"Erro ao salvar configuração: {str(e)}")

# Obtém a lista de RPAs configurados
def get_rpas():
    config = load_config()
    rpas_dict = config.get('rpas', {})
    rpas_list = []
    
    # Converte o dicionário de RPAs em uma lista
    for rpa_id, rpa_data in rpas_dict.items():
        rpa = dict(rpa_data)  # Cria uma cópia do dicionário
        rpa['id'] = rpa_id    # Adiciona o ID ao objeto
        rpas_list.append(rpa)
    
    return rpas_list

# Cria um novo RPA com as configurações padrão
def create_rpa(rpa_data):
    config = load_config()
    if 'rpas' not in config:
        config['rpas'] = {}
    
    rpa_id = str(uuid.uuid4())
    
    # Cria o objeto RPA com os campos necessários
    new_rpa = {
        'name': rpa_data.get('name'),
        'process_name': rpa_data.get('process_name'),
        'host': rpa_data.get('host', 'localhost'),
        'port': rpa_data.get('port', 5000),
        'auto_restart': rpa_data.get('auto_restart', False)
    }
    
    # Adiciona os endpoints
    new_rpa['status_endpoint'] = f"http://{new_rpa['host']}:{new_rpa['port']}/status"
    new_rpa['restart_endpoint'] = f"http://{new_rpa['host']}:{new_rpa['port']}/start"
    
    config['rpas'][rpa_id] = new_rpa
    save_config(config)
    
    # Registra o RPA na tabela rpa_tracking
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Insere o registro inicial na tabela rpa_tracking
        cursor.execute("""
            INSERT INTO rpa_tracking 
            (rpa_name, current_status, last_status_update)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE
                current_status = VALUES(current_status),
                last_status_update = VALUES(last_status_update)
        """, (
            new_rpa['name'],
            'stopped',
            datetime.now()
        ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        logging.error(f"Erro ao registrar RPA na tabela rpa_tracking: {e}")
    
    # Retorna o RPA com seu ID
    new_rpa['id'] = rpa_id
    return new_rpa

# Atualiza as informações de status de todos os RPAs
def update_rpa_info():
    rpas = get_rpas()
    updated_rpas = []
    
    for rpa in rpas:
        rpa_info = dict(rpa)  # Cria uma cópia do dicionário
        try:
            url = rpa.get('status_endpoint') or f"http://{rpa['host']}:{rpa['port']}/status"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                rpa_info['status'] = response.json()
            else:
                rpa_info['status'] = {'error': 'Erro ao obter status'}
        except Exception as e:
            rpa_info['status'] = {'error': str(e)}
        updated_rpas.append(rpa_info)
    
    return updated_rpas

def get_rpa_by_id(rpa_id):
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        if os.path.exists(rpas_file):
            with open(rpas_file, 'r') as f:
                rpas = json.load(f)
                for rpa in rpas:
                    if rpa['id'] == rpa_id:
                        return rpa
    except Exception as e:
        print(f"Erro ao carregar RPAs: {e}")
    return None

def get_rpa_status(rpa):
    try:
        url = f"http://{rpa['host']}:{rpa['port']}/status"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            status_data = response.json()
            # Atualiza registro no banco
            handle_rpa_status_change(rpa['name'], status_data)
            return status_data
            
    except requests.exceptions.RequestException:
        status_data = {
            'status': 'error',
            'error': 'RPA não está respondendo'
        }
        handle_rpa_status_change(rpa['name'], status_data)
        return status_data
    except Exception as e:
        status_data = {
            'status': 'error',
            'error': str(e)
        }
        handle_rpa_status_change(rpa['name'], status_data)
        return status_data

@app.route('/')
@login_required
@module_required('dashboard')
def dashboard():
    user = User.get_by_id(session['user_id'])
    user_permissions = user.permissions
    return render_template('dashboard.html', user_permissions=user_permissions)

@app.route('/rpa-monitor')
@login_required
@module_required('rpa_monitor')
def rpa_monitor():
    user = User.get_by_id(session['user_id'])
    user_permissions = user.permissions
    return render_template('rpa_monitor.html', user_permissions=user_permissions, modules=AVAILABLE_MODULES)

@app.route('/cpfs')
@login_required
@module_required('cpfs')
def cpfs():
    """Dashboard principal do módulo CPFs"""
    # Limpa mensagens flash antigas
    session.pop('_flashes', None)
    
    conn = None
    cursor = None
    try:
        user = User.get_by_id(session['user_id'])
        user_id = user.id
        is_admin = user.is_admin
        
        # Get user's PA if not admin
        pa_filter = None
        if not is_admin:
            pa_filter = user.pa  # Usa todos os PAs do usuário
        
        # Obter parâmetros de filtro
        data_inicio = request.args.get('data_inicio') or ''
        data_fim = request.args.get('data_fim') or ''
        
        # Processar múltiplos PAs
        pas_selecionados = request.args.getlist('pa')
        
        # Processar múltiplos status
        status_selecionados = []
        for status in request.args.getlist('status'):
            if ',' in status:  # Para o caso de "4,5" dos erros
                status_selecionados.extend(status.split(','))
            else:
                status_selecionados.append(status)
        
        app.logger.info("Iniciando consulta de estatísticas de CPFs...")
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Lista de PAs para o filtro (apenas para admin)
        pas = []
        if is_admin:
            cursor.execute("SELECT DISTINCT pa FROM associados ORDER BY pa")
            pas = [str(row['pa']) for row in cursor.fetchall()]
        else:
            pas = user.pa  # Usa os PAs do usuário
        
        stats = {
            'pendentes': 0,
            'encaminhados': 0,
            'aprovados': 0,
            'erros': 0,
            'renda_ponderada': 0
        }
        
        # Consulta base
        base_query = """
            SELECT
                CASE
                    WHEN s.id IN (4, 5) THEN 'Erro'
                    WHEN s.id IN (3, 8) THEN 'Aprovado'
                    WHEN s.id = 7 THEN 'Renda Ponderada'
                    ELSE s.nome
                END as nome,
                COUNT(*) as total
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE 1=1
        """
        query_params = []
        
        # Aplicar filtros
        if data_inicio:
            base_query += " AND DATE(a.data_atualizacao) >= %s"
            query_params.append(data_inicio)
            
        if data_fim:
            base_query += " AND DATE(a.data_atualizacao) <= %s"
            query_params.append(data_fim)
        
        # Filtro de PA
        if not is_admin:
            placeholders = ','.join(['%s'] * len(user.pa))
            base_query += f" AND a.pa IN ({placeholders})"
            query_params.extend(user.pa)
        elif pas_selecionados:
            placeholders = ','.join(['%s'] * len(pas_selecionados))
            base_query += f" AND a.pa IN ({placeholders})"
            query_params.extend(pas_selecionados)
        
        # Filtro de Status
        if status_selecionados:
            placeholders = ','.join(['%s'] * len(status_selecionados))
            base_query += f" AND s.id IN ({placeholders})"
            query_params.extend([int(s) for s in status_selecionados])
        
        base_query += " GROUP BY CASE WHEN s.id IN (4, 5) THEN 'Erro' WHEN s.id IN (3, 8) THEN 'Aprovado' WHEN s.id = 7 THEN 'Renda Ponderada' ELSE s.nome END"
        
        # Executar consultas
        cursor.execute(base_query, query_params)
        
        for row in cursor:
            status_nome = row['nome'].lower()
            total = row['total']
            if 'pendente' in status_nome:
                stats['pendentes'] = total
            elif 'encaminhado' in status_nome:
                stats['encaminhados'] = total
            elif 'aprovado' in status_nome:
                stats['aprovados'] = total
            elif 'erro' in status_nome:
                stats['erros'] = total
            elif 'renda ponderada' in status_nome:
                stats['renda_ponderada'] = total
        
        # Dados para o gráfico
        chart_data = {
            'pendentes': [],
            'encaminhados': [],
            'aprovados': [],
            'erros': [],
            'renda_ponderada': []
        }
        chart_labels = []
        
        # Consulta para distribuição por PA
        chart_query = """
            SELECT
                a.pa,
                CASE
                    WHEN s.id IN (4, 5) THEN 'Erro'
                    WHEN s.id IN (3, 8) THEN 'Aprovado'
                    WHEN s.id = 7 THEN 'Renda Ponderada'
                    ELSE s.nome
                END as status,
                COUNT(*) as total
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE 1=1
        """
        chart_params = []
        
        # Aplicar os mesmos filtros na consulta do gráfico
        if data_inicio:
            chart_query += " AND DATE(a.data_atualizacao) >= %s"
            chart_params.append(data_inicio)
        if data_fim:
            chart_query += " AND DATE(a.data_atualizacao) <= %s"
            chart_params.append(data_fim)
        if not is_admin:
            placeholders = ','.join(['%s'] * len(user.pa))
            chart_query += f" AND a.pa IN ({placeholders})"
            chart_params.extend(user.pa)
        elif pas_selecionados:
            placeholders = ','.join(['%s'] * len(pas_selecionados))
            chart_query += f" AND a.pa IN ({placeholders})"
            chart_params.extend(pas_selecionados)
        if status_selecionados:
            placeholders = ','.join(['%s'] * len(status_selecionados))
            chart_query += f" AND s.id IN ({placeholders})"
            chart_params.extend([int(s) for s in status_selecionados])
            
        chart_query += " GROUP BY a.pa, CASE WHEN s.id IN (4, 5) THEN 'Erro' WHEN s.id IN (3, 8) THEN 'Aprovado' WHEN s.id = 7 THEN 'Renda Ponderada' ELSE s.nome END ORDER BY CAST(a.pa AS SIGNED), status"
        
        # Executar consulta do gráfico
        cursor.execute(chart_query, chart_params)
        current_pa = None
        for row in cursor:
            pa = row["pa"]
            if current_pa != pa:
                if current_pa is not None:
                    # Preencher zeros para status que não têm dados
                    for status in chart_data:
                        if len(chart_data[status]) < len(chart_labels):
                            chart_data[status].append(0)
                current_pa = pa
                chart_labels.append(f'PA {pa}')
            
            status_nome = row['status'].lower()
            total = row['total']
            
            if 'pendente' in status_nome:
                chart_data['pendentes'].append(total)
            elif 'encaminhado' in status_nome:
                chart_data['encaminhados'].append(total)
            elif 'aprovado' in status_nome:
                chart_data['aprovados'].append(total)
            elif 'erro' in status_nome:
                chart_data['erros'].append(total)
            elif 'renda ponderada' in status_nome:
                chart_data['renda_ponderada'].append(total)
        
        # Preencher zeros para o último PA
        if chart_labels:
            for status in chart_data:
                if len(chart_data[status]) < len(chart_labels):
                    chart_data[status].append(0)
        
        app.logger.info("Consulta de estatísticas concluída com sucesso.")
        
        template = render_template('cpfs_dashboard.html',
                             stats_pendentes=stats['pendentes'],
                             stats_encaminhados=stats['encaminhados'],
                             stats_aprovados=stats['aprovados'],
                             stats_erros=stats['erros'],
                             stats_renda_ponderada=stats['renda_ponderada'],
                             chart_labels=chart_labels,
                             chart_data=chart_data,
                             is_admin=is_admin,
                             pas=pas,
                             filter_pas=pas_selecionados,
                             filter_status=status_selecionados,
                             filter_data_inicio=data_inicio,
                             filter_data_fim=data_fim)
        
        return template
                             
    except Exception as e:
        app.logger.error(f"Erro ao consultar estatísticas: {str(e)}")
        flash('Erro ao carregar estatísticas. Por favor, tente novamente.', 'danger')
        return redirect(url_for('dashboard'))
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/cpfs/pendentes')
@login_required
@module_required('cpfs')
def cpfs_pendentes():
    # Limpa mensagens flash antigas
    session.pop('_flashes', None)
    
    conn = None
    cursor = None
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)
        filter_cpf = request.args.get('filter_cpf', '')
        filter_pa = request.args.get('filter_pa', '')
        filter_nome = request.args.get('filter_nome', '')
        
        # Base query para CPFs pendentes
        base_query = """
            SELECT a.cpf, a.pa, s.nome as status, a.data_atualizacao, a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status = 1
        """
        
        # Base query para CPFs removidos
        base_query_removidos = """
            SELECT a.cpf, a.pa, s.nome as status, a.data_atualizacao, a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status = 6
        """
        
        # Adiciona filtros se fornecidos
        params = []
        if filter_cpf:
            base_query += " AND a.cpf LIKE %s"
            base_query_removidos += " AND a.cpf LIKE %s"
            params.append(f"%{filter_cpf}%")
        if filter_pa:
            base_query += " AND a.pa LIKE %s"
            base_query_removidos += " AND a.pa LIKE %s"
            params.append(f"%{filter_pa}%")
        if filter_nome:
            base_query += " AND a.nome LIKE %s"
            base_query_removidos += " AND a.nome LIKE %s"
            params.append(f"%{filter_nome}%")

        # Adicionar ordenação
        base_query_pendentes = base_query + " ORDER BY a.data_atualizacao DESC"
        base_query_removidos += " ORDER BY a.data_atualizacao DESC"

        # Contar total de registros
        count_query_pendentes = base_query_pendentes.replace("SELECT a.cpf, a.pa, s.nome as status, a.data_atualizacao, a.nome", "SELECT COUNT(*)")
        count_query_removidos = base_query_removidos.replace("SELECT a.cpf, a.pa, s.nome as status, a.data_atualizacao, a.nome", "SELECT COUNT(*)")
        
        # Executar queries de contagem
        conn = get_mysql_connection()
        cursor = conn.cursor()
        cursor.execute(count_query_pendentes, params)
        total_pendentes = cursor.fetchone()[0]
        
        cursor.execute(count_query_removidos, params)
        total_removidos = cursor.fetchone()[0]

        # Adicionar paginação
        offset = (page - 1) * per_page
        base_query_pendentes += " LIMIT %s OFFSET %s"
        base_query_removidos += " LIMIT %s OFFSET %s"
        
        # Parâmetros finais incluindo LIMIT e OFFSET
        final_params = params + [per_page, offset]

        # Usar cursor com dictionary=True para os resultados principais
        cursor.close()
        cursor = conn.cursor(dictionary=True)

        # Executar queries principais
        cursor.execute(base_query_pendentes, final_params)
        cpfs_pendentes = cursor.fetchall()
        
        cursor.execute(base_query_removidos, final_params)
        cpfs_removidos = cursor.fetchall()

        template = render_template('cpfs_pendentes.html',
                             cpfs_pendentes=cpfs_pendentes,
                             cpfs_removidos=cpfs_removidos,
                             page=page,
                             per_page=per_page,
                             total_pendentes=total_pendentes,
                             total_removidos=total_removidos,
                             total_pages_pendentes=(total_pendentes + per_page - 1) // per_page,
                             total_pages_removidos=(total_removidos + per_page - 1) // per_page,
                             start_page=max(1, page - 2),
                             end_page_pendentes=min((total_pendentes + per_page - 1) // per_page, page + 2),
                             end_page_removidos=min((total_removidos + per_page - 1) // per_page, page + 2))
        
        return template

    except Exception as e:
        app.logger.error(f"Erro ao consultar CPFs: {str(e)}")
        flash(f"Erro ao carregar CPFs: {str(e)}", "danger")
        return render_template('cpfs_pendentes.html',
                             cpfs_pendentes=[],
                             cpfs_removidos=[],
                             page=1,
                             per_page=25,
                             total_pendentes=0,
                             total_removidos=0,
                             total_pages_pendentes=1,
                             total_pages_removidos=1,
                             start_page=1,
                             end_page_pendentes=1,
                             end_page_removidos=1)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/cpfs/encaminhados')
@login_required
@module_required('cpfs')
def cpfs_encaminhados():
    # Limpa mensagens flash antigas
    session.pop('_flashes', None)
    
    conn = None
    cursor = None
    try:
        user = User.get_by_id(session['user_id'])
        is_admin = user.is_admin
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)
        filter_cpf = request.args.get('filter_cpf', '')
        filter_pa = request.args.get('filter_pa', '')
        filter_nome = request.args.get('filter_nome', '')
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        
        # Base query para CPFs encaminhados
        base_query = """
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_atualizacao,
                a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status = 2
        """
        
        # Base query para CPFs aprovados
        base_query_aprovados = """
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_aprovacao,
                a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status IN (3, 8)
        """
        
        # Base query para CPFs com erro (apenas para admin)
        base_query_erros = """
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_atualizacao,
                a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status IN (4, 5)
        """
        
        # Adiciona filtros se fornecidos
        params = []
        if filter_cpf:
            base_query += " AND a.cpf LIKE %s"
            base_query_aprovados += " AND a.cpf LIKE %s"
            base_query_erros += " AND a.cpf LIKE %s"
            params.append(f"%{filter_cpf}%")
        if filter_pa:
            base_query += " AND a.pa LIKE %s"
            base_query_aprovados += " AND a.pa LIKE %s"
            base_query_erros += " AND a.pa LIKE %s"
            params.append(f"%{filter_pa}%")
        if filter_nome:
            base_query += " AND a.nome LIKE %s"
            base_query_aprovados += " AND a.nome LIKE %s"
            base_query_erros += " AND a.nome LIKE %s"
            params.append(f"%{filter_nome}%")
            
        # Adiciona filtros de data apenas para aprovados
        params_aprovados = params.copy()
        if data_inicio:
            base_query_aprovados += " AND DATE(a.data_aprovacao) >= %s"
            params_aprovados.append(data_inicio)
        if data_fim:
            base_query_aprovados += " AND DATE(a.data_aprovacao) <= %s"
            params_aprovados.append(data_fim)

        # Adicionar ordenação
        base_query += " ORDER BY a.data_atualizacao DESC"
        base_query_aprovados += " ORDER BY a.data_aprovacao DESC"
        base_query_erros += " ORDER BY a.data_atualizacao DESC"

        # Contar total de registros
        count_query = base_query.replace("""
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_atualizacao,
                a.nome""", "SELECT COUNT(*)")
        count_query_aprovados = base_query_aprovados.replace("""
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_aprovacao,
                a.nome""", "SELECT COUNT(*)")
        count_query_erros = base_query_erros.replace("""
            SELECT 
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_atualizacao,
                a.nome""", "SELECT COUNT(*)")

        # Executar queries de contagem
        conn = get_mysql_connection()
        cursor = conn.cursor()
        cursor.execute(count_query, params)
        total_encaminhados = cursor.fetchone()[0]
        
        cursor.execute(count_query_aprovados, params_aprovados)
        total_aprovados = cursor.fetchone()[0]

        cursor.execute(count_query_erros, params)
        total_erros = cursor.fetchone()[0]

        # Adicionar paginação
        offset = (page - 1) * per_page
        base_query += " LIMIT %s OFFSET %s"
        base_query_aprovados += " LIMIT %s OFFSET %s"
        base_query_erros += " LIMIT %s OFFSET %s"
        
        # Parâmetros finais incluindo LIMIT e OFFSET
        final_params = params + [per_page, offset]
        final_params_aprovados = params_aprovados + [per_page, offset]
        final_params_erros = params + [per_page, offset]

        # Usar cursor com dictionary=True para os resultados principais
        cursor.close()
        cursor = conn.cursor(dictionary=True)

        # Executar queries principais
        cursor.execute(base_query, final_params)
        cpfs_encaminhados = cursor.fetchall()
        
        cursor.execute(base_query_aprovados, final_params_aprovados)
        cpfs_aprovados = cursor.fetchall()

        cursor.execute(base_query_erros, final_params_erros)
        cpfs_erros = cursor.fetchall()

        return render_template('cpfs_encaminhados.html',
                             cpfs_encaminhados=cpfs_encaminhados,
                             cpfs_aprovados=cpfs_aprovados,
                             cpfs_erros=cpfs_erros,
                             page=page,
                             per_page=per_page,
                             total_encaminhados=total_encaminhados,
                             total_aprovados=total_aprovados,
                             total_erros=total_erros,
                             total_pages_encaminhados=(total_encaminhados + per_page - 1) // per_page,
                             total_pages_aprovados=(total_aprovados + per_page - 1) // per_page,
                             total_pages_erros=(total_erros + per_page - 1) // per_page,
                             start_page=max(1, page - 2),
                             end_page_encaminhados=min((total_encaminhados + per_page - 1) // per_page, page + 2),
                             end_page_aprovados=min((total_aprovados + per_page - 1) // per_page, page + 2),
                             end_page_erros=min((total_erros + per_page - 1) // per_page, page + 2),
                             data_inicio=data_inicio,
                             data_fim=data_fim,
                             is_admin=is_admin)

    except Exception as e:
        app.logger.error(f"Erro ao consultar CPFs: {str(e)}")
        flash(f"Erro ao carregar CPFs: {str(e)}", "danger")
        return render_template('cpfs_encaminhados.html',
                             cpfs_encaminhados=[],
                             cpfs_aprovados=[],
                             cpfs_erros=[],
                             page=1,
                             per_page=25,
                             total_encaminhados=0,
                             total_aprovados=0,
                             total_erros=0,
                             total_pages_encaminhados=1,
                             total_pages_aprovados=1,
                             total_pages_erros=1,
                             start_page=1,
                             end_page_encaminhados=1,
                             end_page_aprovados=1,
                             end_page_erros=1,
                             data_inicio=None,
                             data_fim=None,
                             is_admin=is_admin)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/cpfs/renda-ponderada')
@login_required
@module_required('cpfs')
def cpfs_renda_ponderada():
    """Página para exibir CPFs com status renda ponderada"""
    # Limpa mensagens flash antigas
    session.pop('_flashes', None)

    conn = None
    cursor = None
    try:
        user = User.get_by_id(session['user_id'])
        is_admin = user.is_admin

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)
        filter_cpf = request.args.get('filter_cpf', '')
        filter_pa = request.args.get('filter_pa', '')
        filter_nome = request.args.get('filter_nome', '')

        # Base query para CPFs com renda ponderada
        base_query = """
            SELECT
                a.cpf,
                a.pa,
                s.nome as status,
                a.data_atualizacao,
                a.nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.status = 7
        """

        query_params = []

        # Filtro de PA para usuários não admin
        if not is_admin:
            placeholders = ','.join(['%s'] * len(user.pa))
            base_query += f" AND a.pa IN ({placeholders})"
            query_params.extend(user.pa)

        # Aplicar filtros
        if filter_cpf:
            base_query += " AND a.cpf LIKE %s"
            query_params.append(f"%{filter_cpf}%")

        if filter_pa and is_admin:
            base_query += " AND a.pa = %s"
            query_params.append(filter_pa)

        if filter_nome:
            base_query += " AND a.nome LIKE %s"
            query_params.append(f"%{filter_nome}%")

        # Contar total de registros
        count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"

        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute(count_query, query_params)
        total_renda_ponderada = cursor.fetchone()['total']

        # Adicionar paginação
        offset = (page - 1) * per_page
        base_query += " ORDER BY a.data_atualizacao DESC LIMIT %s OFFSET %s"
        query_params.extend([per_page, offset])

        cursor.execute(base_query, query_params)
        cpfs_renda_ponderada = cursor.fetchall()

        return render_template('cpfs_renda_ponderada.html',
                             cpfs_renda_ponderada=cpfs_renda_ponderada,
                             page=page,
                             per_page=per_page,
                             total_renda_ponderada=total_renda_ponderada,
                             total_pages=(total_renda_ponderada + per_page - 1) // per_page,
                             start_page=max(1, page - 2),
                             end_page=min((total_renda_ponderada + per_page - 1) // per_page, page + 2),
                             is_admin=is_admin)

    except Exception as e:
        app.logger.error(f"Erro ao consultar CPFs com renda ponderada: {str(e)}")
        flash(f"Erro ao carregar CPFs: {str(e)}", "danger")
        return render_template('cpfs_renda_ponderada.html',
                             cpfs_renda_ponderada=[],
                             page=1,
                             per_page=25,
                             total_renda_ponderada=0,
                             total_pages=1,
                             start_page=1,
                             end_page=1,
                             is_admin=is_admin)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/cpfs/remover', methods=['POST'])
@login_required
@module_required('cpfs')
@audit_log('CPF', 'REMOVER')
def remover_cpf():
    conn = None
    cursor = None
    try:
        app.logger.info(f"Iniciando remoção de CPF - User: {session.get('username')}")

        # Verificar se a requisição tem dados JSON
        if not request.is_json:
            app.logger.error("Requisição não contém JSON válido")
            return jsonify({'success': False, 'error': 'Requisição deve conter JSON'}), 400

        data = request.get_json()
        if not data:
            app.logger.error("Dados JSON vazios ou inválidos")
            return jsonify({'success': False, 'error': 'Dados JSON inválidos'}), 400

        cpf = data.get('cpf')
        app.logger.info(f"CPF recebido para remoção: {cpf}")

        if not cpf:
            app.logger.error("CPF não fornecido na requisição")
            return jsonify({'success': False, 'error': 'CPF não fornecido'}), 400

        app.logger.info("Estabelecendo conexão com banco de dados...")
        conn = get_mysql_connection()
        if not conn:
            app.logger.error("Falha ao conectar com banco de dados")
            return jsonify({'success': False, 'error': 'Erro de conexão com banco de dados'}), 500

        cursor = conn.cursor()
        
        # Obter o status atual do CPF
        app.logger.info(f"Consultando status atual do CPF: {cpf}")
        cursor.execute("""
            SELECT a.status, s.nome as status_nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.cpf = %s
        """, (cpf,))
        result = cursor.fetchone()

        if not result:
            app.logger.warning(f"CPF não encontrado: {cpf}")
            return jsonify({'success': False, 'error': 'CPF não encontrado'}), 404

        status_anterior_id, status_anterior_nome = result
        app.logger.info(f"Status atual do CPF {cpf}: {status_anterior_nome} (ID: {status_anterior_id})")

        # Obter o ID do status 'removido'
        app.logger.info("Consultando ID do status 'removido'")
        cursor.execute("SELECT id, nome FROM status WHERE nome = 'removido'")
        status_removido = cursor.fetchone()

        if not status_removido:
            app.logger.error("Status 'removido' não encontrado na tabela status")
            return jsonify({'success': False, 'error': 'Status removido não encontrado'}), 400

        status_novo_id, status_novo_nome = status_removido
        app.logger.info(f"Status de destino: {status_novo_nome} (ID: {status_novo_id})")

        # Atualizar o status do CPF para removido
        app.logger.info(f"Atualizando status do CPF {cpf} de {status_anterior_nome} para {status_novo_nome}")
        cursor.execute("""
            UPDATE associados
            SET status = %s, data_atualizacao = NOW()
            WHERE cpf = %s
        """, (status_novo_id, cpf))

        affected_rows = cursor.rowcount
        app.logger.info(f"Linhas afetadas na atualização: {affected_rows}")

        if affected_rows == 0:
            app.logger.warning(f"Nenhuma linha foi atualizada para o CPF: {cpf}")
            return jsonify({'success': False, 'error': 'CPF não foi atualizado'}), 400

        conn.commit()
        app.logger.info(f"CPF {cpf} removido com sucesso")
        return jsonify({'success': True, 'message': f'CPF {cpf} removido com sucesso'})
        
    except mysql.connector.Error as db_error:
        app.logger.error(f"Erro de banco de dados ao remover CPF {cpf}: {str(db_error)}")
        if conn:
            try:
                conn.rollback()
                app.logger.info("Rollback executado com sucesso")
            except Exception as rollback_error:
                app.logger.error(f"Erro no rollback: {str(rollback_error)}")
        return jsonify({'success': False, 'error': f'Erro de banco de dados: {str(db_error)}'}), 500
    except Exception as e:
        app.logger.error(f"Erro geral ao remover CPF {cpf}: {str(e)}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        if conn:
            try:
                conn.rollback()
                app.logger.info("Rollback executado com sucesso")
            except Exception as rollback_error:
                app.logger.error(f"Erro no rollback: {str(rollback_error)}")
        return jsonify({'success': False, 'error': f'Erro interno: {str(e)}'}), 500
    finally:
        try:
            if cursor:
                cursor.close()
                app.logger.debug("Cursor fechado")
            if conn:
                conn.close()
                app.logger.debug("Conexão fechada")
        except Exception as close_error:
            app.logger.error(f"Erro ao fechar conexões: {str(close_error)}")

@app.route('/api/cpfs/restaurar', methods=['POST'])
@login_required
@module_required('cpfs')
@audit_log('CPF', 'RESTAURAR')
def restaurar_cpf():
    conn = None
    cursor = None
    try:
        app.logger.info(f"Iniciando restauração de CPF - User: {session.get('username')}")

        # Verificar se a requisição tem dados JSON
        if not request.is_json:
            app.logger.error("Requisição não contém JSON válido")
            return jsonify({'success': False, 'error': 'Requisição deve conter JSON'}), 400

        data = request.get_json()
        if not data:
            app.logger.error("Dados JSON vazios ou inválidos")
            return jsonify({'success': False, 'error': 'Dados JSON inválidos'}), 400

        cpf = data.get('cpf')
        app.logger.info(f"CPF recebido para restauração: {cpf}")

        if not cpf:
            app.logger.error("CPF não fornecido na requisição")
            return jsonify({'success': False, 'error': 'CPF não fornecido'}), 400

        app.logger.info("Estabelecendo conexão com banco de dados...")
        conn = get_mysql_connection()
        if not conn:
            app.logger.error("Falha ao conectar com banco de dados")
            return jsonify({'success': False, 'error': 'Erro de conexão com banco de dados'}), 500

        cursor = conn.cursor()
        
        # Obter o status atual do CPF
        app.logger.info(f"Consultando status atual do CPF: {cpf}")
        cursor.execute("""
            SELECT a.status, s.nome as status_nome
            FROM associados a
            JOIN status s ON a.status = s.id
            WHERE a.cpf = %s
        """, (cpf,))
        result = cursor.fetchone()

        if not result:
            app.logger.warning(f"CPF não encontrado: {cpf}")
            return jsonify({'success': False, 'error': 'CPF não encontrado'}), 404

        status_anterior_id, status_anterior_nome = result
        app.logger.info(f"Status atual do CPF {cpf}: {status_anterior_nome} (ID: {status_anterior_id})")

        # Obter o ID do status 'pendente'
        app.logger.info("Consultando ID do status 'pendente'")
        cursor.execute("SELECT id, nome FROM status WHERE nome = 'pendente'")
        status_pendente = cursor.fetchone()

        if not status_pendente:
            app.logger.error("Status 'pendente' não encontrado na tabela status")
            return jsonify({'success': False, 'error': 'Status pendente não encontrado'}), 400

        status_novo_id, status_novo_nome = status_pendente
        app.logger.info(f"Status de destino: {status_novo_nome} (ID: {status_novo_id})")

        # Atualizar o status do CPF para pendente
        app.logger.info(f"Atualizando status do CPF {cpf} de {status_anterior_nome} para {status_novo_nome}")
        cursor.execute("""
            UPDATE associados
            SET status = %s, data_atualizacao = NOW()
            WHERE cpf = %s
        """, (status_novo_id, cpf))

        affected_rows = cursor.rowcount
        app.logger.info(f"Linhas afetadas na atualização: {affected_rows}")

        if affected_rows == 0:
            app.logger.warning(f"Nenhuma linha foi atualizada para o CPF: {cpf}")
            return jsonify({'success': False, 'error': 'CPF não foi atualizado'}), 400

        conn.commit()
        app.logger.info(f"CPF {cpf} restaurado com sucesso")
        return jsonify({'success': True, 'message': f'CPF {cpf} restaurado com sucesso'})
        
    except mysql.connector.Error as db_error:
        app.logger.error(f"Erro de banco de dados ao restaurar CPF {cpf}: {str(db_error)}")
        if conn:
            try:
                conn.rollback()
                app.logger.info("Rollback executado com sucesso")
            except Exception as rollback_error:
                app.logger.error(f"Erro no rollback: {str(rollback_error)}")
        return jsonify({'success': False, 'error': f'Erro de banco de dados: {str(db_error)}'}), 500
    except Exception as e:
        app.logger.error(f"Erro geral ao restaurar CPF {cpf}: {str(e)}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        if conn:
            try:
                conn.rollback()
                app.logger.info("Rollback executado com sucesso")
            except Exception as rollback_error:
                app.logger.error(f"Erro no rollback: {str(rollback_error)}")
        return jsonify({'success': False, 'error': f'Erro interno: {str(e)}'}), 500
    finally:
        try:
            if cursor:
                cursor.close()
                app.logger.debug("Cursor fechado")
            if conn:
                conn.close()
                app.logger.debug("Conexão fechada")
        except Exception as close_error:
            app.logger.error(f"Erro ao fechar conexões: {str(close_error)}")

@app.route('/api/rpas', methods=['GET'])
@login_required
def list_rpas():
    rpas = []
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        if os.path.exists(rpas_file):
            with open(rpas_file, 'r') as f:
                rpas = json.load(f)
        
        # Carrega a ordem personalizada
        order_file = os.path.join(app.config['DATA_DIR'], 'rpa_order.json')
        if os.path.exists(order_file):
            with open(order_file, 'r') as f:
                order = json.load(f)
                # Cria um dicionário para mapear id -> rpa
                rpas_dict = {rpa['id']: rpa for rpa in rpas}
                # Reorganiza os RPAs de acordo com a ordem salva
                ordered_rpas = []
                for rpa_id in order:
                    if rpa_id in rpas_dict:
                        ordered_rpas.append(rpas_dict[rpa_id])
                # Adiciona quaisquer RPAs que não estejam na ordem
                for rpa in rpas:
                    if rpa['id'] not in order:
                        ordered_rpas.append(rpa)
                rpas = ordered_rpas
                
    except Exception as e:
        print(f"Erro ao carregar RPAs: {e}")
    return jsonify(rpas)

@app.route('/api/rpas', methods=['POST'])
@login_required
@audit_log('RPA', 'CRIAR')
def add_rpa():
    rpa_data = request.json
    if not rpa_data or not all(k in rpa_data for k in ('name', 'process_name', 'host', 'port')):
        return jsonify({'error': 'Dados incompletos'}), 400
    
    try:
        rpa = create_rpa(rpa_data)
        return jsonify(rpa), 201
    except Exception as e:
        app.logger.error(f"Erro ao criar RPA: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rpas/<rpa_id>/start', methods=['POST'])
@login_required
@audit_log('RPA', 'INICIAR')
def start_rpa(rpa_id):
    try:
        rpa = get_rpa_by_id(rpa_id)
        if not rpa:
            return jsonify({'error': 'RPA não encontrado'}), 404
            
        url = f"http://{rpa['host']}:{rpa['port']}/start"
        response = requests.post(url, timeout=5)
        
        if response.status_code == 200:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Erro ao iniciar RPA'}), response.status_code
            
    except requests.exceptions.RequestException as e:
        app.logger.error(f"Erro ao iniciar RPA {rpa_id}: {str(e)}")
        return jsonify({'error': 'RPA não está respondendo'}), 503
    except Exception as e:
        app.logger.error(f"Erro ao iniciar RPA {rpa_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rpas/<rpa_id>/stop', methods=['POST'])
@login_required
@audit_log('RPA', 'PARAR')
def stop_rpa(rpa_id):
    try:
        rpa = get_rpa_by_id(rpa_id)
        if not rpa:
            return jsonify({'error': 'RPA não encontrado'}), 404
            
        # Obtém o status atual antes de parar
        status_anterior = get_rpa_status(rpa).get('status', 'unknown')
        
        url = f"http://{rpa['host']}:{rpa['port']}/stop"
        response = requests.post(url, timeout=5)
        
        if response.status_code == 200:
            # Registra a alteração no log de auditoria
            audit_logger.log_rpa_change(
                rpa_id=rpa_id,
                status_anterior=status_anterior,
                status_novo='stopped',
                configuracoes={
                    'acao': 'stop',
                    'nome': rpa['name'],
                    'usuario_id': session.get('user_id'),
                    'usuario_nome': session.get('username')
                }
            )
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Erro ao parar RPA'}), response.status_code
            
    except requests.exceptions.RequestException as e:
        app.logger.error(f"Erro ao parar RPA {rpa_id}: {str(e)}")
        return jsonify({'error': 'RPA não está respondendo'}), 503
    except Exception as e:
        app.logger.error(f"Erro ao parar RPA {rpa_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rpas/<rpa_id>', methods=['PUT'])
@login_required
@audit_log('RPA', 'ATUALIZAR')
def update_rpa(rpa_id):
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        if not os.path.exists(rpas_file):
            return jsonify({'error': 'RPA não encontrado'}), 404
            
        with open(rpas_file, 'r') as f:
            rpas = json.load(f)
            
        rpa_index = next((i for i, r in enumerate(rpas) if r['id'] == rpa_id), None)
        if rpa_index is None:
            return jsonify({'error': 'RPA não encontrado'}), 404
            
        rpa_data = request.json
        rpa_data['id'] = rpa_id
        rpas[rpa_index] = rpa_data
        
        with open(rpas_file, 'w') as f:
            json.dump(rpas, f, indent=4)
            
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rpas/<rpa_id>', methods=['DELETE'])
@login_required
@audit_log('RPA', 'EXCLUIR')
def delete_rpa(rpa_id):
    try:
        rpa = get_rpa_by_id(rpa_id)
        if not rpa:
            return jsonify({'error': 'RPA não encontrado'}), 404
            
        # Obtém o status atual antes de deletar
        status_anterior = get_rpa_status(rpa).get('status', 'unknown')
        
        config = load_config()
        if rpa_id in config['rpas']:
            # Registra a exclusão no log de auditoria
            audit_logger.log_rpa_change(
                rpa_id=rpa_id,
                status_anterior=status_anterior,
                status_novo='deleted',
                configuracoes={
                    'acao': 'delete',
                    'nome': rpa['name'],
                    'usuario_id': session.get('user_id'),
                    'usuario_nome': session.get('username')
                }
            )
            
            del config['rpas'][rpa_id]
            save_config(config)
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'RPA não encontrado'}), 404
            
    except Exception as e:
        app.logger.error(f"Erro ao deletar RPA {rpa_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/rpas/status')
@login_required
def get_all_status():
    try:
        rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
        if not os.path.exists(rpas_file):
            return jsonify([])
            
        with open(rpas_file, 'r') as f:
            rpas = json.load(f)
        
        for rpa in rpas:
            rpa['status'] = get_rpa_status(rpa)
            
        return jsonify(rpas)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
@login_required
def get_status():
    rpas_file = os.path.join(app.config['DATA_DIR'], 'rpas.json')
    status_dict = {}
    
    try:
        if os.path.exists(rpas_file):
            with open(rpas_file, 'r') as f:
                rpas = json.load(f)
            
            for rpa in rpas:
                rpa_id = rpa['id']
                status = {'status': 'unknown', 'error': None}
                
                try:
                    status_url = f"http://{rpa['host']}:{rpa['port']}/status"
                    response = requests.get(status_url, timeout=3)
                    
                    if response.status_code == 200:
                        status = response.json()
                    else:
                        status['error'] = f"Erro ao obter status (código {response.status_code})"
                except requests.exceptions.Timeout:
                    status['error'] = "Tempo limite de conexão excedido"
                except requests.exceptions.ConnectionError:
                    status['error'] = "Não foi possível conectar ao servidor RPA"
                except Exception as e:
                    status['error'] = str(e)
                
                status_dict[rpa_id] = status
    except Exception as e:
        print(f"Erro ao obter status dos RPAs: {e}")
    
    return jsonify(status_dict)

@app.route('/api/rpas/order', methods=['GET', 'POST'])
@login_required
def rpa_order():
    order_file = os.path.join(app.config['DATA_DIR'], 'rpa_order.json')
    
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data or 'order' not in data:
                return jsonify({'error': 'Dados de ordem inválidos'}), 400
                
            order = data['order']
            if not isinstance(order, list):
                return jsonify({'error': 'Formato de ordem inválido'}), 400
            
            # Garante que o diretório existe
            os.makedirs(os.path.dirname(order_file), exist_ok=True)
            
            # Salva a ordem
            with open(order_file, 'w') as f:
                json.dump({'order': order, 'updated_at': datetime.now().isoformat()}, f)
            
            return jsonify({'success': True, 'message': 'Ordem salva com sucesso'})
        except Exception as e:
            print(f"Erro ao salvar ordem dos RPAs: {str(e)}")
            return jsonify({'error': 'Erro ao salvar ordem dos RPAs'}), 500
    
    # GET request
    try:
        if os.path.exists(order_file):
            with open(order_file, 'r') as f:
                data = json.load(f)
                return jsonify(data)
        return jsonify({'order': [], 'updated_at': None})
    except Exception as e:
        print(f"Erro ao ler ordem dos RPAs: {str(e)}")
        return jsonify({'error': 'Erro ao ler ordem dos RPAs'}), 500

@app.route('/uploads/associados')
@login_required
@module_required('uploads')
def uploads_associados():
    return render_template('uploads_associados.html')

@app.route('/uploads/limites')
@login_required
@module_required('uploads')
def uploads_limites():
    return render_template('uploads_limites.html')

@app.route('/upload/associados', methods=['POST'])
@login_required
@module_required('uploads')
def upload_associados():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith('.xlsx'):
        return jsonify({'error': 'Arquivo deve ser um Excel (.xlsx)'}), 400

    # Salva o arquivo temporariamente
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], f"{str(uuid.uuid4())}_{filename}")
    file.save(filepath)
    
    # Armazena o caminho do arquivo na sessão
    session['associados_file'] = filepath
    
    return jsonify({'message': 'Arquivo recebido com sucesso'})

@app.route('/executar/associados', methods=['POST'])
@login_required
@module_required('uploads')
def executar_associados():
    """Executa o processamento da planilha de associados"""
    try:
        # Verifica se existe um arquivo na sessão
        if 'associados_file' not in session:
            return jsonify({'error': 'Nenhum arquivo encontrado'}), 400
            
        filepath = session['associados_file']
        if not os.path.exists(filepath):
            return jsonify({'error': 'Arquivo não encontrado'}), 404
            
        # Lê a planilha
        df = pd.read_excel(filepath)
        print(f"Lidas {len(df)} linhas da planilha")

        # Mapeamento de possíveis nomes de colunas
        column_mappings = {
            'Data Movimento': ['Data Movimento', 'Data_Movimento', 'DataMovimento', 'Data'],
            'Número PA': ['Número PA', 'Numero PA', 'Número_PA', 'Numero_PA', 'NumeroPA', 'PA'],
            'CPF/CNPJ': ['CPF/CNPJ', 'CPF', 'CNPJ', 'CPF_CNPJ', 'CPFCNPJ'],
            'Nome Cliente': ['Nome Cliente', 'Nome_Cliente', 'NomeCliente', 'Nome'],
            'Telefone': ['Telefone', 'Tel', 'Fone', 'Telefone_Cliente', 'TelefoneCliente', 'Telefone Celular'],
            'Data da Última Renovação Cadastral': ['Data da Última Renovação Cadastral', 'Data Ultima Renovacao', 'DataUltimaRenovacao', 'Ultima_Renovacao'],
            'Valor da Renda Bruta Mensal': ['Valor da Renda Bruta Mensal', 'Valor Renda Bruta Mensal', 'Renda Bruta Mensal', 'Renda_Bruta_Mensal', 'RendaBrutaMensal', 'Renda'],
            'Código CNAE': ['Código CNAE', 'Codigo CNAE', 'Código_CNAE', 'Codigo_CNAE', 'CodigoCNAE'],
            'CNAE': ['CNAE', 'Descricao_CNAE', 'Descricao CNAE'],
            'Tipo de Renda': ['Tipo de Renda', 'Tipo_de_Renda', 'TipoDeRenda', 'Tipo Renda', 'TipoRenda']
        }

        # Função para encontrar a coluna correspondente
        def find_column(possible_names, df_columns):
            for name in possible_names:
                if name in df_columns:
                    return name
            return None

        # Mapeia as colunas encontradas
        column_map = {}
        missing_columns = []
        
        for required_col, possible_names in column_mappings.items():
            found_col = find_column(possible_names, df.columns)
            if found_col:
                column_map[required_col] = found_col
            else:
                missing_columns.append(required_col)

        if missing_columns:
            return jsonify({'error': f'Colunas ausentes: {", ".join(missing_columns)}'}), 400
        
        # Renomeia as colunas para os nomes padrão
        df = df.rename(columns={v: k for k, v in column_map.items()})
        
        # Formatação do CPF/CNPJ (remove caracteres especiais e preenche com zeros à esquerda)
        df['CPF/CNPJ'] = df['CPF/CNPJ'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))

        # Converte as datas para o formato correto do MySQL
        df['Data Movimento'] = pd.to_datetime(df['Data Movimento'], dayfirst=True).dt.strftime('%Y-%m-%d')
        df['Data da Última Renovação Cadastral'] = pd.to_datetime(df['Data da Última Renovação Cadastral'], dayfirst=True).dt.strftime('%Y-%m-%d')

        # Converte valores nulos para None
        df = df.replace({pd.NaT: None, pd.NA: None, np.nan: None})

        # Processa agrupamento por CPF para concatenar tipos de renda
        print(f"Processando agrupamento por CPF para concatenar tipos de renda...")

        # Agrupa por CPF e concatena os tipos de renda, mantendo os outros dados do registro mais recente
        df_grouped = df.groupby('CPF/CNPJ').agg({
            'Data Movimento': 'max',  # Pega a data mais recente
            'Número PA': 'first',
            'Nome Cliente': 'first',
            'Telefone': 'first',
            'Data da Última Renovação Cadastral': 'max',  # Pega a data mais recente
            'Valor da Renda Bruta Mensal': 'first',
            'Código CNAE': 'first',
            'CNAE': 'first',
            'Tipo de Renda': lambda x: ' | '.join(sorted(set(str(val) for val in x if pd.notna(val) and str(val).strip() != '')))
        }).reset_index()

        # Substitui o DataFrame original pelo agrupado
        df = df_grouped
        print(f"Após agrupamento: {len(df)} registros únicos por CPF")
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Atualiza a data mais recente na tabela database
        data_movimento_max = pd.to_datetime(df['Data Movimento']).max()
        if pd.notna(data_movimento_max):
            cursor.execute("""
                UPDATE `database` 
                SET data_associados = %s 
                WHERE id = 1
            """, (data_movimento_max.strftime('%Y-%m-%d %H:%M:%S'),))
            conn.commit()
        
        # Conta quantos CPFs existem na tabela
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa")
        total_cpfs_antes = cursor.fetchone()[0]
        
        # Processa em lotes de 500 registros
        batch_size = 500
        total_rows = len(df)
        registros_atualizados = 0
        registros_inseridos = 0
        
        for i in range(0, total_rows, batch_size):
            batch_df = df.iloc[i:i + batch_size]
            
            for _, row in batch_df.iterrows():
                # Verifica se o CPF já existe
                cursor.execute("SELECT cpf_cnpj FROM associados_completa WHERE cpf_cnpj = %s", (row['CPF/CNPJ'],))
                exists = cursor.fetchone()
                
                if exists:
                    # Atualiza o registro existente
                    cursor.execute("""
                        UPDATE associados_completa
                        SET data_movimento = %s,
                            numero_pa = %s,
                            nome_cliente = %s,
                            telefone = CASE
                                WHEN (telefone IS NULL OR telefone = '' OR telefone != %s)
                                THEN %s
                                ELSE telefone
                            END,
                            data_ultima_renovacao = %s,
                            valor_renda_bruta_mensal = %s,
                            codigo_cnae = %s,
                            cnae = %s,
                            tipo_renda = %s
                        WHERE cpf_cnpj = %s
                    """, (
                        row['Data Movimento'],
                        row['Número PA'],
                        row['Nome Cliente'],
                        row['Telefone'],
                        row['Telefone'],
                        row['Data da Última Renovação Cadastral'],
                        row['Valor da Renda Bruta Mensal'],
                        row['Código CNAE'],
                        row['CNAE'],
                        row['Tipo de Renda'],
                        row['CPF/CNPJ']
                    ))
                    
                    # Determina o status baseado no tipo de renda
                    novo_status = 7 if 'RENDA PONDERADA' in str(row['Tipo de Renda']).upper() else None

                    # Atualiza o nome, telefone e tipo de renda na tabela associados
                    if novo_status:
                        cursor.execute("""
                            UPDATE associados
                            SET nome = CASE
                                    WHEN (nome IS NULL OR nome = '')
                                    THEN %s
                                    ELSE nome
                                END,
                                telefone = CASE
                                    WHEN (telefone IS NULL OR telefone = '' OR telefone != %s)
                                    THEN %s
                                    ELSE telefone
                                END,
                                tipo_renda = %s,
                                status = %s
                            WHERE cpf = %s
                        """, (row['Nome Cliente'], row['Telefone'], row['Telefone'], row['Tipo de Renda'], novo_status, row['CPF/CNPJ']))
                    else:
                        cursor.execute("""
                            UPDATE associados
                            SET nome = CASE
                                    WHEN (nome IS NULL OR nome = '')
                                    THEN %s
                                    ELSE nome
                                END,
                                telefone = CASE
                                    WHEN (telefone IS NULL OR telefone = '' OR telefone != %s)
                                    THEN %s
                                    ELSE telefone
                                END,
                                tipo_renda = %s
                            WHERE cpf = %s
                        """, (row['Nome Cliente'], row['Telefone'], row['Telefone'], row['Tipo de Renda'], row['CPF/CNPJ']))
                    
                    registros_atualizados += 1
                else:
                    # Insere novo registro
                    cursor.execute("""
                        INSERT INTO associados_completa (
                            data_movimento,
                            numero_pa,
                            cpf_cnpj,
                            nome_cliente,
                            telefone,
                            data_ultima_renovacao,
                            valor_renda_bruta_mensal,
                            codigo_cnae,
                            cnae,
                            tipo_renda
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        row['Data Movimento'],
                        row['Número PA'],
                        row['CPF/CNPJ'],
                        row['Nome Cliente'],
                        row['Telefone'],
                        row['Data da Última Renovação Cadastral'],
                        row['Valor da Renda Bruta Mensal'],
                        row['Código CNAE'],
                        row['CNAE'],
                        row['Tipo de Renda']
                    ))
                    registros_inseridos += 1
            
            conn.commit()
            print(f"Processado lote de {len(batch_df)} registros ({i + len(batch_df)} de {total_rows})")
            print(f"Atualizados: {registros_atualizados}, Inseridos: {registros_inseridos}")
        
        # Conta quantos CPFs existem na tabela após o processamento
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa")
        total_cpfs_depois = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        # Remove o arquivo temporário
        os.remove(filepath)
        session.pop('associados_file', None)
        
        return jsonify({
            'message': 'Processamento concluído com sucesso',
            'estatisticas': {
                'total_linhas_planilha': len(df),
                'cpfs_unicos_planilha': len(df['CPF/CNPJ'].unique()),
                'cpfs_antes': total_cpfs_antes,
                'cpfs_depois': total_cpfs_depois,
                'registros_atualizados': registros_atualizados,
                'registros_inseridos': registros_inseridos
            }
        })
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de associados: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/upload/credito', methods=['POST'])
@login_required
@module_required('uploads')
def upload_credito():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'error': 'Formato de arquivo inválido'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['DATA_DIR'], 'temp', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    file.save(filepath)
    
    session['credito_file'] = filepath
    return jsonify({'message': 'Upload realizado com sucesso'})

@app.route('/executar/credito', methods=['POST'])
@login_required
@module_required('uploads')
def executar_credito():
    filepath = session.get('credito_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    try:
        df = pd.read_excel(filepath)
        
        # Formatação do CPF/CNPJ
        df['CPF/CNPJ'] = df['CPF/CNPJ'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Cria a tabela se não existir
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS credito (
                id INT AUTO_INCREMENT PRIMARY KEY,
                cooperativa VARCHAR(50),
                pa VARCHAR(50),
                cpf_cnpj VARCHAR(14),
                nome VARCHAR(255),
                tipo VARCHAR(50),
                portifolio VARCHAR(100),
                perfil_cls VARCHAR(50),
                limite_disponivel_cls DECIMAL(15,2),
                parcela_maxima DECIMAL(15,2),
                lin_cred_automatico VARCHAR(50),
                lin_pix_parcelado VARCHAR(50),
                data_hora_processamento DATETIME,
                UNIQUE KEY uk_cpf_cnpj (cpf_cnpj)
            )
        """)
        
        # Atualiza registros existentes e insere novos
        for _, row in df.iterrows():
            cursor.execute("""
                INSERT INTO credito (
                    cooperativa, pa, cpf_cnpj, nome, tipo, portifolio,
                    perfil_cls, limite_disponivel_cls, parcela_maxima,
                    lin_cred_automatico, lin_pix_parcelado, data_hora_processamento
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    cooperativa = VALUES(cooperativa),
                    pa = VALUES(pa),
                    nome = VALUES(nome),
                    tipo = VALUES(tipo),
                    portifolio = VALUES(portifolio),
                    perfil_cls = VALUES(perfil_cls),
                    limite_disponivel_cls = VALUES(limite_disponivel_cls),
                    parcela_maxima = VALUES(parcela_maxima),
                    lin_cred_automatico = VALUES(lin_cred_automatico),
                    lin_pix_parcelado = VALUES(lin_pix_parcelado),
                    data_hora_processamento = VALUES(data_hora_processamento)
            """, (
                row['Cooperativa'], row['PA'], row['CPF/CNPJ'], row['Nome'],
                row['Tipo'], row['Portifólio'], row['Perfil CLS'],
                row['Limite Disponível CLS'], row['Parcela Máxima'],
                row['Lin. Créd. Automático'], row['Lin. PIX Parcelado'],
                row['Data hora Processamento']
            ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        os.remove(filepath)
        session.pop('credito_file', None)
        
        return jsonify({'message': 'Processamento concluído com sucesso'})
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de crédito: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/upload/score', methods=['POST'])
@login_required
@module_required('uploads')
def upload_score():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith('.xlsx'):
        return jsonify({'error': 'Arquivo deve ser um Excel (.xlsx)'}), 400

    # Salva o arquivo temporariamente
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], f"{str(uuid.uuid4())}_{filename}")
    file.save(filepath)
    
    # Armazena o caminho do arquivo na sessão
    session['score_file'] = filepath
    
    return jsonify({'message': 'Arquivo recebido com sucesso'})

@app.route('/executar/score', methods=['POST'])
@login_required
@module_required('uploads')
def executar_score():
    filepath = session.get('score_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    try:
        app.logger.info("Iniciando processamento da planilha de score...")
        
        # Lê todas as abas da planilha
        excel_file = pd.ExcelFile(filepath)
        all_sheets = excel_file.sheet_names
        app.logger.info(f"Encontradas {len(all_sheets)} abas na planilha: {', '.join(all_sheets)}")
        
        # DataFrame para armazenar todos os dados
        df_final = pd.DataFrame()
        
        # Processa cada aba
        for sheet in all_sheets:
            app.logger.info(f"\nProcessando aba: {sheet}")
            # Lê a planilha começando da linha 7 (header)
            df_sheet = pd.read_excel(filepath, sheet_name=sheet, header=6)
            app.logger.info(f"- Linhas encontradas: {len(df_sheet)}")
            app.logger.info(f"- Colunas encontradas: {', '.join(df_sheet.columns)}")
            df_final = pd.concat([df_final, df_sheet], ignore_index=True)
        
        app.logger.info(f"\nTotal de linhas após concatenação: {len(df_final)}")
        
        # Remove linhas com CPF nulo ou vazio
        total_antes = len(df_final)
        df_final = df_final.dropna(subset=['CPF'])
        app.logger.info(f"Linhas removidas por CPF nulo: {total_antes - len(df_final)}")
        
        # Formatação do CPF (remove caracteres especiais e preenche com zeros à esquerda)
        df_final['CPF'] = df_final['CPF'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        # Remove CPFs inválidos (menos de 11 dígitos após limpeza)
        total_antes = len(df_final)
        df_final = df_final[df_final['CPF'].str.len() == 11]
        app.logger.info(f"Linhas removidas por CPF inválido: {total_antes - len(df_final)}")
        
        # Converte Score para inteiro, tratando valores nulos
        df_final['Score'] = pd.to_numeric(df_final['Score'], errors='coerce').fillna(0).astype(int)
        app.logger.info("\nDistribuição de Scores na planilha:")
        app.logger.info(df_final['Score'].value_counts().sort_index().to_string())
        
        # Verifica scores >= 500
        scores_altos = df_final[df_final['Score'] >= 500]
        app.logger.info(f"\nCPFs com score >= 500 na planilha: {len(scores_altos)}")
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        
        # Verifica CPFs existentes na tabela associados_completa
        cpfs_list = df_final['CPF'].tolist()
        placeholders = ','.join(['%s'] * len(cpfs_list))
        cursor.execute(f"""
            SELECT cpf_cnpj 
            FROM associados_completa 
            WHERE cpf_cnpj IN ({placeholders})
        """, cpfs_list)
        cpfs_existentes = {row[0] for row in cursor.fetchall()}
        app.logger.info(f"CPFs encontrados na tabela associados_completa: {len(cpfs_existentes)}")
        
        # Atualiza o score apenas para CPFs existentes na tabela associados_completa
        atualizados = 0
        total_processados = 0
        
        # Processa em lotes de 1000 registros para melhor performance
        for i in range(0, len(df_final), 1000):
            batch = df_final.iloc[i:i+1000]
            total_processados += len(batch)
            
            for _, row in batch.iterrows():
                if row['CPF'] in cpfs_existentes:
                    app.logger.info(f"Atualizando CPF {row['CPF']} com score {row['Score']}")
                    cursor.execute("""
                        UPDATE associados_completa 
                        SET score = %s 
                        WHERE cpf_cnpj = %s
                    """, (row['Score'], row['CPF']))
                    if cursor.rowcount > 0:
                        atualizados += 1
            
            conn.commit()
            app.logger.info(f"Processados {total_processados} de {len(df_final)} registros. Atualizados até agora: {atualizados}")
        
        # Verifica quantos foram atualizados
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa WHERE score IS NOT NULL")
        total_scores = cursor.fetchone()[0]
        app.logger.info(f"Total de registros com score após atualização: {total_scores}")
        
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa WHERE score >= 500")
        total_scores_500 = cursor.fetchone()[0]
        app.logger.info(f"Total de registros com score >= 500: {total_scores_500}")

        cursor.close()
        conn.close()
        
        # Limpa o arquivo temporário
        os.remove(filepath)
        session.pop('score_file', None)
        
        # Retorna estatísticas do processamento
        return jsonify({
            'message': 'Processamento concluído com sucesso',
            'estatisticas': {
                'total_abas_processadas': len(all_sheets),
                'total_linhas_planilha': len(df_final),
                'cpfs_unicos_planilha': len(df_final['CPF'].unique()),
                'scores_atualizados': atualizados,
                'total_associados_com_score': total_scores,
                'total_scores_500_mais': total_scores_500
            }
        })
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de score: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/login', methods=['GET', 'POST'])
@audit_log('USER', 'LOGIN')
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        auth_type = request.form.get('auth_type', 'ad')  # Default para AD
        
        if not username or not password:
            flash('Por favor, preencha todos os campos', 'warning')
            return redirect(url_for('login'))
        
        if auth_type == 'local':
            user = User.authenticate(username, password)
            if user:
                if not user.is_active:
                    flash('Usuário inativo. Entre em contato com o administrador.', 'warning')
                    return redirect(url_for('login'))
                    
                session.clear()
                session['user_id'] = user.id
                session['username'] = user.username
                session['is_admin'] = user.is_admin
                session['last_activity'] = datetime.now().timestamp()
                session['permissions'] = user.permissions
                session['pa'] = user.pa
                session.modified = True
                
                if user.first_login:
                    return redirect(url_for('change_password'))
                return redirect(url_for('dashboard'))
            else:
                flash('Usuário ou senha inválidos', 'danger')
                return redirect(url_for('login'))
        else:
            # Autenticação AD
            try:
                ad_auth = ADAuth()
                success, user_data = ad_auth.authenticate(username, password)
                
                if not success:
                    flash('Credenciais inválidas. Verifique seu usuário e senha.', 'danger')
                    return redirect(url_for('login'))
                
                if not user_data:
                    flash('Não foi possível obter os dados do usuário no AD.', 'danger')
                    return redirect(url_for('login'))

                # Verifica se o usuário tem e-mail cadastrado
                if not user_data.get('email'):
                    flash('Você não possui e-mail cadastrado no AD. Entre em contato com o administrador.', 'warning')
                    return redirect(url_for('login'))
                
                # Verifica se o usuário já existe no banco
                conn = get_mysql_connection()
                cursor = conn.cursor(dictionary=True)
                cursor.execute(
                    'SELECT id, is_active FROM users WHERE username = %s AND is_ad_user = 1',
                    (username,)
                )
                existing_user = cursor.fetchone()
                conn.close()

                # Se o usuário não existe
                if not existing_user:
                    # Cria o usuário como inativo
                    user = ad_auth.ensure_ad_user_in_db(user_data)
                    flash('Usuário cadastrado com sucesso!', 'success')
                    flash('Seu acesso está pendente de aprovação. Por favor, entre em contato com o administrador.', 'warning')
                    return redirect(url_for('login'))
                
                # Se o usuário existe mas está inativo
                if not existing_user['is_active']:
                    flash('Seu acesso está pendente de aprovação. Por favor, entre em contato com o administrador.', 'warning')
                    return redirect(url_for('login'))

                # Se chegou aqui, o usuário existe e está ativo
                user = ad_auth.ensure_ad_user_in_db(user_data)
                if user:
                    # Gera e envia o código 2FA
                    code = generate_2fa_code()
                    session['2fa_code'] = code
                    session['2fa_timestamp'] = datetime.now().timestamp()
                    session['2fa_email'] = user_data['email']
                    session['temp_user_data'] = {
                        'id': user.id,
                        'username': user.username,
                        'is_admin': user.is_admin,
                        'permissions': user.permissions,
                        'pa': user.pa
                    }
                    
                    if send_2fa_email(user_data['email'], code):
                        return redirect(url_for('verify_2fa'))
                    else:
                        flash('Erro ao enviar o código de verificação. Por favor, tente novamente.', 'danger')
                        return redirect(url_for('login'))
                else:
                    flash('Erro ao processar o login. Por favor, tente novamente.', 'danger')
                    return redirect(url_for('login'))
            except Exception as e:
                app.logger.error(f'Erro na autenticação AD: {str(e)}')
                flash('Erro ao processar a autenticação. Por favor, tente novamente.', 'danger')
                return redirect(url_for('login'))
    
    return render_template('login.html')

@app.route('/verify-2fa', methods=['GET', 'POST'])
def verify_2fa():
    if '2fa_code' not in session or '2fa_timestamp' not in session:
        flash('Sessão expirada. Por favor, faça login novamente.', 'warning')
        return redirect(url_for('login'))
    
    # Verifica se o código expirou (5 minutos)
    if datetime.now().timestamp() - session['2fa_timestamp'] > 300:
        session.pop('2fa_code', None)
        session.pop('2fa_timestamp', None)
        session.pop('2fa_email', None)
        session.pop('temp_user_data', None)
        flash('Código expirado. Por favor, faça login novamente.', 'warning')
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        code = request.form.get('code')
        if code == session['2fa_code']:
            # Limpa dados temporários
            session.pop('2fa_code', None)
            session.pop('2fa_timestamp', None)
            session.pop('2fa_email', None)
            
            # Configura a sessão do usuário
            user_data = session.pop('temp_user_data', None)
            if user_data:
                session['user_id'] = user_data['id']
                session['username'] = user_data['username']
                session['is_admin'] = user_data['is_admin']
                session['last_activity'] = datetime.now().timestamp()
                session['permissions'] = user_data['permissions']
                session['pa'] = user_data['pa']
                session.modified = True
                
                # Redireciona com base nas permissões
                if user_data['is_admin']:
                    return redirect(url_for('dashboard'))
                
                available_modules = ['dashboard', 'robozometro', 'rpa_monitor', 'cpfs', 'uploads', 'relatorios', 'crls']
                for module in available_modules:
                    if module in user_data['permissions']:
                        return redirect(url_for(module))
                
                flash('Você não tem permissão para acessar nenhum módulo.', 'warning')
                return redirect(url_for('logout'))
            else:
                flash('Erro ao processar a verificação. Por favor, tente novamente.', 'danger')
                return redirect(url_for('login'))
        else:
            flash('Código inválido. Por favor, tente novamente.', 'danger')
    
    return render_template('2fa.html')

@app.route('/resend-2fa')
def resend_2fa():
    if '2fa_email' not in session or 'temp_user_data' not in session:
        flash('Sessão expirada. Por favor, faça login novamente.', 'warning')
        return redirect(url_for('login'))
    
    # Gera novo código
    code = generate_2fa_code()
    session['2fa_code'] = code
    session['2fa_timestamp'] = datetime.now().timestamp()
    
    if send_2fa_email(session['2fa_email'], code):
        flash('Novo código enviado com sucesso!', 'success')
    else:
        flash('Erro ao enviar o código. Por favor, tente novamente.', 'danger')
    
    return redirect(url_for('verify_2fa'))

@app.route('/login-local')
def login_local():
    """Rota oculta para login local"""
    return render_template('login-local.html')

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
@audit_log('USER', 'ALTERAR_SENHA')
def change_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        user = User.get_by_id(session['user_id'])
        if not user:
            flash('Usuário não encontrado', 'danger')
            return redirect(url_for('logout'))
            
        # Validações de senha
        if not current_password or not new_password or not confirm_password:
            flash('Todos os campos são obrigatórios', 'danger')
            return redirect(url_for('change_password'))
            
        if not user.check_password(current_password):
            flash('Senha atual incorreta', 'danger')
            return redirect(url_for('change_password'))
            
        if new_password != confirm_password:
            flash('As senhas não conferem', 'danger')
            return redirect(url_for('change_password'))
            
        # Validação de força da senha
        if len(new_password) < 8:
            flash('A nova senha deve ter pelo menos 8 caracteres', 'danger')
            return redirect(url_for('change_password'))
            
        if not re.search("[a-z]", new_password):
            flash('A senha deve conter pelo menos uma letra minúscula', 'danger')
            return redirect(url_for('change_password'))
            
        if not re.search("[A-Z]", new_password):
            flash('A senha deve conter pelo menos uma letra maiúscula', 'danger')
            return redirect(url_for('change_password'))
            
        if not re.search("[0-9]", new_password):
            flash('A senha deve conter pelo menos um número', 'danger')
            return redirect(url_for('change_password'))
            
        if not re.search("[!@#$%^&*(),.?\":{}|<>]", new_password):
            flash('A senha deve conter pelo menos um caractere especial', 'danger')
            return redirect(url_for('change_password'))
            
        if new_password == current_password:
            flash('A nova senha não pode ser igual à senha atual', 'danger')
            return redirect(url_for('change_password'))
            
        try:
            user.set_password(new_password)
            user.first_login = False  # Define como False após a troca de senha
            
            if user.save():
                # Limpa a sessão e redireciona para o login
                session.clear()
                flash('Senha alterada com sucesso. Por favor, faça login com sua nova senha.', 'success')
                return redirect(url_for('login'))
                
            flash('Erro ao alterar senha', 'danger')
            
        except Exception as e:
            app.logger.error(f"Erro ao alterar senha: {str(e)}")
            flash('Erro ao processar alteração de senha', 'danger')
            
    return render_template('change_password.html')

@app.route('/logout')
@audit_log('USER', 'LOGOUT')
def logout():
    # Limpa e invalida a sessão
    session.clear()
    return redirect(url_for('login'))

# Middleware para verificar timeout da sessão
@app.before_request
def check_session_timeout():
    if request.endpoint == 'static':
        return
        
    if 'user_id' in session:
        last_activity = session.get('last_activity', 0)
        current_time = time.time()
        
        # Se passou mais de 8 horas desde a última atividade
        if current_time - last_activity > app.config['PERMANENT_SESSION_LIFETIME'].total_seconds():
            session.clear()
            flash('Sua sessão expirou. Por favor, faça login novamente', 'warning')
            return redirect(url_for('login'))
            
        # Atualiza o timestamp da última atividade
        session['last_activity'] = current_time
        session.modified = True  # Força a atualização da sessão
        
        # Verifica se o usuário ainda existe e tem as permissões necessárias
        try:
            user = User.get_by_id(session['user_id'])
            if not user:
                session.clear()
                flash('Usuário não encontrado. Por favor, faça login novamente', 'warning')
                return redirect(url_for('login'))
                
            # Atualiza as permissões na sessão
            session['permissions'] = user.permissions
            session['is_admin'] = user.is_admin
            session['pa'] = user.pa
            session.modified = True
        except Exception as e:
            app.logger.error(f"Erro ao verificar usuário: {str(e)}")
            session.clear()
            flash('Erro ao verificar sessão. Por favor, faça login novamente', 'warning')
            return redirect(url_for('login'))

# Cria o Blueprint de usuários
usuarios_bp = Blueprint('usuarios', __name__, url_prefix='/usuarios')

@usuarios_bp.route('/')
@login_required
@admin_required
def usuarios():
    users = User.get_all()
    available_pas = ['0', '2', '3', '4', '5', '6', '7', '9', '10', '11', '12', 
                     '15', '16', '17', '18', '20', '21', '22', '23', '24', '25', '26', '97']
    return render_template('usuarios.html', users=users, available_pas=available_pas)

@usuarios_bp.route('/adicionar_usuario', methods=['POST'])
@login_required
@admin_required
@audit_log('USER', 'ADICIONAR')
def adicionar_usuario():
    """Adiciona um novo usuário"""
    try:
        name = request.form.get('name')
        username = request.form.get('username')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        pas = request.form.getlist('pa')  # Recebe uma lista de PAs
        modules = request.form.getlist('modules')
        
        # Se for admin, garante que tem todas as permissões
        if is_admin:
            modules = ['dashboard', 'rpa_monitor', 'cpfs', 'uploads', 
                      'robozometro', 'relatorios', 'crls', 'audit']
        
        user = User.create(name, username, password, is_admin, modules, pas)
        if user:
            flash('Usuário adicionado com sucesso', 'success')
        else:
            flash('Erro ao adicionar usuário', 'danger')
            
    except Exception as e:
        flash(f'Erro ao adicionar usuário: {str(e)}', 'danger')
    
    return redirect(url_for('usuarios.usuarios'))

@usuarios_bp.route('/editar/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
@audit_log('USER', 'EDITAR')
def editar_usuario(user_id):
    user = User.get_by_id(user_id)
    if not user:
        flash('Usuário não encontrado.', 'danger')
        return redirect(url_for('usuarios.usuarios'))

    if request.method == 'GET':
        return jsonify({
            'name': user.name,
            'username': user.username,
            'is_admin': user.is_admin,
            'is_ad_user': user.is_ad_user,
            'is_active': user.is_active,
            'pa': user.pa,
            'permissions': user.permissions
        })

    # POST - Atualiza o usuário
    try:
        name = request.form.get('name')
        username = request.form.get('username')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        pa = request.form.getlist('pa')
        modules = request.form.getlist('modules')

        app.logger.info(f"Dados recebidos para edição do usuário {user_id}:")
        app.logger.info(f"Nome: {name}")
        app.logger.info(f"Username: {username}")
        app.logger.info(f"Is Admin: {is_admin}")
        app.logger.info(f"PAs: {pa}")
        app.logger.info(f"Módulos: {modules}")

        # Se o usuário é admin, dá acesso a todos os módulos
        if is_admin:
            modules = ['dashboard', 'rpa_monitor', 'cpfs', 'uploads', 
                      'robozometro', 'relatorios', 'crls', 'audit']
            app.logger.info(f"Usuário é admin, módulos atualizados para: {modules}")

        # Atualiza o usuário
        success = user.update(
            name=name,
            username=username,
            password=password if password else None,
            is_admin=is_admin,
            permissions=modules,
            pas=pa
        )

        if success:
            flash('Usuário atualizado com sucesso!', 'success')
            return jsonify({'success': True})
        else:
            flash('Erro ao atualizar usuário', 'danger')
            return jsonify({'success': False, 'error': 'Erro ao atualizar usuário'})

    except Exception as e:
        app.logger.error(f'Erro ao atualizar usuário: {str(e)}')
        return jsonify({'success': False, 'error': str(e)})

@usuarios_bp.route('/excluir/<int:user_id>', methods=['POST'])
@login_required
@admin_required
@audit_log('USER', 'EXCLUIR')
def excluir_usuario(user_id):
    try:
        user = User.get_by_id(user_id)
        if user and user.delete():
            flash('Usuário excluído com sucesso', 'success')
        else:
            flash('Erro ao excluir usuário', 'danger')
    except Exception as e:
        flash(f'Erro ao excluir usuário: {str(e)}', 'danger')
    
    return redirect(url_for('usuarios.usuarios'))

@usuarios_bp.route('/excluir_ad/<int:user_id>', methods=['POST'])
@login_required
@admin_required
@audit_log('USER', 'EXCLUIR_AD')
def excluir_usuario_ad(user_id):
    """Exclui um usuário AD do sistema"""
    try:
        user = User.get_by_id(user_id)
        if not user:
            flash('Usuário não encontrado', 'danger')
            return redirect(url_for('usuarios.usuarios'))
            
        if not user.is_ad_user:
            flash('Este usuário não é um usuário AD', 'danger')
            return redirect(url_for('usuarios.usuarios'))
            
        if user.delete():
            flash('Usuário AD excluído com sucesso', 'success')
        else:
            flash('Erro ao excluir usuário AD', 'danger')
            
    except Exception as e:
        flash(f'Erro ao excluir usuário AD: {str(e)}', 'danger')
    
    return redirect(url_for('usuarios.usuarios'))

@usuarios_bp.route('/toggle_active/<int:user_id>', methods=['POST'])
@login_required
@admin_required
@audit_log('USER', 'TOGGLE_ACTIVE')
def toggle_user_active(user_id):
    """Ativa/desativa um usuário"""
    try:
        user = User.get_by_id(user_id)
        if not user:
            flash('Usuário não encontrado', 'danger')
            return redirect(url_for('usuarios.usuarios'))
        
        # Inverte o status atual
        user.is_active = 0 if user.is_active else 1
        
        if user.save():
            status = 'ativado' if user.is_active else 'desativado'
            flash(f'Usuário {status} com sucesso', 'success')
        else:
            flash('Erro ao alterar status do usuário', 'danger')
            
    except Exception as e:
        flash(f'Erro ao alterar status do usuário: {str(e)}', 'danger')
    
    return redirect(url_for('usuarios.usuarios'))

# Registra o Blueprint
app.register_blueprint(usuarios_bp)

@app.route('/upload/limites', methods=['POST'])
@login_required
@module_required('uploads')
def upload_limites():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith('.csv'):
        return jsonify({'error': 'Formato de arquivo inválido. Use .csv'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    session['limites_file'] = filepath
    return jsonify({'message': 'Upload realizado com sucesso'})

@app.route('/executar/limites', methods=['POST'])
@login_required
@module_required('uploads')
def executar_limites():
    filepath = session.get('limites_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    try:
        app.logger.info("Iniciando processamento da planilha de limites...")
        
        # Tenta diferentes codificações
        encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
        df = None
        
        for encoding in encodings:
            try:
                app.logger.info(f"Tentando ler o arquivo com encoding {encoding}...")
                df = pd.read_csv(filepath, encoding=encoding, sep=';', decimal=',')
                app.logger.info(f"Arquivo lido com sucesso usando encoding {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            raise ValueError("Não foi possível ler o arquivo com nenhuma das codificações tentadas")
        
        # Validação das colunas necessárias
        required_columns = ['CPF/CNPJ', 'PA', 'Nome', 'Limite Disponível CLS', 'Data hora Processamento']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Colunas obrigatórias ausentes: {', '.join(missing_columns)}")
        
        # Formatação do CPF/CNPJ
        df['CPF/CNPJ'] = df['CPF/CNPJ'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Atualiza a data mais recente na tabela database
        data_processamento_max = pd.to_datetime(df['Data hora Processamento'], dayfirst=True).max()
        if pd.notna(data_processamento_max):
            cursor.execute("""
                UPDATE `database` 
                SET data_limitedisp = %s 
                WHERE id = 1
            """, (data_processamento_max.strftime('%Y-%m-%d %H:%M:%S'),))
            conn.commit()

        # Limpa todos os limites existentes para CPFs com status 3 ou 8
        app.logger.info("Limpando limites existentes...")
        cursor.execute("UPDATE associados SET limite = 0 WHERE status IN (3, 8)")
        conn.commit()
        
        # Contadores para o relatório
        total_registros = len(df)
        registros_atualizados = 0
        registros_ignorados = 0
        registros_sem_status = 0
        
        # Processa cada linha
        for _, row in df.iterrows():
            try:
                cpf_cnpj = row['CPF/CNPJ']
                limite = float(str(row['Limite Disponível CLS']).replace('.', '').replace(',', '.')) if pd.notna(row['Limite Disponível CLS']) else 0
                
                # Verifica se existe o CPF na tabela associados com status 3 ou 8
                cursor.execute("""
                    UPDATE associados 
                    SET limite = %s 
                    WHERE cpf = %s AND status IN (3, 8)
                """, (limite, cpf_cnpj))
                
                if cursor.rowcount > 0:
                    registros_atualizados += 1
                else:
                    # Verifica se o CPF existe mas não tem status 3 ou 8
                    cursor.execute("SELECT COUNT(*) FROM associados WHERE cpf = %s", (cpf_cnpj,))
                    if cursor.fetchone()[0] > 0:
                        registros_sem_status += 1
                    else:
                        registros_ignorados += 1
                
            except Exception as e:
                app.logger.error(f"Erro ao processar linha {_+2}: {str(e)}")
                registros_ignorados += 1
                continue
        
        # Commit das alterações
        conn.commit()
        cursor.close()
        conn.close()
        
        # Remove o arquivo temporário
        os.remove(filepath)
        session.pop('limites_file', None)
        
        # Prepara mensagem de retorno
        message = (f"Processamento concluído com sucesso.\n"
                  f"Total de registros na planilha: {total_registros}\n"
                  f"Registros atualizados (status 3 ou 8): {registros_atualizados}\n"
                  f"Registros sem status 3 ou 8: {registros_sem_status}\n"
                  f"Registros ignorados (CPF não encontrado): {registros_ignorados}")
        
        app.logger.info(message)
        return jsonify({'message': message})
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de limites: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/executar/associados_score', methods=['POST'])
@login_required
@module_required('uploads')
def executar_associados_score():
    try:
        data = request.get_json(silent=True) or {}
        score_minimo = int(data.get('score_minimo', 500))
        # Primeiro processa a planilha de score
        score_file = session.get('score_file')
        if score_file:
            app.logger.info("Iniciando processamento da planilha de score...")
            
            # Lê todas as abas da planilha
            excel_file = pd.ExcelFile(score_file)
            all_sheets = excel_file.sheet_names
            app.logger.info(f"Encontradas {len(all_sheets)} abas na planilha: {', '.join(all_sheets)}")
            
            # DataFrame para armazenar todos os dados
            df_final = pd.DataFrame()
            
            # Processa cada aba
            for sheet in all_sheets:
                app.logger.info(f"\nProcessando aba: {sheet}")
                # Lê a planilha começando da linha 7 (header)
                df_sheet = pd.read_excel(score_file, sheet_name=sheet, header=6)
                app.logger.info(f"- Linhas encontradas: {len(df_sheet)}")
                app.logger.info(f"- Colunas encontradas: {', '.join(df_sheet.columns)}")
                df_final = pd.concat([df_final, df_sheet], ignore_index=True)
            
            app.logger.info(f"\nTotal de linhas após concatenação: {len(df_final)}")
            
            # Remove linhas com CPF nulo ou vazio
            total_antes = len(df_final)
            df_final = df_final.dropna(subset=['CPF'])
            app.logger.info(f"Linhas removidas por CPF nulo: {total_antes - len(df_final)}")
            
            # Formatação do CPF
            df_final['CPF'] = df_final['CPF'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
            
            # Remove CPFs inválidos
            total_antes = len(df_final)
            df_final = df_final[df_final['CPF'].str.len() == 11]
            app.logger.info(f"Linhas removidas por CPF inválido: {total_antes - len(df_final)}")
            
            # Converte Score para inteiro
            df_final['Score'] = pd.to_numeric(df_final['Score'], errors='coerce').fillna(0).astype(int)
            app.logger.info("\nDistribuição de Scores na planilha:")
            app.logger.info(df_final['Score'].value_counts().sort_index().to_string())
            
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # Atualiza os scores na tabela associados_completa
            atualizados = 0
            total_processados = 0
            
            # Processa em lotes de 500 registros
            for i in range(0, len(df_final), 500):
                batch = df_final.iloc[i:i+500]
                total_processados += len(batch)
                
                for _, row in batch.iterrows():
                    cursor.execute("""
                        UPDATE associados_completa 
                        SET score = %s 
                        WHERE cpf_cnpj = %s
                    """, (row['Score'], row['CPF']))
                    if cursor.rowcount > 0:
                        atualizados += 1
            
            conn.commit()
            app.logger.info(f"Processados {total_processados} de {len(df_final)} registros. Atualizados: {atualizados}")
            
            cursor.close()
            conn.close()
            app.logger.info(f"Atualização de scores concluída. Total atualizado: {atualizados}")
        
        # Depois atualiza a tabela associados com os registros que têm score >= 500
        registros_atualizados = update_associados_from_completa(score_minimo=score_minimo)
        
        # Remove os arquivos temporários e limpa a sessão
        if 'associados_file' in session:
            try:
                os.remove(session['associados_file'])
            except:
                pass
            session.pop('associados_file')
            
        if 'score_file' in session:
            try:
                os.remove(score_file)
            except:
                pass
            session.pop('score_file')
        
        # Obtém estatísticas para retornar
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa")
        total_cpfs = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT cpf_cnpj) FROM associados_completa WHERE score >= 500")
        total_cpfs_score = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT cpf) FROM associados")
        total_associados = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'message': 'Processamento concluído com sucesso',
            'estatisticas': {
                'total_cpfs': total_cpfs,
                'total_cpfs_score_500': total_cpfs_score,
                'total_associados': total_associados,
                'registros_atualizados': registros_atualizados
            }
        })
        
    except Exception as e:
        app.logger.error(f"Erro no processamento integrado: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/download/relatorio/associados/<tipo>', methods=['GET'])
@login_required
@module_required('uploads')
def download_relatorio_associados(tipo):
    conn = None
    cursor = None
    try:
        # Obtém os dados do banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Query para buscar os dados mais recentes
        query = """
            SELECT 
                ac.cpf_cnpj as cpf,
                ac.numero_pa as pa,
                ac.valor_renda_bruta_mensal as renda,
                ac.score,
                ac.data_movimento as data_atualizacao,
                a.data_aprovacao,
                CASE 
                    WHEN s.nome IS NULL THEN 'Pendente'
                    ELSE s.nome
                END as status
            FROM associados_completa ac
            LEFT JOIN associados a ON ac.cpf_cnpj = a.cpf
            LEFT JOIN status s ON a.status = s.id
            WHERE ac.score >= 500
            ORDER BY ac.data_movimento DESC
        """
        cursor.execute(query)
        dados = cursor.fetchall()
        
        # Cria um DataFrame com os dados
        df = pd.DataFrame(dados)
        
        # Formata as colunas
        if not df.empty:
            # Formata CPF (removendo pontuação)
            df['cpf'] = df['cpf'].apply(lambda x: x if x else x)
            
            # Formata telefone (remove espaços e caracteres especiais)
            if 'telefone' in df.columns:
                df['telefone'] = df['telefone'].apply(lambda x: str(x).strip() if pd.notnull(x) else '')
            
            # Formata datas
            date_columns = ['data_atualizacao', 'data_aprovacao']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col]).dt.strftime('%d/%m/%Y %H:%M:%S')
            
            # Formata valores monetários
            money_columns = ['renda', 'limite', 'limite_uso', 'cheque_especial', 'cheque_especial_uso', 'limite_cartao', 'Crédito Pessoal Disponível', 'Crédito Pessoal em Uso']
            for col in money_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"R$ {float(x):,.2f}".replace(',', '_').replace('.', ',').replace('_', '.') if pd.notnull(x) else '')
            
            # Renomeia as colunas para melhor apresentação
            df = df.rename(columns={
                'cpf': 'CPF',
                'pa': 'PA',
                'renda': 'Renda',
                'score': 'Score',
                'data_atualizacao': 'Data Atualização',
                'data_aprovacao': 'Data Aprovação',
                'status': 'Status'
            })

        # Gera o relatório no formato solicitado
        if tipo == 'excel':
            # Cria um buffer para o arquivo Excel
            output = BytesIO()
            
            # Cria um writer do Excel
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Relatório', index=False)
                
                # Obtém o workbook e a worksheet
                workbook = writer.book
                worksheet = writer.sheets['Relatório']
                
                # Formata o cabeçalho
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D9D9D9',
                    'border': 1
                })
                
                # Aplica o formato do cabeçalho
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
                
                # Ajusta a largura das colunas
                for i, col in enumerate(df.columns):
                    max_length = max(
                        df[col].astype(str).apply(len).max(),
                        len(str(col))
                    ) + 2
                    worksheet.set_column(i, i, max_length)
            
            # Prepara o arquivo para download
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'relatorio_associados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )
            
        elif tipo == 'pdf':
            # Cria um buffer para o arquivo PDF
            output = BytesIO()
            
            # Configura o documento PDF
            doc = SimpleDocTemplate(
                output,
                pagesize=landscape(A4),
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )
            
            # Prepara os elementos do PDF
            elements = []
            
            # Adiciona o título
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # Centralizado
            )
            elements.append(Paragraph('Relatório de Associados', title_style))
            
            # Adiciona a data do relatório
            date_style = ParagraphStyle(
                'DateStyle',
                parent=styles['Normal'],
                fontSize=10,
                textColor=colors.grey,
                spaceAfter=20,
                alignment=1  # Centralizado
            )
            elements.append(Paragraph(
                f'Gerado em {datetime.now().strftime("%d/%m/%Y às %H:%M:%S")}',
                date_style
            ))
            
            # Prepara os dados da tabela
            table_data = [df.columns.tolist()] + df.values.tolist()
            
            # Cria a tabela
            table_style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWHEIGHT', (0, 0), (-1, -1), 20),
            ])
            
            # Cria a tabela com os dados
            t = Table(table_data)
            t.setStyle(table_style)
            
            # Adiciona a tabela ao documento
            elements.append(t)
            
            # Gera o PDF
            doc.build(elements)
            
            # Prepara o arquivo para download
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/pdf',
                as_attachment=True,
                download_name=f'relatorio_associados_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
            )
        
        else:
            raise ValueError('Tipo de relatório inválido')
            
    except Exception as e:
        app.logger.error(f"Erro ao gerar relatório: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/atualizar_cpfs_teste', methods=['POST'])
@login_required
def atualizar_cpfs_teste():
    try:
        # Lista de CPFs para atualizar (você pode enviar via POST)
        cpfs_data = request.json
        if not cpfs_data or 'cpfs' not in cpfs_data:
            return jsonify({'error': 'Lista de CPFs não fornecida'}), 400
            
        cpfs = cpfs_data['cpfs']
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Contadores para o relatório
        inseridos = 0
        atualizados = 0
        
        # Processa cada CPF
        for cpf in cpfs:
            # Formata o CPF (remove caracteres especiais e preenche com zeros)
            cpf_formatado = ''.join(filter(str.isdigit, str(cpf))).zfill(11)
            
            # Verifica se o CPF já existe
            cursor.execute("SELECT id FROM associados WHERE cpf = %s", (cpf_formatado,))
            result = cursor.fetchone()
            
            if result:
                # Atualiza o status se o CPF já existe
                cursor.execute("""
                    UPDATE associados 
                    SET status = 3 
                    WHERE cpf = %s
                """, (cpf_formatado,))
                atualizados += 1
            else:
                # Insere novo registro se o CPF não existe
                cursor.execute("""
                    INSERT INTO associados (cpf, status, data_atualizacao) 
                    VALUES (%s, 3, NOW())
                """, (cpf_formatado,))
                inseridos += 1
        
        # Commit das alterações
        conn.commit()
        cursor.close()
        conn.close()
        
        message = (f"Processamento concluído com sucesso.\n"
                  f"CPFs inseridos: {inseridos}\n"
                  f"CPFs atualizados: {atualizados}\n"
                  f"Total processado: {inseridos + atualizados}")
        
        return jsonify({
            'message': message,
            'inseridos': inseridos,
            'atualizados': atualizados,
            'total': inseridos + atualizados
        })
        
    except Exception as e:
        app.logger.error(f"Erro ao atualizar CPFs: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/robozometro')
@login_required
@module_required('robozometro')
def robozometro():
    # Verifica se está no modo TV
    is_full_mode = request.args.get('full') == '1'
    return render_template('robozometro.html', is_full_mode=is_full_mode)

@app.route('/relatorios')
@login_required
@module_required('relatorios')
def relatorios():
    """Página principal do módulo de relatórios"""
    return render_template('relatorios.html')

@app.route('/download/relatorio/<tipo>/<formato>')
@login_required
@module_required('relatorios')
def download_relatorio(tipo, formato):
    """Gera e faz download do relatório no formato especificado"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Define a query base de acordo com o tipo de relatório
        if tipo == 'aprovados':
            query = """
                SELECT 
                    nome,
                    cpf,
                    telefone,
                    pa,
                    renda,
                    data_aprovacao,
                    limite as 'Crédito Pessoal Disponível',
                    limite_uso as 'Crédito Pessoal em Uso',
                    chespecial as cheque_especial,
                    chespecial_uso as cheque_especial_uso,
                    limite_cartao,
                    debito_automatico as 'Débito Automático',
                    UPPER(situacao_cartao) as 'Situação Conta Cartão'
                FROM associados
                WHERE status IN (3, 8)
                ORDER BY pa, data_aprovacao DESC
            """
        elif tipo == 'pendentes':
            query = """
                SELECT
                    nome,
                    cpf,
                    telefone,
                    pa
                FROM associados
                WHERE status = 1
                ORDER BY pa, nome
            """
        elif tipo == 'atualizados_pa':
            query = """
                SELECT
                    pa as 'PA',
                    COUNT(*) as 'Total de Cadastros Atualizados',
                    SUM(CASE WHEN renda IS NOT NULL AND renda > 0 THEN 1 ELSE 0 END) as 'Com Renda',
                    SUM(CASE WHEN limite IS NOT NULL AND limite > 0 THEN 1 ELSE 0 END) as 'Com Crédito Pessoal',
                    SUM(CASE WHEN chespecial IS NOT NULL AND chespecial > 0 THEN 1 ELSE 0 END) as 'Com Cheque Especial',
                    SUM(CASE WHEN limite_cartao IS NOT NULL AND limite_cartao > 0 THEN 1 ELSE 0 END) as 'Com Limite Cartão',
                    SUM(CASE WHEN debito_automatico = 'SIM' THEN 1 ELSE 0 END) as 'Com Débito Automático',
                    COALESCE(SUM(renda), 0) as 'Total Renda',
                    COALESCE(SUM(limite), 0) as 'Total Crédito Pessoal Disponível',
                    COALESCE(SUM(limite_uso), 0) as 'Total Crédito Pessoal em Uso',
                    COALESCE(SUM(chespecial), 0) as 'Total Cheque Especial Disponível',
                    COALESCE(SUM(chespecial_uso), 0) as 'Total Cheque Especial em Uso',
                    COALESCE(SUM(limite_cartao), 0) as 'Total Limite Cartão',
                    MIN(data_aprovacao) as 'Primeira Aprovação',
                    MAX(data_aprovacao) as 'Última Aprovação'
                FROM associados
                WHERE status IN (3, 8)
                GROUP BY pa
                ORDER BY CAST(pa AS UNSIGNED)
            """
        else:
            raise ValueError('Tipo de relatório inválido')
        
        cursor.execute(query)
        dados = cursor.fetchall()
        
        # Cria DataFrame com os dados
        df = pd.DataFrame(dados)
        
        if not df.empty:
            # Tratamento específico para relatório de atualizados por PA
            if tipo == 'atualizados_pa':
                # Adiciona linha de totais antes da formatação
                total_row = {
                    'PA': 'TOTAL GERAL',
                    'Total de Cadastros Atualizados': df['Total de Cadastros Atualizados'].sum(),
                    'Com Renda': df['Com Renda'].sum(),
                    'Com Crédito Pessoal': df['Com Crédito Pessoal'].sum(),
                    'Com Cheque Especial': df['Com Cheque Especial'].sum(),
                    'Com Limite Cartão': df['Com Limite Cartão'].sum(),
                    'Com Débito Automático': df['Com Débito Automático'].sum(),
                    'Total Renda': df['Total Renda'].sum(),
                    'Total Crédito Pessoal Disponível': df['Total Crédito Pessoal Disponível'].sum(),
                    'Total Crédito Pessoal em Uso': df['Total Crédito Pessoal em Uso'].sum(),
                    'Total Cheque Especial Disponível': df['Total Cheque Especial Disponível'].sum(),
                    'Total Cheque Especial em Uso': df['Total Cheque Especial em Uso'].sum(),
                    'Total Limite Cartão': df['Total Limite Cartão'].sum(),
                    'Primeira Aprovação': '-',
                    'Última Aprovação': '-'
                }

                # Adiciona a linha de totais ao DataFrame
                df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)

                # Formata datas para o relatório de PA (exceto linha de totais)
                if 'Primeira Aprovação' in df.columns:
                    df.loc[df['PA'] != 'TOTAL GERAL', 'Primeira Aprovação'] = pd.to_datetime(df.loc[df['PA'] != 'TOTAL GERAL', 'Primeira Aprovação']).dt.strftime('%d/%m/%Y %H:%M:%S')
                if 'Última Aprovação' in df.columns:
                    df.loc[df['PA'] != 'TOTAL GERAL', 'Última Aprovação'] = pd.to_datetime(df.loc[df['PA'] != 'TOTAL GERAL', 'Última Aprovação']).dt.strftime('%d/%m/%Y %H:%M:%S')

                # Formata valores monetários para o relatório de PA
                money_columns_pa = ['Total Renda', 'Total Crédito Pessoal Disponível', 'Total Crédito Pessoal em Uso',
                                   'Total Cheque Especial Disponível', 'Total Cheque Especial em Uso', 'Total Limite Cartão']
                for col in money_columns_pa:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: f"R$ {float(x):,.2f}".replace(',', '_').replace('.', ',').replace('_', '.') if pd.notnull(x) else 'R$ 0,00')
            else:
                # Tratamento para outros relatórios
                # Formata CPF (removendo pontuação)
                if 'cpf' in df.columns:
                    df['cpf'] = df['cpf'].apply(lambda x: x if x else x)

                # Formata telefone (remove espaços e caracteres especiais)
                if 'telefone' in df.columns:
                    df['telefone'] = df['telefone'].apply(lambda x: str(x).strip() if pd.notnull(x) else '')

                # Formata datas
                if 'data_aprovacao' in df.columns:
                    df['data_aprovacao'] = pd.to_datetime(df['data_aprovacao']).dt.strftime('%d/%m/%Y %H:%M:%S')

                # Formata valores monetários
                money_columns = ['renda', 'limite', 'limite_uso', 'cheque_especial', 'cheque_especial_uso', 'limite_cartao', 'Crédito Pessoal Disponível', 'Crédito Pessoal em Uso']
                for col in money_columns:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: f"R$ {float(x):,.2f}".replace(',', '_').replace('.', ',').replace('_', '.') if pd.notnull(x) else '')
            
            # Renomeia as colunas para melhor apresentação (apenas para relatórios que não são de PA)
            if tipo != 'atualizados_pa':
                column_names = {
                    'nome': 'Nome',
                    'cpf': 'CPF',
                    'telefone': 'Telefone',
                    'pa': 'PA',
                    'renda': 'Renda',
                    'data_aprovacao': 'Data Aprovação',
                    'limite': 'Crédito Pessoal Disponível',
                    'limite_uso': 'Crédito Pessoal em Uso',
                    'cheque_especial': 'Cheque Especial Disponível',
                    'cheque_especial_uso': 'Cheque Especial em Uso',
                    'limite_cartao': 'Limite do Cartão',
                    'debito_automatico': 'Débito Automático',
                    'situacao_cartao': 'Situação Conta Cartão'
                }
                df = df.rename(columns=column_names)
        
        # Nome do arquivo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if tipo == 'atualizados_pa':
            filename = f'relatorio_cadastros_atualizados_por_pa_{timestamp}'
        else:
            filename = f'relatorio_{tipo}_{timestamp}'
        
        # Gera o relatório Excel
        output = BytesIO()
        
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Para relatório de PA, precisamos de formatação especial
            if tipo == 'atualizados_pa':
                # Escreve os dados começando da linha 2 (deixa espaço para título)
                df.to_excel(writer, sheet_name='Relatório', index=False, startrow=1)
            else:
                df.to_excel(writer, sheet_name='Relatório', index=False)

            workbook = writer.book
            worksheet = writer.sheets['Relatório']

            # Formatação especial para relatório de PA
            if tipo == 'atualizados_pa':
                # Formato do título
                title_format = workbook.add_format({
                    'bold': True,
                    'font_size': 14,
                    'align': 'center',
                    'valign': 'vcenter',
                    'fg_color': '#003641',
                    'font_color': 'white',
                    'border': 1
                })

                # Adiciona o título mesclado na primeira linha
                worksheet.merge_range(0, 0, 0, len(df.columns)-1, 'Relatórios de Cadastros do RPA - Por PA', title_format)

                # Ajusta altura da linha do título
                worksheet.set_row(0, 25)

                # Formata o cabeçalho (agora na linha 1)
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D9D9D9',
                    'border': 1
                })

                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(1, col_num, value, header_format)
            else:
                # Formata o cabeçalho normal para outros relatórios
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D9D9D9',
                    'border': 1
                })

                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

            # Formatação especial para linha de totais (se for relatório de PA)
            if tipo == 'atualizados_pa' and not df.empty:
                # Encontra a linha de totais
                total_row_index = df[df['PA'] == 'TOTAL GERAL'].index
                if len(total_row_index) > 0:
                    # +2 porque temos título na linha 0 e cabeçalho na linha 1
                    total_row_num = total_row_index[0] + 2

                    # Formato para linha de totais com as mesmas cores do título
                    total_format = workbook.add_format({
                        'bold': True,
                        'fg_color': '#003641',  # Mesma cor de fundo do título
                        'font_color': 'white',  # Mesma cor da fonte do título
                        'border': 1,
                        'top': 2  # Borda superior mais grossa
                    })

                    # Aplica formatação na linha de totais
                    for col_num in range(len(df.columns)):
                        cell_value = df.iloc[total_row_index[0], col_num]
                        worksheet.write(total_row_num, col_num, cell_value, total_format)

            # Ajusta a largura das colunas
            for i, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(str(col))
                ) + 2
                worksheet.set_column(i, i, max_length)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{filename}.xlsx'
        )
            
    except Exception as e:
        app.logger.error(f"Erro ao gerar relatório: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def format_time_hms(seconds):
    """Formata segundos em horas, minutos e segundos"""
    if not seconds:
        return "00:00:00"
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def get_rpas_execution_time():
    """Retorna o tempo total de execução de todos os RPAs e o tempo individual de cada um"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Busca todos os RPAs e seus tempos
        cursor.execute("""
            SELECT rpa_name, display_name, total_active_time, current_status, last_status_update
            FROM rpa_tracking
            ORDER BY display_name
        """)
        
        rpas_data = cursor.fetchall()
        total_time = 0
        rpas_times = []
        
        current_time = datetime.now()
        
        for rpa in rpas_data:
            rpa_time = rpa['total_active_time'] or 0
            
            # Se o RPA está rodando, adiciona o tempo desde a última atualização
            if rpa['current_status'] == 'running' and rpa['last_status_update']:
                elapsed = (current_time - rpa['last_status_update']).total_seconds()
                rpa_time += elapsed
            
            total_time += rpa_time
            rpas_times.append({
                'name': rpa['display_name'] or rpa['rpa_name'],
                'time': format_time_hms(rpa_time)
            })
        
        cursor.close()
        conn.close()
        
        return {
            'total_time': format_time_hms(total_time),
            'rpas': rpas_times
        }
        
    except Exception as e:
        logging.error(f"Erro ao obter tempo de execução dos RPAs: {e}")
        return {
            'total_time': "00:00:00",
            'rpas': []
        }

@app.route('/api/robozometro/dados')
@login_required
@module_required('robozometro')
def get_robozometro_dados():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)

        # Obter dados de execução dos RPAs
        rpas_execution = get_rpas_execution_time()

        # Obter horas trabalhadas usando a nova função
        horas_trabalhadas, minutos_trabalhados = get_total_execution_time()

        # Calcular economia (horas trabalhadas * custo por hora)
        economia = horas_trabalhadas * CUSTO_HORA_FUNCIONARIO + (minutos_trabalhados / 60) * CUSTO_HORA_FUNCIONARIO

        # Obter total de CPFs aprovados (status = 3 ou 8)
        cursor.execute("""
            SELECT COUNT(*) as total
            FROM associados
            WHERE status IN (3, 8)
        """)
        cpfs_result = cursor.fetchone()
        cadastros_atualizados = cpfs_result['total']

        # Obter total de CPFs aprovados com limites
        cursor.execute("""
            SELECT COUNT(DISTINCT cpf) as total
            FROM associados
            WHERE status IN (3, 8)
            AND (
                (limite IS NOT NULL AND limite > 0)
                OR (chespecial IS NOT NULL AND chespecial > 0)
                OR (limite_uso IS NOT NULL AND limite_uso > 0)
                OR (chespecial_uso IS NOT NULL AND chespecial_uso > 0)
            )
        """)
        cpfs_com_limites_result = cursor.fetchone()
        cadastros_com_limites = cpfs_com_limites_result['total']

        # Obter total de CPFs com limites disponíveis
        cursor.execute("""
            SELECT COUNT(DISTINCT cpf) as total
            FROM associados
            WHERE status IN (3, 8)
            AND (limite IS NOT NULL OR chespecial IS NOT NULL)
            AND (limite > 0 OR chespecial > 0)
            AND (limite_uso IS NULL OR limite_uso = 0)
            AND (chespecial_uso IS NULL OR chespecial_uso = 0)
        """)
        cpfs_limites_disponiveis_result = cursor.fetchone()
        cadastros_limites_disponiveis = cpfs_limites_disponiveis_result['total']

        # Obter total de CPFs com limites em uso
        cursor.execute("""
            SELECT COUNT(DISTINCT cpf) as total
            FROM associados
            WHERE status IN (3, 8)
            AND (limite_uso IS NOT NULL OR chespecial_uso IS NOT NULL)
            AND (limite_uso > 0 OR chespecial_uso > 0)
        """)
        cpfs_limites_uso_result = cursor.fetchone()
        cadastros_limites_uso = cpfs_limites_uso_result['total']

        # Ajustar o total para ser a soma exata
        cadastros_com_limites = cadastros_limites_disponiveis + cadastros_limites_uso

        # Obter valores de limites disponíveis
        cursor.execute("""
            SELECT 
                COALESCE(SUM(limite), 0) as total_limite,
                COALESCE(SUM(chespecial), 0) as total_chespecial,
                COALESCE(SUM(limite_uso), 0) as total_limite_uso,
                COALESCE(SUM(chespecial_uso), 0) as total_chespecial_uso,
                COALESCE(SUM(receita), 0) as total_receita
            FROM associados
            WHERE status IN (3, 8)
        """)
        limites_result = cursor.fetchone()
        
        # Obter datas base da tabela database
        cursor.execute("""
            SELECT 
                DATE_FORMAT(data_associados, '%Y-%m-%d') as data_associados,
                DATE_FORMAT(data_limitedisp, '%d-%m-%Y') as data_limitedisp,
                DATE_FORMAT(data_limiteuso, '%Y-%m-%d') as data_limiteuso
            FROM `database`
            WHERE id = 1
        """)
        datas_base = cursor.fetchone()
        
        # Calcular totais
        total_limite_disponivel = float(limites_result['total_limite'])
        total_cheque_disponivel = float(limites_result['total_chespecial'])
        total_disponivel = total_limite_disponivel + total_cheque_disponivel
        
        total_limite_uso = float(limites_result['total_limite_uso'])
        total_cheque_uso = float(limites_result['total_chespecial_uso'])
        total_receita = float(limites_result['total_receita'])
        total_uso = total_limite_uso + total_cheque_uso
        
        total_geral = total_disponivel + total_uso

        cursor.close()
        conn.close()

        return jsonify({
            'rpas_execution': rpas_execution,
            'horas_trabalhadas': horas_trabalhadas,
            'minutos_trabalhados': minutos_trabalhados,
            'economia': economia,
            'cadastros_atualizados': cadastros_atualizados,
            'cadastros_com_limites': cadastros_com_limites,
            'cadastros_limites_disponiveis': cadastros_limites_disponiveis,
            'cadastros_limites_uso': cadastros_limites_uso,
            'limites': {
                'disponivel': {
                    'limite': total_limite_disponivel,
                    'cheque': total_cheque_disponivel,
                    'total': total_disponivel
                },
                'uso': {
                    'limite': total_limite_uso,
                    'cheque': total_cheque_uso,
                    'total': total_uso,
                    'receita': total_receita
                },
                'total_geral': total_geral
            },
            'datas_base': {
                'data_associados': datas_base['data_associados'] if datas_base['data_associados'] else None,
                'data_limitedisp': datas_base['data_limitedisp'] if datas_base['data_limitedisp'] else None,
                'data_limiteuso': datas_base['data_limiteuso'] if datas_base['data_limiteuso'] else None
            }
        })
    except Exception as e:
        logging.error(f"Erro ao obter dados do Robozômetro: {str(e)}")
        return jsonify({'error': 'Erro ao obter dados'}), 500

@app.route('/upload/cheque', methods=['POST'])
@login_required
@module_required('uploads')
def upload_cheque():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith('.csv'):
        return jsonify({'error': 'Formato de arquivo inválido. Use .csv'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    session['cheque_file'] = filepath
    return jsonify({'message': 'Upload realizado com sucesso'})

@app.route('/executar/cheque', methods=['POST'])
@login_required
@module_required('uploads')
def executar_cheque():
    filepath = session.get('cheque_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    try:
        app.logger.info("Iniciando processamento da planilha de cheque especial...")
        
        # Tenta diferentes codificações
        encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
        df = None
        
        for encoding in encodings:
            try:
                app.logger.info(f"Tentando ler o arquivo com encoding {encoding}...")
                df = pd.read_csv(filepath, encoding=encoding, sep=';', decimal=',')
                app.logger.info(f"Arquivo lido com sucesso usando encoding {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            raise ValueError("Não foi possível ler o arquivo com nenhuma das codificações tentadas")
        
        # Validação das colunas necessárias
        required_columns = ['CPF/CNPJ', 'PA', 'Nome', 'Disp. Automático', 'Data Hora Enquadramento']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Colunas obrigatórias ausentes: {', '.join(missing_columns)}")
        
        # Formatação do CPF/CNPJ
        df['CPF/CNPJ'] = df['CPF/CNPJ'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Atualiza a data mais recente na tabela database
        data_enquadramento_max = pd.to_datetime(df['Data Hora Enquadramento'], dayfirst=True).max()
        if pd.notna(data_enquadramento_max):
            cursor.execute("""
                UPDATE `database` 
                SET data_chdisp = %s 
                WHERE id = 1
            """, (data_enquadramento_max.strftime('%Y-%m-%d %H:%M:%S'),))
            conn.commit()

        # Limpa todos os cheques especiais existentes para CPFs com status 3 ou 8
        app.logger.info("Limpando cheques especiais existentes...")
        cursor.execute("UPDATE associados SET chespecial = 0 WHERE status IN (3, 8)")
        conn.commit()
        
        # Contadores para o relatório
        total_registros = len(df)
        registros_atualizados = 0
        registros_ignorados = 0
        registros_sem_status = 0
        
        # Processa cada linha
        for _, row in df.iterrows():
            try:
                cpf_cnpj = row['CPF/CNPJ']
                cheque = float(str(row['Disp. Automático']).replace('.', '').replace(',', '.')) if pd.notna(row['Disp. Automático']) else 0
                
                # Verifica se existe o CPF na tabela associados com status 3 ou 8
                cursor.execute("""
                    UPDATE associados 
                    SET chespecial = %s 
                    WHERE cpf = %s AND status IN (3, 8)
                """, (cheque, cpf_cnpj))
                
                if cursor.rowcount > 0:
                    registros_atualizados += 1
                else:
                    # Verifica se o CPF existe mas não tem status 3 ou 8
                    cursor.execute("SELECT COUNT(*) FROM associados WHERE cpf = %s", (cpf_cnpj,))
                    if cursor.fetchone()[0] > 0:
                        registros_sem_status += 1
                    else:
                        registros_ignorados += 1
                
            except Exception as e:
                app.logger.error(f"Erro ao processar linha {_+2}: {str(e)}")
                registros_ignorados += 1
                continue
        
        # Commit das alterações
        conn.commit()
        cursor.close()
        conn.close()
        
        # Remove o arquivo temporário
        os.remove(filepath)
        session.pop('cheque_file', None)
        
        # Prepara mensagem de retorno
        message = (f"Processamento concluído com sucesso.\n"
                  f"Total de registros na planilha: {total_registros}\n"
                  f"Registros atualizados (status 3 ou 8): {registros_atualizados}\n"
                  f"Registros sem status 3 ou 8: {registros_sem_status}\n"
                  f"Registros ignorados (CPF não encontrado): {registros_ignorados}")
        
        app.logger.info(message)
        return jsonify({'message': message})
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de cheque especial: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/upload/limites-uso', methods=['POST'])
@login_required
@module_required('uploads')
def upload_limites_uso():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'error': 'Formato de arquivo inválido. Use Excel (.xlsx ou .xls)'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    session['limites_uso_file'] = filepath
    return jsonify({'message': 'Upload realizado com sucesso'})

@app.route('/executar/limites-uso', methods=['POST'])
@login_required
@module_required('uploads')
def executar_limites_uso():
    filepath = session.get('limites_uso_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    try:
        app.logger.info("Iniciando processamento da planilha de limites em uso...")
        
        # Lê o arquivo Excel
        df = pd.read_excel(filepath, dtype=str)  # Força todas as colunas como string inicialmente
        app.logger.info(f"Arquivo lido com sucesso")
        app.logger.info(f"Colunas encontradas: {df.columns.tolist()}")
        
        # Verifica se as colunas necessárias estão presentes
        required_columns = [
            'Número CPF/CNPJ', 'Número PA', 'Produto Fábrica', 'Valor Contrato', 
            'Data Movimento', 'Quantidade Parcelas Abertas', '% Taxa Operação',
            'Valor Saldo Devedor Diário'
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Colunas obrigatórias ausentes: {', '.join(missing_columns)}")
        
        # Formatação do CPF/CNPJ
        df['Número CPF/CNPJ'] = df['Número CPF/CNPJ'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        # Remove linhas com CPFs inválidos
        df = df[df['Número CPF/CNPJ'].str.len() == 11]
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Atualiza a data mais recente na tabela database
        data_movimento_max = pd.to_datetime(df['Data Movimento'], dayfirst=True).max()
        if pd.notna(data_movimento_max):
            cursor.execute("""
                UPDATE `database` 
                SET data_limiteuso = %s,
                    data_chuso = %s 
                WHERE id = 1
            """, (data_movimento_max.strftime('%Y-%m-%d %H:%M:%S'),
                  data_movimento_max.strftime('%Y-%m-%d %H:%M:%S')))
            conn.commit()
        
        # Zera a coluna receita para todos os CPFs antes de começar
        cursor.execute("UPDATE associados SET receita = 0, limite_uso = 0, chespecial_uso = 0 WHERE status IN (3, 8)")
        conn.commit()
        
        # Dicionário para armazenar os valores por CPF
        cpf_valores = {}
        
        # Primeiro loop: processa e agrupa os dados por CPF
        for _, row in df.iterrows():
            try:
                cpf_cnpj = row['Número CPF/CNPJ']
                
                # Verifica se o CPF existe com status 3 ou 8
                cursor.execute("SELECT COUNT(*) FROM associados WHERE cpf = %s AND status IN (3, 8)", (cpf_cnpj,))
                if cursor.fetchone()[0] == 0:
                    continue
                
                # Trata o valor do contrato
                try:
                    valor_str = str(row['Valor Contrato']).strip()
                    valor_str = ''.join(c for c in valor_str if c.isdigit() or c in '.,')
                    if ',' in valor_str:
                        partes = valor_str.split(',')
                        valor_str = partes[0].replace('.', '') + '.' + partes[1]
                    valor = float(valor_str) if valor_str else 0
                except (ValueError, TypeError, IndexError) as e:
                    app.logger.warning(f"Valor inválido para CPF {cpf_cnpj}: {row['Valor Contrato']} - Erro: {str(e)}")
                    valor = 0

                # Define o produto antes do cálculo da receita
                produto = str(row['Produto Fábrica']).strip()
                
                # Calcula a receita
                try:
                    parcelas = float(str(row['Quantidade Parcelas Abertas']).replace(',', '.'))
                    taxa = float(str(row['% Taxa Operação']).replace(',', '.'))
                    # Corrige o tratamento do saldo devedor para considerar que já está em centavos
                    saldo_devedor = float(str(row['Valor Saldo Devedor Diário']).replace('.', '').replace(',', '.')) / 100
                    
                    app.logger.info(f"""
Valores para cálculo da receita do CPF {cpf_cnpj}:
- Produto: {produto}
- Parcelas: {parcelas}
- Taxa (bruta): {taxa}%
- Taxa (após divisão): {taxa/100}
- Saldo Devedor: R$ {saldo_devedor:,.2f}
""")
                    
                    # Calcula a receita de acordo com o produto
                    if produto == 'Crédito Automático':
                        # Primeiro calcula (taxa/100 * parcelas)
                        resultado1 = (taxa / 100) * parcelas
                        app.logger.info(f"Resultado intermediário Crédito Automático (taxa/100 * parcelas): {resultado1}")
                        # Depois multiplica pelo saldo devedor
                        receita = resultado1 * saldo_devedor
                    elif produto == 'Conta Corrente':
                        # Para Conta Corrente, apenas (taxa/100) * saldo_devedor
                        receita = (taxa / 100) * saldo_devedor
                        app.logger.info(f"Cálculo Conta Corrente (taxa/100 * saldo_devedor)")
                    else:
                        receita = 0
                        app.logger.warning(f"Produto não reconhecido para CPF {cpf_cnpj}: {produto}")
                    
                    app.logger.info(f"Receita calculada para este contrato: R$ {receita:,.2f}")
                    
                except (ValueError, TypeError) as e:
                    app.logger.warning(f"Erro ao calcular receita para CPF {cpf_cnpj}: {str(e)}")
                    receita = 0
                
                # Inicializa o dicionário para este CPF se não existir
                if cpf_cnpj not in cpf_valores:
                    cpf_valores[cpf_cnpj] = {
                        'limite_uso': 0,
                        'chespecial_uso': 0,
                        'receita': 0
                    }
                
                # Acumula os valores para este CPF
                if produto == 'Crédito Automático':
                    cpf_valores[cpf_cnpj]['limite_uso'] += valor
                elif produto == 'Conta Corrente':
                    cpf_valores[cpf_cnpj]['chespecial_uso'] += valor
                
                cpf_valores[cpf_cnpj]['receita'] += receita
                
                # Log da receita acumulada para este CPF
                app.logger.info(f"Receita total acumulada para o CPF {cpf_cnpj}: R$ {cpf_valores[cpf_cnpj]['receita']:,.2f}")
                
            except Exception as e:
                app.logger.error(f"Erro ao processar linha para CPF {cpf_cnpj}: {str(e)}")
                continue
        
        # Contadores para o relatório
        total_registros = len(df)
        registros_atualizados = 0
        registros_ignorados = len(df) - len(cpf_valores)
        total_receita = 0
        
        # Segundo loop: atualiza o banco com os valores agrupados
        for cpf, valores in cpf_valores.items():
            try:
                cursor.execute("""
                    UPDATE associados 
                    SET limite_uso = %s,
                        chespecial_uso = %s,
                        receita = %s
                    WHERE cpf = %s AND status IN (3, 8)
                """, (valores['limite_uso'], valores['chespecial_uso'], valores['receita'], cpf))
                
                if cursor.rowcount > 0:
                    registros_atualizados += 1
                    total_receita += valores['receita']
                
            except Exception as e:
                app.logger.error(f"Erro ao atualizar banco para CPF {cpf}: {str(e)}")
                continue
        
        # Commit das alterações
        conn.commit()
        cursor.close()
        conn.close()
        
        # Remove o arquivo temporário
        os.remove(filepath)
        session.pop('limites_uso_file', None)
        
        # Prepara mensagem de retorno
        message = (f"Processamento concluído com sucesso.\n"
                  f"Total de registros na planilha: {total_registros}\n"
                  f"CPFs atualizados (status 3 ou 8): {registros_atualizados}\n"
                  f"Registros ignorados (CPF não encontrado ou produto inválido): {registros_ignorados}\n"
                  f"Total de receita gerada: R$ {total_receita:,.2f}")
        
        app.logger.info(message)
        return jsonify({'message': message})
        
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de limites em uso: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/verificar_cpfs_sem_nome')
@login_required
@admin_required
@module_required('relatorios')
def verificar_cpfs_sem_nome():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT 
                a.cpf,
                a.pa,
                a.data_atualizacao,
                a.status,
                s.nome as status_nome
            FROM associados a
            LEFT JOIN status s ON a.status = s.id
            WHERE a.nome IS NULL OR a.nome = ''
            ORDER BY a.pa, a.data_atualizacao DESC
        """
        
        cursor.execute(query)
        cpfs_sem_nome = cursor.fetchall()
        
        # Formata os dados para exibição
        for cpf in cpfs_sem_nome:
            cpf['cpf_formatado'] = f"{cpf['cpf'][:3]}.{cpf['cpf'][3:6]}.{cpf['cpf'][6:9]}-{cpf['cpf'][9:]}"
            cpf['data_atualizacao'] = cpf['data_atualizacao'].strftime('%d/%m/%Y %H:%M:%S')
        
        return render_template(
            'cpfs_sem_nome.html',
            cpfs=cpfs_sem_nome,
            total=len(cpfs_sem_nome)
        )
        
    except Exception as e:
        app.logger.error(f"Erro ao verificar CPFs sem nome: {str(e)}")
        flash('Erro ao verificar CPFs sem nome', 'danger')
        return redirect(url_for('relatorios'))
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/cpfs/retornar-pendente', methods=['POST'])
@login_required
@module_required('cpfs')
@admin_required
def retornar_cpf_pendente():
    """Retorna um CPF para o status pendente"""
    try:
        data = request.get_json()
        cpf = data.get('cpf')
        
        if not cpf:
            return jsonify({'success': False, 'message': 'CPF não fornecido'}), 400
            
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se o CPF existe e está com status de erro
        cursor.execute("SELECT status FROM associados WHERE cpf = %s AND status IN (4, 5)", (cpf,))
        result = cursor.fetchone()
        
        if not result:
            return jsonify({'success': False, 'message': 'CPF não encontrado ou não está com status de erro'}), 404
        
        # Atualiza o status para pendente
        cursor.execute("UPDATE associados SET status = 1, data_atualizacao = NOW() WHERE cpf = %s", (cpf,))
        conn.commit()
        
        return jsonify({'success': True, 'message': 'CPF retornado para pendente com sucesso'})
        
    except Exception as e:
        app.logger.error(f"Erro ao retornar CPF para pendente: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/cpfs/retornar-todos-para-pendente', methods=['POST'])
@login_required
@module_required('cpfs')
@admin_required
def retornar_todos_cpfs_pendente():
    """Retorna todos os CPFs com erro para o status pendente"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se existem CPFs com erro
        cursor.execute("SELECT COUNT(*) FROM associados WHERE status IN (4, 5)")
        total = cursor.fetchone()[0]
        
        if total == 0:
            return jsonify({'success': False, 'message': 'Não há CPFs com erro para retornar'}), 404
        
        # Atualiza todos os CPFs com erro para pendente
        cursor.execute("""
            UPDATE associados 
            SET status = 1, 
                data_atualizacao = NOW() 
            WHERE status IN (4, 5)
        """)
        conn.commit()
        
        return jsonify({
            'success': True, 
            'message': f'{total} CPF(s) retornado(s) para pendente com sucesso'
        })
        
    except Exception as e:
        app.logger.error(f"Erro ao retornar todos os CPFs para pendente: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/audit/logs')
@login_required
@admin_required
def audit_logs():
    try:
        app.logger.info("Iniciando carregamento dos logs de auditoria")
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        app.logger.info(f"Página: {page}, Registros por página: {per_page}")
        
        # Obtém os filtros
        entity_type = request.args.get('entity_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        username = request.args.get('username')
        
        app.logger.info(f"Filtros: entity_type={entity_type}, start_date={start_date}, end_date={end_date}, username={username}")
        
        # Obtém conexão com o banco
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Primeiro, vamos verificar se existem registros na tabela
        cursor.execute("SELECT COUNT(*) as total FROM audit_logs")
        total_logs = cursor.fetchone()['total']
        app.logger.info(f"Total de registros na tabela audit_logs: {total_logs}")
        
        if total_logs == 0:
            app.logger.warning("Nenhum registro encontrado na tabela audit_logs")
            return render_template('audit_logs.html', 
                                logs=[], 
                                pagination=None,
                                get_cpf_log_details=get_cpf_log_details,
                                get_rpa_log_details=get_rpa_log_details,
                                get_user_log_details=get_user_log_details)
        
        # Constrói a query base - Modificada para incluir detalhes de todos os tipos de logs
        query = """
            SELECT 
                al.*,
                -- Detalhes de CPF
                cal.cpf,
                cal.status_anterior as cpf_status_anterior,
                cal.status_novo as cpf_status_novo,
                cal.detalhes_alteracao as cpf_detalhes,
                -- Detalhes de RPA
                ral.rpa_id,
                ral.status_anterior as rpa_status_anterior,
                ral.status_novo as rpa_status_novo,
                ral.configuracoes_alteradas as rpa_detalhes,
                -- Detalhes de Usuário
                ual.tipo_acesso,
                ual.sucesso,
                ual.detalhes as user_detalhes
            FROM audit_logs al
            LEFT JOIN cpf_audit_logs cal ON al.id = cal.audit_log_id
            LEFT JOIN rpa_audit_logs ral ON al.id = ral.audit_log_id
            LEFT JOIN user_access_logs ual ON al.id = ual.audit_log_id
            WHERE 1=1
        """
        params = []
        
        # Adiciona filtros se fornecidos
        if entity_type:
            query += " AND al.entity_type = %s"
            params.append(entity_type)
        
        if start_date:
            query += " AND DATE(al.timestamp) >= %s"
            params.append(start_date)
        
        if end_date:
            query += " AND DATE(al.timestamp) <= %s"
            params.append(end_date)
        
        if username:
            query += " AND al.username LIKE %s"
            params.append(f"%{username}%")
        
        # Conta o total de registros com filtros
        count_query = f"SELECT COUNT(*) as total FROM ({query}) as subquery"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        app.logger.info(f"Total de registros após aplicar filtros: {total}")
        
        # Adiciona ordenação e paginação
        query += " ORDER BY al.timestamp DESC LIMIT %s OFFSET %s"
        params.extend([per_page, (page - 1) * per_page])
        
        # Log da query final
        app.logger.info(f"Query final: {query}")
        app.logger.info(f"Parâmetros: {params}")
        
        # Executa a query principal
        cursor.execute(query, params)
        logs = cursor.fetchall()
        
        app.logger.info(f"Número de logs retornados: {len(logs)}")
        if len(logs) > 0:
            app.logger.info(f"Exemplo do primeiro log: {logs[0]}")
        
        # Cria objeto de paginação
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': page * per_page < total,
            'prev_num': page - 1,
            'next_num': page + 1,
            'iter_pages': lambda: range(1, ((total + per_page - 1) // per_page) + 1)
        }
        
        app.logger.info(f"Informações de paginação: {pagination}")
        
        return render_template('audit_logs.html', 
                             logs=logs, 
                             pagination=pagination,
                             get_cpf_log_details=get_cpf_log_details,
                             get_rpa_log_details=get_rpa_log_details,
                             get_user_log_details=get_user_log_details)
                             
    except Exception as e:
        app.logger.error(f"Erro ao carregar logs de auditoria: {str(e)}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        flash('Erro ao carregar logs de auditoria', 'error')
        return redirect(url_for('index'))
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_cpf_log_details(audit_log_id):
    """Obtém detalhes do log de CPF"""
    conn = None
    cursor = None
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT cal.*, al.action, al.timestamp, al.ip_address, al.user_agent
            FROM cpf_audit_logs cal
            JOIN audit_logs al ON cal.audit_log_id = al.id
            WHERE cal.audit_log_id = %s
        """, (audit_log_id,))
        
        result = cursor.fetchone()
        if result and result.get('detalhes_alteracao'):
            try:
                result['detalhes_alteracao'] = json.loads(result['detalhes_alteracao'])
            except json.JSONDecodeError:
                app.logger.error(f"Erro ao decodificar JSON dos detalhes do log {audit_log_id}")
                result['detalhes_alteracao'] = {}
        return result
        
    except Exception as e:
        app.logger.error(f"Erro ao obter detalhes do log de CPF: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_rpa_log_details(audit_log_id):
    """Obtém detalhes do log de RPA"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT ral.*, al.action, al.timestamp, al.ip_address, al.user_agent
            FROM rpa_audit_logs ral
            JOIN audit_logs al ON ral.audit_log_id = al.id
            WHERE ral.audit_log_id = %s
        """, (audit_log_id,))
        
        result = cursor.fetchone()
        if result and result.get('configuracoes_alteradas'):
            try:
                result['configuracoes_alteradas'] = json.loads(result['configuracoes_alteradas'])
            except json.JSONDecodeError:
                app.logger.error(f"Erro ao decodificar JSON dos detalhes do log {audit_log_id}")
                result['configuracoes_alteradas'] = {}
        return result
        
    except Exception as e:
        app.logger.error(f"Erro ao obter detalhes do log de RPA: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_user_log_details(audit_log_id):
    """Obtém detalhes do log de usuário"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT ual.*, al.action, al.timestamp, al.ip_address, al.user_agent
            FROM user_access_logs ual
            JOIN audit_logs al ON ual.audit_log_id = al.id
            WHERE ual.audit_log_id = %s
        """, (audit_log_id,))
        
        result = cursor.fetchone()
        if result and result.get('detalhes'):
            try:
                result['detalhes'] = json.loads(result['detalhes'])
            except json.JSONDecodeError:
                app.logger.error(f"Erro ao decodificar JSON dos detalhes do log {audit_log_id}")
                result['detalhes'] = {}
        return result
        
    except Exception as e:
        app.logger.error(f"Erro ao obter detalhes do log de usuário: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def has_permission(module_name):
    """Verifica se o usuário tem permissão para acessar um módulo"""
    if 'user_id' not in session:
        return False
    
    try:
        user = User.get_by_id(session['user_id'])
        if not user:
            return False
        
        return user.is_admin or module_name in user.permissions
    except Exception as e:
        app.logger.error(f"Erro ao verificar permissão: {str(e)}")
        return False

# Rotas do módulo CRLs
@app.route('/crls')
@login_required
@module_required('crls')
def crls():
    # Obtém os filtros da query string
    filter_data_inicio = request.args.get('data_inicio')
    filter_data_fim = request.args.get('data_fim')
    filter_pas = request.args.getlist('pas')
    filter_status = request.args.getlist('status')
    
    # Obtém as estatísticas
    conn = get_mysql_connection()
    cursor = conn.cursor(dictionary=True)
    
    # Construir a cláusula WHERE base
    where_clause = "WHERE 1=1"
    params = []
    
    # Adiciona filtro de PA se não for admin
    if not session.get('is_admin'):
        where_clause += " AND pa IN (%s)"
        params.extend(session.get('pa'))
    elif filter_pas:
        placeholders = ','.join(['%s'] * len(filter_pas))
        where_clause += f" AND pa IN ({placeholders})"
        params.extend(filter_pas)
    
    # Adiciona filtros de data
    if filter_data_inicio:
        where_clause += " AND DATE(data_atualizacao) >= %s"
        params.append(filter_data_inicio)
    if filter_data_fim:
        where_clause += " AND DATE(data_atualizacao) <= %s"
        params.append(filter_data_fim)
    
    # Adiciona filtros de status
    if filter_status:
        placeholders = ','.join(['%s'] * len(filter_status))
        where_clause += f" AND status_crl IN ({placeholders})"
        params.extend(filter_status)
    
    # Consulta para cada status
    cursor.execute(f"""
        SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 1
    """, params)
    total_pendentes = cursor.fetchone()['total']
    
    cursor.execute(f"""
        SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 2
    """, params)
    total_executados = cursor.fetchone()['total']
    
    cursor.execute(f"""
        SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 3
    """, params)
    total_impeditivos = cursor.fetchone()['total']
    
    cursor.execute(f"""
        SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl IN (4, 5)
    """, params)
    total_erros = cursor.fetchone()['total']
    
    cursor.close()
    conn.close()
    
    # Lista de PAs disponíveis para filtro (admin)
    pas_disponiveis = AVAILABLE_PAS if session.get('is_admin') else session.get('pa')
    
    return render_template('crls_dashboard.html',
                         total_pendentes=total_pendentes,
                         total_executados=total_executados,
                         total_impeditivos=total_impeditivos,
                         total_erros=total_erros,
                         filter_data_inicio=filter_data_inicio,
                         filter_data_fim=filter_data_fim,
                         filter_pas=filter_pas,
                         filter_status=filter_status,
                         pas_disponiveis=pas_disponiveis)

@app.route('/crls/pendentes')
@login_required
@module_required('crls')
def crls_pendentes():
    try:
        page = request.args.get('page', 1, type=int)
        page_removidos = request.args.get('page_removidos', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)

        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)

        # Construir a cláusula WHERE base
        where_clause = "WHERE 1=1"
        params = []

        # Adiciona filtro de PA se não for admin
        if not session.get('is_admin'):
            user_pas = session.get('pa', [])
            if user_pas:
                placeholders = ','.join(['%s'] * len(user_pas))
                where_clause += f" AND pa IN ({placeholders})"
                params.extend(user_pas)

        # Consulta para pendentes
        cursor.execute(f"""
            SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 1
        """, params)
        total_pendentes = cursor.fetchone()['total']

        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT cpf, pa, nome, data_aprovacao FROM associados {where_clause} AND status_crl = 1
            ORDER BY data_atualizacao DESC LIMIT %s OFFSET %s
        """, params + [per_page, offset])
        crls_pendentes = cursor.fetchall()

        # Consulta para removidos
        cursor.execute(f"""
            SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 6
        """, params)
        total_removidos = cursor.fetchone()['total']

        offset_removidos = (page_removidos - 1) * per_page
        cursor.execute(f"""
            SELECT cpf, pa, nome, data_aprovacao FROM associados {where_clause} AND status_crl = 6
            ORDER BY data_atualizacao DESC LIMIT %s OFFSET %s
        """, params + [per_page, offset_removidos])
        crls_removidos = cursor.fetchall()

        cursor.close()
        conn.close()

        # Cálculos de paginação
        total_pages_pendentes = (total_pendentes + per_page - 1) // per_page
        total_pages_removidos = (total_removidos + per_page - 1) // per_page

        start_page = max(1, page - 2)
        end_page = min(start_page + 4, total_pages_pendentes)

        start_page_removidos = max(1, page_removidos - 2)
        end_page_removidos = min(start_page_removidos + 4, total_pages_removidos)
    
        return render_template('crls_pendentes.html',
                             crls_pendentes=crls_pendentes,
                             crls_removidos=crls_removidos,
                             total_pendentes=total_pendentes,
                             total_removidos=total_removidos,
                             page=page,
                             page_removidos=page_removidos,
                             per_page=per_page,
                             total_pages_pendentes=total_pages_pendentes,
                             total_pages_removidos=total_pages_removidos,
                             start_page=start_page,
                             end_page=end_page,
                             start_page_removidos=start_page_removidos,
                             end_page_removidos=end_page_removidos)

    except Exception as e:
        app.logger.error(f"Erro ao carregar CRLs pendentes: {str(e)}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        flash('Erro ao carregar dados das CRLs pendentes.', 'danger')
        return redirect(url_for('crls'))

@app.route('/crls/executados')
@login_required
@module_required('crls')
def crls_executados():
    try:
        page = request.args.get('page', 1, type=int)
        page_impeditivos = request.args.get('page_impeditivos', 1, type=int)
        page_erros = request.args.get('page_erros', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Construir a cláusula WHERE base
        where_clause = "WHERE 1=1"
        params = []
        
        # Adiciona filtro de PA se não for admin
        if not session.get('is_admin'):
            where_clause += " AND pa IN (%s)"
            params.extend(session.get('pa'))
            
        # Adiciona filtros de data se fornecidos
        if data_inicio:
            where_clause += " AND DATE(data_crl) >= %s"
            params.append(data_inicio)
        if data_fim:
            where_clause += " AND DATE(data_crl) <= %s"
            params.append(data_fim)
        
        # Consulta para executados
        cursor.execute(f"""
            SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 2
        """, params)
        total_executados = cursor.fetchone()['total']
        
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT cpf, pa, nome, data_crl FROM associados {where_clause} AND status_crl = 2
            ORDER BY data_crl DESC, data_atualizacao DESC LIMIT %s OFFSET %s
        """, params + [per_page, offset])
        crls_executados = cursor.fetchall()
        
        # Consulta para impeditivos
        cursor.execute(f"""
            SELECT COUNT(*) as total FROM associados {where_clause} AND status_crl = 3
        """, params)
        total_impeditivos = cursor.fetchone()['total']
        
        offset_impeditivos = (page_impeditivos - 1) * per_page
        cursor.execute(f"""
            SELECT cpf, pa, nome, data_crl FROM associados {where_clause} AND status_crl = 3
            ORDER BY data_crl DESC, data_atualizacao DESC LIMIT %s OFFSET %s
        """, params + [per_page, offset_impeditivos])
        crls_impeditivos = cursor.fetchall()
        
        # Consulta para erros
        cursor.execute(f"""
            SELECT COUNT(*) as total FROM associados {where_clause} AND (status_crl = 4 OR status_crl = 5)
        """, params)
        total_erros = cursor.fetchone()['total']
        
        offset_erros = (page_erros - 1) * per_page
        cursor.execute(f"""
            SELECT cpf, pa, nome, data_crl FROM associados {where_clause} AND (status_crl = 4 OR status_crl = 5)
            ORDER BY data_crl DESC, data_atualizacao DESC LIMIT %s OFFSET %s
        """, params + [per_page, offset_erros])
        crls_erros = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        # Cálculos de paginação
        total_pages_executados = (total_executados + per_page - 1) // per_page
        total_pages_impeditivos = (total_impeditivos + per_page - 1) // per_page
        total_pages_erros = (total_erros + per_page - 1) // per_page
        
        # Cálculos de paginação para executados
        start_page = max(1, page - 2)
        end_page = min(start_page + 4, total_pages_executados)
        
        # Cálculos de paginação para impeditivos
        start_page_impeditivos = max(1, page_impeditivos - 2)
        end_page_impeditivos = min(start_page_impeditivos + 4, total_pages_impeditivos)
        
        # Cálculos de paginação para erros
        start_page_erros = max(1, page_erros - 2)
        end_page_erros = min(start_page_erros + 4, total_pages_erros)
        
        return render_template('crls_executados.html',
                            crls_executados=crls_executados,
                            crls_impeditivos=crls_impeditivos,
                            crls_erros=crls_erros,
                            total_executados=total_executados,
                            total_impeditivos=total_impeditivos,
                            total_erros=total_erros,
                            page=page,
                            page_impeditivos=page_impeditivos,
                            page_erros=page_erros,
                            per_page=per_page,
                            total_pages_executados=total_pages_executados,
                            total_pages_impeditivos=total_pages_impeditivos,
                            total_pages_erros=total_pages_erros,
                            start_page=start_page,
                            end_page=end_page,
                            start_page_impeditivos=start_page_impeditivos,
                            end_page_impeditivos=end_page_impeditivos,
                            start_page_erros=start_page_erros,
                            end_page_erros=end_page_erros,
                            data_inicio=data_inicio,
                            data_fim=data_fim)
    except Exception as e:
        app.logger.error(f"Erro ao carregar CRLs executados: {str(e)}")
        return redirect(url_for('login'))

# Rotas da API para ações do módulo CRLs
@app.route('/api/crls/<cpf>/remover', methods=['POST'])
@login_required
@module_required('crls')
def api_remover_crl(cpf):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se o usuário tem acesso ao PA do CPF
        if not session.get('is_admin'):
            cursor.execute("SELECT pa FROM associados WHERE cpf = %s", [cpf])
            result = cursor.fetchone()
            if not result or str(result[0]) not in session.get('pa'):
                return jsonify({'success': False, 'message': 'Sem permissão para este PA'}), 403
        
        cursor.execute("""
            UPDATE associados SET status_crl = 6, data_atualizacao = NOW()
            WHERE cpf = %s AND status_crl = 1
        """, [cpf])
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"Erro ao remover CRL: {str(e)}")
        return jsonify({'success': False, 'message': 'Erro ao remover CRL'}), 500

@app.route('/api/crls/<cpf>/restaurar', methods=['POST'])
@login_required
@module_required('crls')
def api_restaurar_crl(cpf):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se o usuário tem acesso ao PA do CPF
        if not session.get('is_admin'):
            cursor.execute("SELECT pa FROM associados WHERE cpf = %s", [cpf])
            result = cursor.fetchone()
            if not result or str(result[0]) not in session.get('pa'):
                return jsonify({'success': False, 'message': 'Sem permissão para este PA'}), 403
        
        cursor.execute("""
            UPDATE associados SET status_crl = 1, data_atualizacao = NOW()
            WHERE cpf = %s AND status_crl = 6
        """, [cpf])
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"Erro ao restaurar CRL: {str(e)}")
        return jsonify({'success': False, 'message': 'Erro ao restaurar CRL'}), 500

@app.route('/api/crls/<cpf>/retornar', methods=['POST'])
@login_required
@module_required('crls')
def api_retornar_crl(cpf):
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Verifica se o usuário tem acesso ao PA do CPF
        if not session.get('is_admin'):
            cursor.execute("SELECT pa FROM associados WHERE cpf = %s", [cpf])
            result = cursor.fetchone()
            if not result or str(result[0]) not in session.get('pa'):
                return jsonify({'success': False, 'message': 'Sem permissão para este PA'}), 403
        
        cursor.execute("""
            UPDATE associados SET status_crl = 1, data_atualizacao = NOW()
            WHERE cpf = %s AND status_crl IN (3, 4, 5)
        """, [cpf])
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"Erro ao retornar CRL: {str(e)}")
        return jsonify({'success': False, 'message': 'Erro ao retornar CRL'}), 500

@app.route('/api/crls/retornar-impeditivos', methods=['POST'])
@login_required
@module_required('crls')
def api_retornar_todos_impeditivos():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Construir a cláusula WHERE base
        where_clause = "WHERE status_crl = 3"
        params = []
        
        # Adiciona filtro de PA se não for admin
        if not session.get('is_admin'):
            where_clause += " AND pa IN (%s)"
            params.extend(session.get('pa'))
        
        cursor.execute(f"""
            UPDATE associados SET status_crl = 1, data_atualizacao = NOW()
            {where_clause}
        """, params)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"Erro ao retornar todos os impeditivos: {str(e)}")
        return jsonify({'success': False, 'message': 'Erro ao retornar todos os impeditivos'}), 500

@app.route('/api/crls/retornar-erros', methods=['POST'])
@login_required
@module_required('crls')
def api_retornar_todos_erros():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Construir a cláusula WHERE base
        where_clause = "WHERE status_crl IN (4, 5)"
        params = []
        
        # Adiciona filtro de PA se não for admin
        if not session.get('is_admin'):
            where_clause += " AND pa IN (%s)"
            params.extend(session.get('pa'))
        
        cursor.execute(f"""
            UPDATE associados SET status_crl = 1, data_atualizacao = NOW()
            {where_clause}
        """, params)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({'success': True})
    except Exception as e:
        app.logger.error(f"Erro ao retornar todos os erros: {str(e)}")
        return jsonify({'success': False, 'message': 'Erro ao retornar todos os erros'}), 500

@app.route('/upload/cartoes', methods=['POST'])
@login_required
@module_required('uploads')
def upload_cartoes():
    if 'file' not in request.files:
        return jsonify({'error': 'Nenhum arquivo enviado'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
    
    if not file.filename.endswith('.xlsx'):
        return jsonify({'error': 'Formato de arquivo inválido. Use .xlsx'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    session['cartoes_file'] = filepath
    return jsonify({'message': 'Upload realizado com sucesso'})

@app.route('/executar/cartoes', methods=['POST'])
@login_required
@module_required('uploads')
def executar_cartoes():
    # Desativa temporariamente o reloader
    app.debug = False
    
    filepath = session.get('cartoes_file')
    if not filepath:
        return jsonify({'error': 'Arquivo não encontrado'}), 400
    
    conn = None
    cursor = None
    
    try:
        app.logger.info("Iniciando processamento da planilha de cartões...")
        
        # Lê o arquivo Excel
        app.logger.info(f"Tentando ler o arquivo: {filepath}")
        df = pd.read_excel(filepath)
        
        # Log das colunas encontradas
        app.logger.info(f"Colunas encontradas na planilha: {df.columns.tolist()}")
        
        # Mapeamento de possíveis variações dos nomes das colunas
        column_variations = {
            'CPF/CNPJ Titular': ['CPF/CNPJ Titular', 'CPF/CPNJ Titular', 'CPF CNPJ Titular', 'CPF CPNJ Titular'],
            'Valor Limite Atribuído': ['Valor Limite Atribuído', 'Valor Limite Atribuido', 'Limite Atribuído', 'Limite'],
            'Descrição Débito Automático Fatura': ['Descrição Débito Automático Fatura', 'Descricao Debito Automatico Fatura', 'Débito Automático Fatura'],
            'Situação Conta Cartão': ['Situação Conta Cartão', 'Situacao Conta Cartao', 'Situação do Cartão', 'Situacao do Cartao']
        }
        
        # Tenta encontrar as colunas mesmo com espaços extras ou diferenças de case
        df.columns = df.columns.str.strip()
        normalized_columns = {col.lower().strip(): col for col in df.columns}
        
        # Mapeia as colunas requeridas para seus nomes reais na planilha
        column_mapping = {}
        missing_columns = []
        
        for required_col, variations in column_variations.items():
            found = False
            for variation in variations:
                normalized_variation = variation.lower().strip()
                if normalized_variation in normalized_columns:
                    column_mapping[required_col] = normalized_columns[normalized_variation]
                    found = True
                    break
            if not found:
                missing_columns.append(required_col)
        
        if missing_columns:
            app.logger.error(f"Colunas obrigatórias ausentes: {missing_columns}")
            app.logger.error(f"Colunas disponíveis (normalizadas): {list(normalized_columns.keys())}")
            raise ValueError(f"Colunas obrigatórias ausentes: {', '.join(missing_columns)}")
        
        # Renomeia as colunas para os nomes esperados
        df = df.rename(columns={column_mapping[col]: col for col in column_variations.keys()})
        
        # Formatação do CPF/CNPJ
        df['CPF/CNPJ Titular'] = df['CPF/CNPJ Titular'].astype(str).apply(lambda x: ''.join(filter(str.isdigit, x)).zfill(11))
        
        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # Inicia a transação
        conn.start_transaction()
        
        try:
            # Verifica se as colunas existem, se não, cria
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_name = 'associados' 
                AND column_name = 'limite_cartao'
            """)
            if cursor.fetchone()[0] == 0:
                cursor.execute("ALTER TABLE associados ADD COLUMN limite_cartao DECIMAL(15,2) DEFAULT 0")

            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_name = 'associados' 
                AND column_name = 'debito_automatico'
            """)
            if cursor.fetchone()[0] == 0:
                cursor.execute("ALTER TABLE associados ADD COLUMN debito_automatico VARCHAR(3) DEFAULT 'Não'")

            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_name = 'associados' 
                AND column_name = 'situacao_cartao'
            """)
            if cursor.fetchone()[0] == 0:
                cursor.execute("ALTER TABLE associados ADD COLUMN situacao_cartao VARCHAR(50) DEFAULT NULL")
            
            # Zera os valores antes de começar
            cursor.execute("UPDATE associados SET limite_cartao = 0, debito_automatico = 'Não', situacao_cartao = NULL WHERE status IN (3, 8)")
            
            # Contadores para o relatório
            total_registros = len(df)
            registros_atualizados = 0
            registros_ignorados = 0
            
            # Processa em lotes de 1000 registros
            batch_size = 1000
            for start in range(0, len(df), batch_size):
                end = min(start + batch_size, len(df))
                batch_df = df.iloc[start:end]
                
                # Processa cada linha do lote
                for idx, row in batch_df.iterrows():
                    try:
                        cpf = row['CPF/CNPJ Titular']
                        # Log do valor antes da conversão
                        app.logger.debug(f"Valor original do limite para CPF {cpf}: {row['Valor Limite Atribuído']}")
                        
                        # Tratamento do valor do limite
                        valor_str = str(row['Valor Limite Atribuído'])
                        if isinstance(row['Valor Limite Atribuído'], (int, float)):
                            limite_cartao = float(row['Valor Limite Atribuído'])
                        else:
                            # Remove caracteres não numéricos exceto ponto e vírgula
                            valor_str = re.sub(r'[^\d,.]', '', valor_str)
                            # Substitui vírgula por ponto
                            valor_str = valor_str.replace(',', '.')
                            limite_cartao = float(valor_str)
                        
                        # Tratamento do débito automático
                        debito_automatico = 'Sim' if str(row['Descrição Débito Automático Fatura']).strip().upper() == 'CDA' else 'Não'
                        
                        # Tratamento da situação do cartão
                        situacao_cartao = str(row['Situação Conta Cartão']).strip().upper() if pd.notnull(row['Situação Conta Cartão']) else None
                        
                        app.logger.debug(f"Valor convertido do limite para CPF {cpf}: {limite_cartao}")
                        app.logger.debug(f"Débito automático para CPF {cpf}: {debito_automatico}")
                        app.logger.debug(f"Situação do cartão para CPF {cpf}: {situacao_cartao}")
                        
                        # Atualiza o limite do cartão, débito automático e situação do cartão para CPFs com status 3 ou 8
                        cursor.execute("""
                            UPDATE associados 
                            SET limite_cartao = %s,
                                debito_automatico = %s,
                                situacao_cartao = %s
                            WHERE cpf = %s AND status IN (3, 8)
                        """, (limite_cartao, debito_automatico, situacao_cartao, cpf))
                        
                        if cursor.rowcount > 0:
                            registros_atualizados += 1
                        else:
                            registros_ignorados += 1
                            
                    except Exception as e:
                        app.logger.error(f"Erro ao processar linha {idx+2}: {str(e)}")
                        app.logger.error(f"Dados da linha: {row.to_dict()}")
                        registros_ignorados += 1
                        continue
                
                # Commit parcial a cada lote
                conn.commit()
                app.logger.info(f"Processado lote de {start} até {end} registros")
            
            # Commit final
            conn.commit()
            
            # Remove o arquivo temporário
            os.remove(filepath)
            session.pop('cartoes_file', None)
            
            # Prepara mensagem de retorno
            message = (f"Processamento concluído com sucesso.\n"
                      f"Total de registros na planilha: {total_registros}\n"
                      f"Registros atualizados (status 3 ou 8): {registros_atualizados}\n"
                      f"Registros ignorados: {registros_ignorados}")
            
            app.logger.info(message)
            return jsonify({'message': message})
            
        except Exception as e:
            # Em caso de erro, faz rollback da transação
            if conn:
                conn.rollback()
            raise e
            
    except Exception as e:
        app.logger.error(f"Erro ao processar planilha de cartões: {str(e)}")
        return jsonify({'error': str(e)}), 500
        
    finally:
        # Restaura o modo debug
        app.debug = True
        
        # Fecha as conexões
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def generate_2fa_code():
    """Gera um código 2FA de 6 dígitos"""
    return ''.join(random.choices(string.digits, k=6))

def send_2fa_email(to_email, code):
    """Envia e-mail com código 2FA usando Microsoft Graph API"""
    subject = 'Código de Verificação - RPA Monitor'
    body = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #003641;">Verificação de Acesso - RPA Monitor</h2>
            <p>Olá,</p>
            <p>Seu código de verificação para acessar o RPA Monitor é:</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;">
                <h1 style="color: #00AE9D; font-size: 32px; margin: 0;">{code}</h1>
            </div>
            <p>Este código é válido por 5 minutos.</p>
            <p>Se você não solicitou este código, por favor ignore este e-mail.</p>
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                Este é um e-mail automático, por favor não responda.
            </p>
        </div>
    </body>
    </html>
    """
    try:
        enviar_email_automatico(to_email, subject, body)
        return True
    except Exception as e:
        app.logger.error(f'Erro ao enviar e-mail 2FA: {str(e)}')
        return False

# --- Endpoint para integração com Zabbix (Webhook) ---
@app.route('/api/zabbix/send_email', methods=['POST'])
def zabbix_send_email():
    app.logger.info('Recebida requisição do Zabbix!')
    AUTH_TOKEN = os.getenv('ZABBIX_WEBHOOK_TOKEN', 'SEU_TOKEN_AQUI')
    token = request.headers.get('Authorization')
    if token != f'Bearer {AUTH_TOKEN}':
        app.logger.warning('Tentativa de acesso não autorizado ao endpoint do Zabbix.')
        return jsonify({'error': 'Não autorizado'}), 401

    data = request.get_json()
    app.logger.info(f'Dados recebidos: {data}')
    to = data.get('to')
    subject = data.get('subject')
    body = data.get('body')
    if not all([to, subject, body]):
        app.logger.error('Parâmetros obrigatórios ausentes na requisição do Zabbix.')
        return jsonify({'error': 'Parâmetros obrigatórios: to, subject, body'}), 400
    try:
        enviar_email_automatico(to, subject, body)
        app.logger.info(f'E-mail enviado com sucesso para {to} (assunto: {subject}) via Zabbix.')
        return jsonify({'status': 'ok'}), 200
    except Exception as e:
        app.logger.error(f'Erro ao enviar e-mail via Zabbix: {str(e)}')
        return jsonify({'error': str(e)}), 500
# ... código existente ...

if __name__ == '__main__':
    # Detecta ambiente baseado em variáveis ou arquivos de certificado
    ambiente = os.environ.get('FLASK_ENV', 'production')  # Padrão para produção
    ssl_cert = r'C:/xampp/apache/conf/ssl.crt/sicoob.crt'
    ssl_key = r'C:/xampp/apache/conf/ssl.key/sicoob.key'

    # Verifica se os certificados existem
    certificados_existem = os.path.exists(ssl_cert) and os.path.exists(ssl_key)

    if ambiente == 'production' and certificados_existem:
        print(f"Iniciando em modo PRODUÇÃO com HTTPS")
        print(f"Certificado SSL: {ssl_cert}")
        print(f"Chave SSL: {ssl_key}")
        app.run(host='0.0.0.0', port=5000, debug=False, ssl_context=(ssl_cert, ssl_key))
    elif ambiente == 'production' and not certificados_existem:
        print("AVISO: Modo produção solicitado mas certificados SSL não encontrados!")
        print(f"Certificado esperado: {ssl_cert}")
        print(f"Chave esperada: {ssl_key}")
        print("Iniciando sem HTTPS...")
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print(f"Iniciando em modo DESENVOLVIMENTO (HTTP)")
        app.run(host='0.0.0.0', port=5000, debug=True)
