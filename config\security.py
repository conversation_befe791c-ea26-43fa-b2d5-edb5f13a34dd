"""
Configurações de segurança do sistema
"""

# Configurações de senha
PASSWORD_MIN_LENGTH = 8
PASSWORD_REQUIRE_UPPERCASE = True
PASSWORD_REQUIRE_LOWERCASE = True
PASSWORD_REQUIRE_NUMBERS = True
PASSWORD_REQUIRE_SPECIAL = True
PASSWORD_SPECIAL_CHARS = "!@#$%^&*(),.?\":{}|<>"

# Configurações de sessão
SESSION_LIFETIME = 8 * 60 * 60  # 8 horas em segundos
SESSION_COOKIE_SECURE = True  # Requer HTTPS
SESSION_COOKIE_HTTPONLY = True  # Previne acesso via JavaScript
SESSION_COOKIE_SAMESITE = 'Lax'  # Proteção contra CSRF

# Configurações de login
MAX_LOGIN_ATTEMPTS = 3
LOGIN_TIMEOUT = 300  # 5 minutos em segundos
LOGIN_USERNAME_MIN_LENGTH = 3
LOGIN_USERNAME_MAX_LENGTH = 255
LOGIN_USERNAME_PATTERN = r"^[a-zA-Z0-9_@.-]+$"

# Configurações de hash
HASH_ALGORITHM = 'sha256'
HASH_ITERATIONS = 100000
HASH_SALT_SIZE = 32  # bytes
HASH_KEY_LENGTH = 128  # bytes

# Lista de PAs válidos
VALID_PAS = ['0', '2', '3', '4', '5', '6', '7', '9', '10', '11', '12', 
             '15', '16', '17', '18', '20', '21', '22', '23', '24', '25', '26', '97']

# Módulos disponíveis
AVAILABLE_MODULES = {
    'dashboard': 'Painel Principal',
    'cpfs': 'Módulo de CPFs',
    'rpa_monitor': 'Monitoramento de RPAs',
    'uploads': 'Upload de Planilhas',
    'usuarios': 'Gerenciamento de Usuários',
    'configuracoes': 'Configurações'
}

# Headers de segurança
SECURITY_HEADERS = {
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
}

# Configurações de log
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'
LOG_FILE = 'app.log'

# Configurações do banco de dados
DB_CONNECTION_TIMEOUT = 5
DB_ALLOW_LOCAL_INFILE = True
DB_USE_PURE = True
DB_SSL_DISABLED = True 