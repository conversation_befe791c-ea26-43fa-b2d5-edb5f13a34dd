import mysql.connector
import logging
from datetime import datetime

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Tentando conectar ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True,
            connection_timeout=5,
            allow_local_infile=True,
            use_pure=True
        )
        logger.info("Conexão MySQL estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def update_associados_from_completa(score_minimo=500):
    """
    Atualiza a tabela associados com os registros da tabela associados_completa que têm score >= 500.
    
    Nova lógica de atualização:
    1. Para a tabela associados_completa:
       - Todos os registros do arquivo de upload são inseridos/atualizados normalmente
    
    2. Para transferência de dados da tabela associados_completa para associados:
       - Verifica data_movimento (associados_completa) vs data_aprovacao (associados)
       - Se data_movimento < data_aprovacao: não faz nenhuma alteração
       - Se data_movimento > data_aprovacao: prossegue com as atualizações e força status = 1
       - Se o CPF não existir: insere com status 1
    """
    try:
        print("Tentando conectar ao MySQL...")
        conn = get_mysql_connection()
        cursor = conn.cursor()
        print("Conexão MySQL estabelecida com sucesso!")

        # Seleciona registros da tabela associados_completa com score >= 500
        cursor.execute("""
            SELECT
                ac.cpf_cnpj,
                ac.numero_pa,
                ac.valor_renda_bruta_mensal,
                ac.score,
                ac.nome_cliente,
                ac.data_movimento,
                ac.tipo_renda
            FROM associados_completa ac
            WHERE ac.score >= %s
        """, (score_minimo,))
        records = cursor.fetchall()
        print(f"Registros selecionados para atualização: {len(records)}")
        
        # Contadores de registros
        registros_atualizados = 0
        registros_existentes = 0
        registros_novos = 0
        registros_ignorados = 0
        registros_status_alterado = 0
        
        # Data atual para os timestamps
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for record in records:
            cpf_cnpj, numero_pa, valor_renda_bruta_mensal, score, nome_cliente, data_movimento, tipo_renda = record
            
            # Verifica se o CPF já existe e obtém seu status e data_aprovacao atual
            cursor.execute("""
                SELECT status, nome, data_aprovacao 
                FROM associados 
                WHERE cpf = %s
            """, (cpf_cnpj,))
            result = cursor.fetchone()
            
            if result:
                current_status, current_nome, data_aprovacao = result
                
                # Se não houver data_aprovacao, consideramos que pode atualizar
                if data_aprovacao is None or data_movimento > data_aprovacao.date():
                    # Determina o status baseado no tipo de renda
                    novo_status = 7 if tipo_renda and 'RENDA PONDERADA' in str(tipo_renda).upper() else 1

                    # Atualiza o registro
                    cursor.execute("""
                        UPDATE associados
                        SET pa = %s,
                            renda = %s,
                            score = %s,
                            nome = CASE
                                WHEN (nome IS NULL OR nome = '') AND %s IS NOT NULL
                                THEN %s
                                ELSE nome
                            END,
                            tipo_renda = %s,
                            status = %s,
                            data_atualizacao = %s
                        WHERE cpf = %s
                    """, (numero_pa, valor_renda_bruta_mensal, score, nome_cliente, nome_cliente, tipo_renda, novo_status, now, cpf_cnpj))
                    
                    registros_status_alterado += 1
                    print(f"CPF {cpf_cnpj} atualizado e status alterado para {novo_status} (data_movimento: {data_movimento}, data_aprovacao: {data_aprovacao})")
                else:
                    # Ignora a atualização pois data_movimento <= data_aprovacao
                    registros_ignorados += 1
                    print(f"CPF {cpf_cnpj} ignorado - data_movimento ({data_movimento}) <= data_aprovacao ({data_aprovacao})")
                
                registros_existentes += 1
            else:
                # Determina o status baseado no tipo de renda para novos registros
                novo_status = 7 if tipo_renda and 'RENDA PONDERADA' in str(tipo_renda).upper() else 1

                # Insere novo registro
                cursor.execute("""
                    INSERT INTO associados (
                        cpf,
                        pa,
                        renda,
                        score,
                        nome,
                        tipo_renda,
                        status,
                        data_atualizacao
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (cpf_cnpj, numero_pa, valor_renda_bruta_mensal, score, nome_cliente, tipo_renda, novo_status, now))
                registros_novos += 1
                print(f"CPF {cpf_cnpj} inserido como novo registro com status {novo_status}")
            
            # Log a cada 100 registros
            if (registros_existentes + registros_novos) % 100 == 0:
                print(f"Processados {registros_existentes + registros_novos} de {len(records)} registros")
        
        conn.commit()
        print(f"""
        Resumo do processamento:
        - Total de registros processados: {registros_existentes + registros_novos}
        - Registros existentes atualizados com status = 1: {registros_status_alterado}
        - Registros ignorados (data_movimento <= data_aprovacao): {registros_ignorados}
        - Novos registros inseridos com status 1: {registros_novos}
        """)
        
        # Verifica status após processamento
        cursor.execute("""
            SELECT s.nome, COUNT(a.id) as total
            FROM status s
            LEFT JOIN associados a ON a.status = s.id
            GROUP BY s.id, s.nome
            ORDER BY s.id
        """)
        status_counts = cursor.fetchall()
        print("\nDistribuição de status após processamento:")
        for status in status_counts:
            print(f"Status {status[0]}: {status[1]} registros")
        
        cursor.close()
        conn.close()
        
        return registros_existentes + registros_novos
        
    except Exception as e:
        print(f"Erro ao atualizar registros: {str(e)}")
        raise e

if __name__ == "__main__":
    update_associados_from_completa()
