import mysql.connector
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    try:
        logger.info("Tentando conectar ao MySQL...")
        conn = mysql.connector.connect(
            host='rpa.sicoobcredilivre.com.br',
            user='rpa',
            password='sicoob@123',
            database='rpa',
            port=3306,
            auth_plugin='mysql_native_password',
            ssl_disabled=True,
            connection_timeout=5,
            allow_local_infile=True,
            use_pure=True
        )
        logger.info("Conexão MySQL estabelecida com sucesso!")
        return conn
    except mysql.connector.Error as err:
        logger.error(f"Erro ao conectar ao MySQL: {err}")
        raise

def add_tipo_renda_column():
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Verifica se a coluna já existe
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'rpa' 
            AND TABLE_NAME = 'associados' 
            AND COLUMN_NAME = 'tipo_renda'
        """)
        column_exists = cursor.fetchone()[0] > 0

        if column_exists:
            logger.info("Coluna 'tipo_renda' já existe na tabela 'associados'")
        else:
            # Adiciona a coluna tipo_renda
            logger.info("Adicionando coluna 'tipo_renda' na tabela 'associados'...")
            cursor.execute("""
                ALTER TABLE associados
                ADD COLUMN tipo_renda TEXT DEFAULT NULL COMMENT 'Tipos de renda do associado (concatenados se múltiplos)'
            """)
            logger.info("Coluna 'tipo_renda' adicionada com sucesso!")

            # Adiciona índice para otimizar consultas
            logger.info("Adicionando índice para a coluna 'tipo_renda'...")
            try:
                cursor.execute("CREATE INDEX idx_tipo_renda ON associados (tipo_renda(255))")
                logger.info("Índice criado com sucesso!")
            except mysql.connector.Error as err:
                if err.errno == 1061:  # Duplicate key name
                    logger.info("Índice já existe, ignorando...")
                else:
                    raise

        # Verifica se a coluna também existe na tabela associados_completa
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'rpa' 
            AND TABLE_NAME = 'associados_completa' 
            AND COLUMN_NAME = 'tipo_renda'
        """)
        column_exists_completa = cursor.fetchone()[0] > 0

        if not column_exists_completa:
            # Adiciona a coluna tipo_renda na tabela associados_completa também
            logger.info("Adicionando coluna 'tipo_renda' na tabela 'associados_completa'...")
            cursor.execute("""
                ALTER TABLE associados_completa
                ADD COLUMN tipo_renda VARCHAR(255) DEFAULT NULL COMMENT 'Tipo de renda do associado'
            """)
            logger.info("Coluna 'tipo_renda' adicionada na tabela 'associados_completa' com sucesso!")

            # Adiciona índice
            try:
                cursor.execute("CREATE INDEX idx_tipo_renda_completa ON associados_completa (tipo_renda)")
                logger.info("Índice criado na tabela 'associados_completa' com sucesso!")
            except mysql.connector.Error as err:
                if err.errno == 1061:  # Duplicate key name
                    logger.info("Índice já existe na tabela 'associados_completa', ignorando...")
                else:
                    raise
        else:
            logger.info("Coluna 'tipo_renda' já existe na tabela 'associados_completa'")

        conn.commit()
        logger.info("Operação concluída com sucesso!")

        # Mostra a estrutura atualizada das tabelas
        logger.info("Estrutura atualizada da tabela 'associados':")
        cursor.execute("DESCRIBE associados")
        for row in cursor.fetchall():
            logger.info(f"  {row}")

        logger.info("Estrutura atualizada da tabela 'associados_completa':")
        cursor.execute("DESCRIBE associados_completa")
        for row in cursor.fetchall():
            logger.info(f"  {row}")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"Erro ao adicionar coluna 'tipo_renda': {str(e)}")
        raise

if __name__ == "__main__":
    add_tipo_renda_column()
