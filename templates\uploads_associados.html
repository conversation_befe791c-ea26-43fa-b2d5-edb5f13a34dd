{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="page-icon-modern bg-sicoob-verde-escuro me-3">
                            <i class="bi bi-cloud-upload"></i>
                        </div>
                        <div>
                            <h1 class="h3 mb-0 text-sicoob-verde-escuro">Upload de Associados</h1>
                            <p class="text-muted mb-0">Processamento em 2 etapas: Associados e Score</p>
                        </div>
                    </div>
                    <div class="step-counter-modern">
                        <span class="step-label">Passo</span>
                        <span class="step-current" id="currentStep">1</span>
                        <span class="step-separator">de</span>
                        <span class="step-total">2</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card-modern upload-card">
                <div class="card-body">
                    <!-- Barra de Progresso dos Steps Moderna -->
                    <div class="steps-progress-modern mb-5">
                        <div class="step-line-modern">
                            <div class="progress-modern">
                                <div class="progress-bar-modern" id="stepsProgress"></div>
                            </div>
                        </div>
                        <div class="step-indicators-modern">
                            <div class="step-indicator-modern step-indicator active" id="step1Indicator">
                                <div class="step-circle-modern">
                                    <i class="bi bi-file-earmark-spreadsheet"></i>
                                </div>
                                <div class="step-content-modern">
                                    <div class="step-title">Planilha Associados</div>
                                    <div class="step-subtitle">Upload dos dados de associados</div>
                                </div>
                            </div>
                            <div class="step-indicator-modern step-indicator" id="step2Indicator">
                                <div class="step-circle-modern">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="step-content-modern">
                                    <div class="step-title">Planilha Score</div>
                                    <div class="step-subtitle">Upload dos dados de score</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Upload Associados -->
                    <div id="step1" class="step-content">
                        <div class="upload-container-modern">
                            <div class="upload-area-modern mb-4">
                                <form id="uploadAssociadosForm" enctype="multipart/form-data">
                                    <div class="upload-dropzone-modern upload-dropzone" id="associadosDropzone">
                                        <div class="upload-icon-modern">
                                            <i class="bi bi-cloud-upload"></i>
                                        </div>
                                        <h5 class="upload-title">Arraste a planilha de Associados aqui</h5>
                                        <p class="upload-subtitle">ou clique para selecionar</p>
                                        <label class="btn-modern btn-sicoob-primary mb-3" for="associadosFile">
                                            <i class="bi bi-folder2-open me-2"></i>
                                            <span>Selecionar Arquivo</span>
                                        </label>
                                        <input class="d-none" type="file" id="associadosFile" name="file" accept=".xlsx">
                                        <p class="upload-format">Formato aceito: Excel (.xlsx)</p>
                                    </div>

                                    <div class="alert-modern alert-info-modern mt-4">
                                        <div class="alert-icon-modern">
                                            <i class="bi bi-info-circle"></i>
                                        </div>
                                        <div class="alert-content-modern">
                                            <div class="alert-title">Caminho para download da planilha:</div>
                                            <div class="alert-text">Sisbr Analítico / Conteúdo da equipe / Área Pública - Consumidor / Área Pública da Central 1003 - Consumidor / Área Pública da Singular 3049 - Consumidor / Cadastro / Cadastros >300 dias</div>
                                        </div>
                                    </div>

                                    <div id="associadosFileInfo" class="file-info d-none">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                                            <span class="filename"></span>
                                            <button type="button" class="btn-close ms-auto" onclick="removeFile('associados')"></button>
                                        </div>
                                    </div>

                                    <div class="progress upload-progress d-none" id="progressAssociados">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">
                                            <span class="progress-text">0%</span>
                                        </div>
                                    </div>

                                    <div class="mb-3 mt-4">
                                        <label for="scoreMinimo" class="form-label">Score mínimo para aprovação</label>
                                        <input type="number" class="form-control" id="scoreMinimo" name="scoreMinimo" value="500" min="0" max="1000" step="1">
                                        <div class="form-text">Apenas CPFs com score maior ou igual a este valor serão transferidos para a tabela de associados.</div>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <button type="submit" class="btn-modern btn-sicoob-primary btn-lg">
                                            <i class="bi bi-upload me-2"></i>
                                            <span>Iniciar Upload</span>
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="d-flex justify-content-end mt-4">
                                <button type="button" class="btn-modern btn-sicoob-success btn-lg d-none" id="nextStepBtn">
                                    <span>Avançar para Score</span>
                                    <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Upload Score -->
                    <div id="step2" class="step-content d-none">
                        <div class="upload-container-modern">
                            <div class="upload-area-modern mb-4">
                                <form id="uploadScoreForm" enctype="multipart/form-data">
                                    <div class="upload-dropzone-modern upload-dropzone" id="scoreDropzone">
                                        <div class="upload-icon-modern">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                        <h5 class="upload-title">Arraste a planilha de Score aqui</h5>
                                        <p class="upload-subtitle">ou clique para selecionar</p>
                                        <label class="btn-modern btn-sicoob-primary mb-3" for="scoreFile">
                                            <i class="bi bi-folder2-open me-2"></i>
                                            <span>Selecionar Arquivo</span>
                                        </label>
                                        <input class="d-none" type="file" id="scoreFile" name="file" accept=".xlsx">
                                        <p class="upload-format">Formato aceito: Excel (.xlsx)</p>
                                    </div>

                                    <div class="alert-modern alert-info-modern mt-4">
                                        <div class="alert-icon-modern">
                                            <i class="bi bi-info-circle"></i>
                                        </div>
                                        <div class="alert-content-modern">
                                            <div class="alert-title">Caminho para download da planilha:</div>
                                            <div class="alert-text">Sisbr Analítico / Números e Negócios / Relatórios / 17. Serasa / Serasa Detalhado</div>
                                        </div>
                                    </div>

                                    <div id="scoreFileInfo" class="file-info d-none">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                                            <span class="filename"></span>
                                            <button type="button" class="btn-close ms-auto" onclick="removeFile('score')"></button>
                                        </div>
                                    </div>

                                    <div class="progress upload-progress d-none" id="progressScore">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">
                                            <span class="progress-text">0%</span>
                                        </div>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <button type="submit" class="btn-modern btn-sicoob-primary btn-lg">
                                            <i class="bi bi-upload me-2"></i>
                                            <span>Iniciar Upload</span>
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <button class="btn-modern btn-sicoob-secondary btn-lg" onclick="goToStep(1)">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    <span>Voltar</span>
                                </button>
                                <button class="btn-modern btn-sicoob-success btn-lg d-none" id="executarBtn" onclick="executarProcessamento()">
                                    <i class="bi bi-play-fill me-2"></i>
                                    <span>Executar Processamento</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Conclusão -->
                    <div id="step3" class="step-content d-none">
                        <div class="upload-container-modern">
                            <div class="text-center mb-5">
                                <div class="success-checkmark mb-4">
                                    <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                                </div>
                                <h4 class="mb-3">Processamento Concluído com Sucesso!</h4>
                                <p class="text-muted">Os arquivos foram processados e os dados foram atualizados.</p>
                            </div>

                            <div class="card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">Resumo do Processamento</h5>
                                    <div id="processingSummary">
                                        <!-- Preenchido via JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <div class="row g-4 mb-4">
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="bi bi-file-earmark-excel mb-3" style="font-size: 2rem; color: #217346;"></i>
                                            <h5 class="card-title">Relatório Excel</h5>
                                            <p class="card-text">Download do relatório detalhado em formato Excel</p>
                                            <button class="btn btn-outline-success" onclick="downloadReport('excel')">
                                                <i class="bi bi-download me-2"></i>Download Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="bi bi-file-earmark-pdf mb-3" style="font-size: 2rem; color: #dc3545;"></i>
                                            <h5 class="card-title">Relatório PDF</h5>
                                            <p class="card-text">Download do relatório detalhado em formato PDF</p>
                                            <button class="btn btn-outline-danger" onclick="downloadReport('pdf')">
                                                <i class="bi bi-download me-2"></i>Download PDF
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button class="btn btn-secondary btn-lg" onclick="resetProcess()">
                                    <i class="bi bi-arrow-repeat me-2"></i>Iniciar Novo Processo
                                </button>
                                <button class="btn btn-primary btn-lg" onclick="window.location.href='/'">
                                    <i class="bi bi-house me-2"></i>Ir para Home
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Modal de Processamento -->
                    <div class="modal fade" id="processingModal" data-bs-backdrop="static" tabindex="-1">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-body text-center p-4">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">Processando...</span>
                                    </div>
                                    <h5 class="modal-title mb-3">Processando Arquivos</h5>
                                    <p class="text-muted mb-0">Por favor, aguarde enquanto processamos os arquivos...</p>
                                    <div class="progress mt-4" style="height: 10px;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* ===== CORES DA IDENTIDADE VISUAL SICOOB ===== */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
    }

    /* Classes utilitárias de cores */
    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* ===== HEADER MODERNO ===== */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(201, 210, 0, 0.05) 0%, rgba(125, 182, 28, 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(201, 210, 0, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(125, 182, 28, 0.3);
    }

    .step-counter-modern {
        background: rgba(0, 174, 157, 0.1);
        border: 1px solid rgba(0, 174, 157, 0.2);
        border-radius: 12px;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: var(--sicoob-azul);
    }

    .step-current {
        background: var(--sicoob-azul);
        color: white;
        border-radius: 8px;
        padding: 4px 8px;
        font-weight: 700;
        min-width: 24px;
        text-align: center;
    }

    /* ===== CARD MODERNO ===== */
    .card-modern {
        border: none;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        background: white;
        position: relative;
    }

    .upload-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--sicoob-verde-claro) 0%, var(--sicoob-azul) 100%);
    }

    .card-modern .card-body {
        padding: 32px;
    }

    /* ===== STEPS PROGRESS MODERNO ===== */
    .steps-progress-modern {
        position: relative;
        padding: 40px 0;
    }

    .step-line-modern {
        position: absolute;
        top: 50%;
        left: 10%;
        right: 10%;
        transform: translateY(-50%);
        z-index: 1;
    }

    .progress-modern {
        height: 4px;
        background: rgba(201, 210, 0, 0.2);
        border-radius: 2px;
        overflow: hidden;
    }

    .progress-bar-modern {
        height: 100%;
        background: linear-gradient(90deg, var(--sicoob-verde-claro) 0%, var(--sicoob-azul) 100%);
        border-radius: 2px;
        transition: width 0.6s ease;
        width: 0%;
    }

    .step-indicators-modern {
        display: flex;
        justify-content: space-between;
        position: relative;
        z-index: 2;
    }

    .step-indicator-modern {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        flex: 1;
        max-width: 300px;
        transition: all 0.3s ease;
    }

    .step-circle-modern {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: white;
        border: 3px solid #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        transition: all 0.3s ease;
        font-size: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .step-content-modern {
        max-width: 200px;
    }

    .step-title {
        font-size: 16px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 4px;
    }

    .step-subtitle {
        font-size: 13px;
        color: var(--sicoob-cinza);
        line-height: 1.4;
    }

    .step-indicator-modern.active .step-circle-modern {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        border-color: var(--sicoob-azul);
        color: white;
        box-shadow: 0 6px 20px rgba(0, 174, 157, 0.4);
        transform: scale(1.1);
    }

    .step-indicator-modern.active .step-title {
        color: var(--sicoob-azul);
    }

    .step-indicator-modern.complete .step-circle-modern {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        border-color: var(--sicoob-verde-escuro);
        color: white;
        box-shadow: 0 6px 20px rgba(125, 182, 28, 0.4);
    }

    .step-indicator-modern.complete .step-title {
        color: var(--sicoob-verde-escuro);
    }

    /* ===== UPLOAD AREA MODERNA ===== */
    .upload-container-modern {
        max-width: 800px;
        margin: 0 auto;
    }

    .upload-area-modern {
        background: rgba(201, 210, 0, 0.02);
        border-radius: 16px;
        padding: 32px;
        border: 1px solid rgba(201, 210, 0, 0.1);
    }

    .upload-dropzone-modern {
        border: 2px dashed rgba(0, 174, 157, 0.3);
        border-radius: 16px;
        padding: 48px 32px;
        text-align: center;
        background: white;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .upload-dropzone-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.02) 0%, rgba(125, 182, 28, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .upload-dropzone-modern:hover::before {
        opacity: 1;
    }

    .upload-dropzone-modern:hover {
        border-color: var(--sicoob-azul);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 174, 157, 0.15);
    }

    .upload-icon-modern {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        font-size: 32px;
        color: white;
        box-shadow: 0 8px 24px rgba(0, 174, 157, 0.3);
    }

    .upload-title {
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .upload-subtitle {
        font-size: 16px;
        color: var(--sicoob-cinza);
        margin-bottom: 24px;
    }

    .upload-format {
        font-size: 13px;
        color: var(--sicoob-cinza);
        margin-top: 16px;
        margin-bottom: 0;
    }

    /* ===== BOTÕES MODERNOS ===== */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    .btn-sicoob-secondary {
        background: linear-gradient(135deg, var(--sicoob-cinza) 0%, #7a8599 100%);
        color: white;
    }

    .btn-sicoob-secondary:hover {
        color: white;
    }

    .btn-sicoob-success {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        color: white;
    }

    .btn-sicoob-success:hover {
        color: white;
    }

    /* ===== ALERTS MODERNOS ===== */
    .alert-modern {
        border-radius: 12px;
        border: none;
        padding: 20px;
        display: flex;
        align-items: flex-start;
        gap: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .alert-info-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.08) 0%, rgba(125, 182, 28, 0.05) 100%);
        border-left: 4px solid var(--sicoob-azul);
    }

    .alert-icon-modern {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--sicoob-azul);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    .alert-content-modern {
        flex: 1;
    }

    .alert-title {
        font-size: 16px;
        font-weight: 700;
        color: var(--sicoob-azul);
        margin-bottom: 8px;
    }

    .alert-text {
        font-size: 14px;
        color: #2c3e50;
        line-height: 1.5;
        margin: 0;
    }

    /* ===== PROGRESS BAR MODERNA ===== */
    .upload-progress {
        border-radius: 8px;
        height: 12px;
        background: rgba(201, 210, 0, 0.1);
        overflow: hidden;
        margin-top: 16px;
    }

    .upload-progress .progress-bar {
        background: linear-gradient(90deg, var(--sicoob-verde-claro) 0%, var(--sicoob-azul) 100%);
        border-radius: 8px;
        position: relative;
    }

    .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 11px;
        font-weight: 700;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* ===== FILE INFO ===== */
    .file-info {
        background: rgba(125, 182, 28, 0.05);
        border: 1px solid rgba(125, 182, 28, 0.2);
        border-radius: 12px;
        padding: 16px;
        margin-top: 16px;
    }

    .file-info .filename {
        font-weight: 600;
        color: var(--sicoob-verde-escuro);
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-header-modern .d-flex {
            flex-direction: column;
            gap: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .step-counter-modern {
            align-self: stretch;
            justify-content: center;
        }

        .card-modern .card-body {
            padding: 24px;
        }

        .upload-area-modern {
            padding: 24px;
        }

        .upload-dropzone-modern {
            padding: 32px 16px;
        }

        .upload-icon-modern {
            width: 64px;
            height: 64px;
            font-size: 24px;
        }

        .step-circle-modern {
            width: 56px;
            height: 56px;
            font-size: 20px;
        }

        .step-indicators-modern {
            flex-direction: column;
            gap: 24px;
        }

        .step-line-modern {
            display: none;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Função para exibir mensagens flash
    function flash(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const container = document.querySelector('.card-body');
        const existingAlert = container.querySelector('.alert');

        if (existingAlert) {
            existingAlert.remove();
        }

        container.insertBefore(alertDiv, container.firstChild);
    }

    let associadosUploaded = false;
    let scoreUploaded = false;
    let processingModal = null;

    function updateProgress() {
        const progress = document.getElementById('stepsProgress');
        const currentStepElement = document.getElementById('currentStep');

        if (associadosUploaded && scoreUploaded) {
            progress.style.width = '100%';
            currentStepElement.textContent = '2';
        } else if (associadosUploaded) {
            progress.style.width = '50%';
            currentStepElement.textContent = '2';
        } else {
            progress.style.width = '0%';
            currentStepElement.textContent = '1';
        }
    }

    function goToStep(step) {
        const step1 = document.getElementById('step1');
        const step2 = document.getElementById('step2');
        const step1Indicator = document.getElementById('step1Indicator');
        const step2Indicator = document.getElementById('step2Indicator');
        const progress = document.getElementById('stepsProgress');
        const currentStepElement = document.getElementById('currentStep');

        [step1, step2].forEach(s => {
            s.classList.add('d-none');
            s.style.opacity = '0';
        });

        [step1Indicator, step2Indicator].forEach(i => {
            i.classList.remove('active', 'complete');
        });

        if (step === 1) {
            step1.classList.remove('d-none');
            step1Indicator.classList.add('active');
            progress.style.width = '0%';
            currentStepElement.textContent = '1';
            setTimeout(() => step1.style.opacity = '1', 50);
        } else if (step === 2) {
            step2.classList.remove('d-none');
            step1Indicator.classList.add('complete');
            step2Indicator.classList.add('active');
            progress.style.width = '50%';
            currentStepElement.textContent = '2';
            setTimeout(() => step2.style.opacity = '1', 50);
        }
    }

    function removeFile(type) {
        const fileInput = document.getElementById(`${type}File`);
        const fileInfo = document.getElementById(`${type}FileInfo`);
        const dropzone = document.getElementById(`${type}Dropzone`);
        const progress = document.getElementById(`progress${type.charAt(0).toUpperCase() + type.slice(1)}`);
        const submitButton = document.querySelector(`#upload${type.charAt(0).toUpperCase() + type.slice(1)}Form button[type="submit"]`);

        fileInput.value = '';
        fileInfo.classList.add('d-none');
        dropzone.classList.remove('d-none');
        progress.classList.add('d-none');
        submitButton.classList.remove('d-none');

        // Remove flash messages apenas se for do mesmo tipo de arquivo
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            const alertText = existingAlert.textContent.toLowerCase();
            if ((type === 'associados' && alertText.includes('associados')) ||
                (type === 'score' && alertText.includes('score'))) {
                existingAlert.remove();
            }
        }

        if (type === 'associados') {
            associadosUploaded = false;
            document.getElementById('nextStepBtn').classList.add('d-none');
        } else if (type === 'score') {
            scoreUploaded = false;
            document.getElementById('executarBtn').classList.add('d-none');
        }

        updateProgress();
    }

    function showProcessingModal() {
        if (!processingModal) {
            processingModal = new bootstrap.Modal(document.getElementById('processingModal'));
        }
        processingModal.show();
    }

    function hideProcessingModal() {
        if (processingModal) {
            processingModal.hide();
        }
    }

    function handleFileUpload(formId, progressId, uploadUrl, executeUrl, onSuccess) {
        const form = document.getElementById(formId);
        const progress = document.getElementById(progressId);
        const progressBar = progress.querySelector('.progress-bar');
        const progressText = progressBar.querySelector('.progress-text');
        const fileInput = form.querySelector('input[type="file"]');
        const dropzone = form.querySelector('.upload-dropzone');
        const fileInfo = document.getElementById(`${formId.replace('Form', '')}FileInfo`);
        const submitButton = form.querySelector('button[type="submit"]');

        // Drag and drop handlers
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('dragover');
        });

        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('dragover');
        });

        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('dragover');

            if (e.dataTransfer.files.length) {
                fileInput.files = e.dataTransfer.files;
                handleFileSelection(fileInput);
            }
        });

        // File selection handler
        fileInput.addEventListener('change', () => handleFileSelection(fileInput));

        function handleFileSelection(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                dropzone.classList.add('d-none');
                fileInfo.classList.remove('d-none');
                fileInfo.querySelector('.filename').textContent = file.name;
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!fileInput.files || fileInput.files.length === 0) {
                flash('danger', 'Por favor, selecione um arquivo.');
                return;
            }

            const formData = new FormData(form);
            progress.classList.remove('d-none');
            progressBar.style.width = '0%';
            progressText.textContent = '0%';

            try {
                progressBar.style.width = '30%';
                progressText.textContent = '30%';

                const uploadResponse = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                progressBar.style.width = '60%';
                progressText.textContent = '60%';

                const uploadResult = await uploadResponse.json();

                if (!uploadResponse.ok) {
                    throw new Error(uploadResult.error || 'Erro no upload');
                }

                if (formId === 'uploadAssociadosForm') {
                    progressBar.style.width = '80%';
                    progressText.textContent = '80%';

                    showProcessingModal();
                    const executeResponse = await fetch('/executar/associados', {
                        method: 'POST'
                    });

                    const executeResult = await executeResponse.json();

                    if (!executeResponse.ok) {
                        throw new Error(executeResult.error || 'Erro no processamento');
                    }

                    hideProcessingModal();

                    if (executeResult.estatisticas) {
                        const stats = executeResult.estatisticas;
                        flash('success', `
                            Processamento concluído com sucesso!<br>
                            <small>
                            • Total de linhas na planilha: ${stats.total_linhas_planilha}<br>
                            • CPFs únicos na planilha: ${stats.cpfs_unicos_planilha}<br>
                            • CPFs antes da atualização: ${stats.cpfs_antes}<br>
                            • CPFs após atualização: ${stats.cpfs_depois}<br>
                            • Registros atualizados: ${stats.registros_atualizados_associados}
                            </small>
                        `);
                    }
                }

                progressBar.style.width = '100%';
                progressText.textContent = '100%';

                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess();
                }

                // Esconde o botão de submit após o sucesso
                submitButton.classList.add('d-none');

            } catch (error) {
                console.error('Erro durante o processo:', error);
                progress.classList.add('d-none');
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
                hideProcessingModal();
                flash('danger', 'Erro: ' + error.message);
                // Mostra o botão de submit novamente em caso de erro
                submitButton.classList.remove('d-none');
            }
        });

        // Mostra o botão de submit quando um novo arquivo é selecionado
        fileInput.addEventListener('change', () => {
            submitButton.classList.remove('d-none');
        });
    }

    async function executarProcessamento() {
        try {
            showProcessingModal();
            const scoreMinimo = document.getElementById('scoreMinimo').value;
            const response = await fetch('/executar/associados_score', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ score_minimo: scoreMinimo })
            });
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Erro no processamento');
            }

            hideProcessingModal();

            const stats = result.estatisticas || {};
            let mensagem = `
                <div class="mb-3">
                    <strong>Estatísticas do Processamento:</strong>
                </div>
                <ul class="list-unstyled">
                    <li><i class="bi bi-dot me-2"></i>Total de CPFs na base: ${stats.total_cpfs || 0}</li>
                    <li><i class="bi bi-dot me-2"></i>Total de CPFs com Score >= 500: ${stats.total_cpfs_score_500 || 0}</li>
                    <li><i class="bi bi-dot me-2"></i>Total de Associados: ${stats.total_associados || 0}</li>
                    <li><i class="bi bi-dot me-2"></i>Registros atualizados: ${stats.registros_atualizados || 0}</li>
                </ul>
            `;

            if (!stats.registros_atualizados || stats.registros_atualizados === 0) {
                mensagem += `
                    <div class="alert alert-warning mt-3">
                        <strong>Atenção:</strong> Nenhum registro foi atualizado.<br>
                        Possíveis motivos:
                        <ul class="mb-0 mt-2">
                            <li>CPFs da planilha não encontrados na base</li>
                            <li>Nenhum CPF com Score >= 500</li>
                            <li>Dados já estão atualizados</li>
                        </ul>
                    </div>
                `;
            }

            // Atualiza o resumo na página de conclusão
            document.getElementById('processingSummary').innerHTML = mensagem;

            // Mostra a página de conclusão
            document.querySelectorAll('.step-content').forEach(step => step.classList.add('d-none'));
            document.getElementById('step3').classList.remove('d-none');
            setTimeout(() => document.getElementById('step3').style.opacity = '1', 50);

            // Atualiza indicadores de progresso
            document.getElementById('stepsProgress').style.width = '100%';
            document.getElementById('currentStep').textContent = '3';
            document.querySelectorAll('.step-indicator').forEach(indicator => {
                indicator.classList.remove('active');
                indicator.classList.add('complete');
            });

        } catch (error) {
            hideProcessingModal();
            console.error('Erro completo:', error);
            flash('danger', 'Erro ao processar: ' + error.message);
        }
    }

    async function downloadReport(type) {
        try {
            const response = await fetch(`/download/relatorio/associados/${type}`);

            if (!response.ok) {
                throw new Error(`Erro ao baixar relatório: ${response.statusText}`);
            }

            // Obtém o nome do arquivo do cabeçalho da resposta
            const contentDisposition = response.headers.get('content-disposition');
            let filename = 'relatorio';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }
            filename += type === 'excel' ? '.xlsx' : '.pdf';

            // Faz o download do arquivo
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

        } catch (error) {
            console.error('Erro ao baixar relatório:', error);
            flash('danger', error.message);
        }
    }

    function resetProcess() {
        // Reseta os formulários
        document.getElementById('uploadAssociadosForm').reset();
        document.getElementById('uploadScoreForm').reset();

        // Esconde as barras de progresso
        document.getElementById('progressAssociados').classList.add('d-none');
        document.getElementById('progressScore').classList.add('d-none');

        // Esconde os botões de ação
        document.getElementById('nextStepBtn').classList.add('d-none');
        document.getElementById('executarBtn').classList.add('d-none');

        // Reseta as informações de arquivo
        document.getElementById('associadosFileInfo').classList.add('d-none');
        document.getElementById('scoreFileInfo').classList.add('d-none');

        // Mostra as áreas de drop
        document.getElementById('associadosDropzone').classList.remove('d-none');
        document.getElementById('scoreDropzone').classList.remove('d-none');

        // Reseta os estados
        associadosUploaded = false;
        scoreUploaded = false;

        // Reseta o progresso
        updateProgress();

        // Volta para o step 1
        goToStep(1);

        // Remove mensagens flash
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
    }

    // Inicializa os handlers para os formulários
    handleFileUpload(
        'uploadAssociadosForm',
        'progressAssociados',
        '/upload/associados',
        '/executar/associados',
        () => {
            associadosUploaded = true;
            updateProgress();
            document.getElementById('nextStepBtn').classList.remove('d-none');
        }
    );

    handleFileUpload(
        'uploadScoreForm',
        'progressScore',
        '/upload/score',
        '/executar/score',
        () => {
            scoreUploaded = true;
            updateProgress();
            document.getElementById('executarBtn').classList.remove('d-none');
        }
    );

    document.getElementById('nextStepBtn').addEventListener('click', function() {
        goToStep(2);
    });

    // Inicializa o modal de processamento
    document.addEventListener('DOMContentLoaded', () => {
        processingModal = new bootstrap.Modal(document.getElementById('processingModal'));
    });

    // Adiciona listeners para limpar mensagens apenas quando selecionar um novo arquivo
    document.querySelectorAll('input[type="file"]').forEach(input => {
        input.addEventListener('change', () => {
            const form = input.closest('form');
            // Só limpa a mensagem se for do mesmo formulário
            const existingAlert = document.querySelector('.alert');
            if (existingAlert && form.id === 'uploadAssociadosForm' && input.id === 'associadosFile') {
                existingAlert.remove();
            } else if (existingAlert && form.id === 'uploadScoreForm' && input.id === 'scoreFile') {
                existingAlert.remove();
            }
        });
    });

    // Limpa mensagens ao mudar de step
    document.getElementById('nextStepBtn').addEventListener('click', () => {
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        goToStep(2);
    });
</script>
{% endblock %}