{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>Gerenciamento de Usuários</h2>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <!-- Botão para adicionar novo usuário -->
    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#addUserModal">
        Adicionar Usuário
    </button>
    
    <!-- Tabela de usuários -->
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Nome</th>
                    <th>Usuário</th>
                    <th>Admin</th>
                    <th>PA</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user[1] }}</td>
                    <td>{{ user[2] }}</td>
                    <td>{% if user[3] %}Sim{% else %}Não{% endif %}</td>
                    <td>{{ user[4] }}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editUserModal"
                            data-user-id="{{ user[0] }}"
                            data-user-name="{{ user[1] }}"
                            data-user-username="{{ user[2] }}"
                            data-user-is-admin="{{ user[3] }}"
                            data-user-pa="{{ user[4] }}"
                            data-user-permissions='{{ user[5]|tojson }}'>
                            Editar
                        </button>
                        <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal"
                            data-user-id="{{ user[0] }}"
                            data-user-name="{{ user[1] }}">
                            Excluir
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Modal Adicionar Usuário -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Adicionar Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ url_for('add_user') }}" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">Usuário</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Senha Inicial</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" value="1">
                            <label class="form-check-label" for="is_admin">Administrador</label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">PA</label>
                            <select class="form-select" id="addUserPA" name="pa" required>
                                <option value="0">0</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="97">97</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Permissões de Módulos</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_cpfs" name="modules" value="cpfs" checked disabled>
                                <label class="form-check-label" for="module_cpfs">CPFs (Padrão)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_rpa_monitor" name="modules" value="rpa_monitor">
                                <label class="form-check-label" for="module_rpa_monitor">Monitoramento RPAs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_usuarios" name="modules" value="usuarios">
                                <label class="form-check-label" for="module_usuarios">Usuários</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_configuracoes" name="modules" value="configuracoes">
                                <label class="form-check-label" for="module_configuracoes">Configurações</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_dashboard" name="modules" value="dashboard">
                                <label class="form-check-label" for="module_dashboard">Dashboard</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Adicionar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal Editar Usuário -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ url_for('edit_user') }}" method="post">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_username" class="form-label">Usuário</label>
                            <input type="text" class="form-control" id="edit_username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_password" class="form-label">Nova Senha (opcional)</label>
                            <input type="password" class="form-control" id="edit_password" name="password">
                            <small class="form-text text-muted">Deixe em branco para manter a senha atual</small>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_is_admin" name="is_admin" value="1">
                                <label class="form-check-label" for="edit_is_admin">Administrador</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editUserPA" class="form-label">PA</label>
                            <select class="form-select" id="editUserPA" name="pa" required>
                                <option value="0">0</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="97">97</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Módulos</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_cpfs_module" name="modules" value="cpfs">
                                <label class="form-check-label" for="edit_cpfs_module">CPFs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_dashboard_module" name="modules" value="dashboard">
                                <label class="form-check-label" for="edit_dashboard_module">Dashboard</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_rpa_monitor_module" name="modules" value="rpa_monitor">
                                <label class="form-check-label" for="edit_rpa_monitor_module">RPAs</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Modal Excluir Usuário -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Excluir Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ url_for('delete_user') }}" method="post">
                    <input type="hidden" id="delete_user_id" name="user_id">
                    <div class="modal-body">
                        <p>Tem certeza que deseja excluir o usuário <strong><span id="delete_user_name"></span></strong>?</p>
                        <p class="text-danger"><small>Esta ação não pode ser desfeita.</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">Excluir</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar modal de edição
    const editUserModal = document.getElementById('editUserModal');
    editUserModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');
        const userUsername = button.getAttribute('data-user-username');
        const userIsAdmin = button.getAttribute('data-user-is-admin');
        const userPA = button.getAttribute('data-user-pa');
        const userPermissions = JSON.parse(button.getAttribute('data-user-permissions'));
        
        // Preencher campos do formulário
        editUserModal.querySelector('#edit_user_id').value = userId;
        editUserModal.querySelector('#edit_name').value = userName;
        editUserModal.querySelector('#edit_username').value = userUsername;
        editUserModal.querySelector('#edit_is_admin').checked = userIsAdmin === '1';
        editUserModal.querySelector('#editUserPA').value = userPA;
        editUserModal.querySelector('#edit_password').value = '';
        
        // Marcar módulos de acordo com as permissões
        editUserModal.querySelector('#edit_cpfs_module').checked = userPermissions.includes('cpfs');
        editUserModal.querySelector('#edit_dashboard_module').checked = userPermissions.includes('dashboard');
        editUserModal.querySelector('#edit_rpa_monitor_module').checked = userPermissions.includes('rpa_monitor');
        
        // Se for admin, marcar e desabilitar todos os módulos
        const moduleCheckboxes = editUserModal.querySelectorAll('input[name="modules"]');
        moduleCheckboxes.forEach(checkbox => {
            if (userIsAdmin === '1') {
                checkbox.checked = true;
                checkbox.disabled = true;
            } else {
                checkbox.disabled = false;
            }
        });
    });
    
    // Ao marcar/desmarcar administrador
    document.getElementById('edit_is_admin').addEventListener('change', function(event) {
        const isAdmin = event.target.checked;
        const moduleCheckboxes = editUserModal.querySelectorAll('input[name="modules"]');
        
        moduleCheckboxes.forEach(checkbox => {
            if (isAdmin) {
                checkbox.checked = true;
                checkbox.disabled = true;
            } else {
                checkbox.disabled = false;
            }
        });
    });
    
    // Configurar modal de exclusão
    const deleteUserModal = document.getElementById('deleteUserModal');
    deleteUserModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');
        
        deleteUserModal.querySelector('#delete_user_id').value = userId;
        deleteUserModal.querySelector('#delete_user_name').textContent = userName;
    });
});
</script>
{% endblock %}
