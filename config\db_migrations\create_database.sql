-- Criação da tabela database para armazenar datas de atualização
CREATE TABLE IF NOT EXISTS `database` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `data_associados` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    `data_limitedisp` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    `data_limiteuso` DATETIME NULL,
    `data_chdisp` DATETIME NULL,
    `data_chuso` DATETIME NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insere um registro inicial
INSERT INTO `database` (data_associados, data_limitedisp, data_limiteuso, data_chdisp, data_chuso)
VALUES (NULL, NULL, NULL, NULL, NULL); 