{% extends "base.html" %}

{% block title %}Gerenciamento de Usuários{% endblock %}

{% block extra_css %}
<style>
    /* ===== CORES DA IDENTIDADE VISUAL SICOOB ===== */
    :root {
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-escuro: #7DB61C;
        --sicoob-azul: #00AE9D;
        --sicoob-cinza: #8E9AAF;
        --sicoob-roxo: #49479D;
    }

    /* Classes utilitárias de cores */
    .text-sicoob-verde-claro { color: var(--sicoob-verde-claro) !important; }
    .text-sicoob-verde-escuro { color: var(--sicoob-verde-escuro) !important; }
    .text-sicoob-azul { color: var(--sicoob-azul) !important; }
    .text-sicoob-cinza { color: var(--sicoob-cinza) !important; }
    .text-sicoob-roxo { color: var(--sicoob-roxo) !important; }
    .bg-sicoob-verde-claro { background-color: var(--sicoob-verde-claro) !important; }
    .bg-sicoob-verde-escuro { background-color: var(--sicoob-verde-escuro) !important; }
    .bg-sicoob-azul { background-color: var(--sicoob-azul) !important; }
    .bg-sicoob-cinza { background-color: var(--sicoob-cinza) !important; }
    .bg-sicoob-roxo { background-color: var(--sicoob-roxo) !important; }

    /* ===== HEADER MODERNO ===== */
    .page-header-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.05) 0%, rgba(125, 182, 28, 0.05) 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid rgba(0, 174, 157, 0.1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
    }

    .page-icon-modern {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
    }

    /* ===== BOTÕES MODERNOS ===== */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-modern:hover::before {
        opacity: 1;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .btn-sicoob-primary {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-sicoob-primary:hover {
        color: white;
    }

    /* ===== ALERTS MODERNOS ===== */
    .alert-modern {
        border-radius: 12px;
        padding: 16px 20px;
        margin-bottom: 20px;
        border: none;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .alert-modern::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
    }

    .alert-success-modern {
        background: linear-gradient(135deg, rgba(125, 182, 28, 0.1) 0%, rgba(107, 160, 21, 0.05) 100%);
        color: var(--sicoob-verde-escuro);
    }

    .alert-success-modern::before {
        background: var(--sicoob-verde-escuro);
    }

    .alert-danger-modern {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.05) 100%);
        color: #dc3545;
    }

    .alert-danger-modern::before {
        background: #dc3545;
    }

    .alert-warning-modern {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%);
        color: #ffc107;
    }

    .alert-warning-modern::before {
        background: #ffc107;
    }

    .alert-info-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.1) 0%, rgba(0, 138, 122, 0.05) 100%);
        color: var(--sicoob-azul);
    }

    .alert-info-modern::before {
        background: var(--sicoob-azul);
    }

    .alert-icon-modern {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-right: 16px;
        background: rgba(255, 255, 255, 0.2);
    }

    .alert-content-modern {
        flex: 1;
        font-weight: 500;
    }

    .btn-close-modern {
        background: none;
        border: none;
        font-size: 18px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
        padding: 4px;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close-modern:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.2);
    }

    /* ===== CARD MODERNO ===== */
    .card-modern {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: none;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-modern:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    .card-header-modern {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.05) 0%, rgba(125, 182, 28, 0.05) 100%);
        padding: 24px;
        border-bottom: 1px solid rgba(0, 174, 157, 0.1);
    }

    .header-icon-modern {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        box-shadow: 0 4px 12px rgba(125, 182, 28, 0.3);
    }

    .header-title-modern {
        font-size: 18px;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    .header-subtitle-modern {
        font-size: 14px;
        color: var(--sicoob-cinza);
        margin: 0;
    }

    .card-body-modern {
        padding: 0;
    }

    /* ===== TABELA MODERNA ===== */
    .table-modern {
        width: 100%;
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-header-modern {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .table-header-modern th {
        padding: 16px 20px;
        font-weight: 600;
        font-size: 13px;
        color: #495057;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        position: relative;
    }

    .table-header-modern th:first-child {
        border-top-left-radius: 0;
    }

    .table-header-modern th:last-child {
        border-top-right-radius: 0;
    }

    .table-body-modern tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f3f4;
    }

    .table-body-modern tr:hover {
        background: linear-gradient(135deg, rgba(0, 174, 157, 0.02) 0%, rgba(125, 182, 28, 0.02) 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .table-body-modern td {
        padding: 16px 20px;
        border: none;
        vertical-align: middle;
    }

    /* ===== CÉLULAS ESPECIAIS ===== */
    .user-name-cell {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .user-avatar-modern {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        box-shadow: 0 2px 8px rgba(0, 174, 157, 0.3);
    }

    .user-info-modern {
        flex: 1;
    }

    .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
        margin-bottom: 2px;
    }

    .user-meta {
        font-size: 12px;
        color: var(--sicoob-cinza);
    }

    .username-modern {
        font-family: 'Courier New', monospace;
        background: rgba(0, 174, 157, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 13px;
        color: var(--sicoob-azul);
        font-weight: 600;
    }

    .date-modern {
        font-size: 13px;
        color: var(--sicoob-cinza);
    }

    /* ===== BADGES MODERNOS ===== */
    .badges-container-modern {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
    }

    .badge-modern {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .badge-pa {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        color: white;
    }

    .badge-permission {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .badge-admin {
        background: linear-gradient(135deg, var(--sicoob-roxo) 0%, #3d3a85 100%);
        color: white;
    }

    .badge-ad {
        background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
        color: white;
    }

    .badge-local {
        background: linear-gradient(135deg, var(--sicoob-cinza) 0%, #7a8599 100%);
        color: white;
    }

    .status-badge-modern {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .status-active {
        background: linear-gradient(135deg, rgba(125, 182, 28, 0.15) 0%, rgba(107, 160, 21, 0.1) 100%);
        color: var(--sicoob-verde-escuro);
        border: 1px solid rgba(125, 182, 28, 0.2);
    }

    .status-pending {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 171, 0, 0.1) 100%);
        color: #f57c00;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }

    /* ===== BOTÕES DE AÇÃO ===== */
    .actions-modern {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .btn-action-modern {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .btn-action-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.2);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .btn-action-modern:hover::before {
        opacity: 1;
    }

    .btn-action-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .btn-edit-modern {
        background: linear-gradient(135deg, var(--sicoob-azul) 0%, #008a7a 100%);
        color: white;
    }

    .btn-success-modern {
        background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, #6ba015 100%);
        color: white;
    }

    .btn-warning-modern {
        background: linear-gradient(135deg, #ffc107 0%, #ffab00 100%);
        color: white;
    }

    .btn-danger-modern {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .page-header-modern {
            padding: 16px;
        }

        .page-header-modern .d-flex {
            flex-direction: column;
            gap: 16px;
        }

        .page-icon-modern {
            width: 48px;
            height: 48px;
            font-size: 20px;
        }

        .card-header-modern {
            padding: 20px 16px;
        }

        .header-icon-modern {
            width: 40px;
            height: 40px;
            font-size: 18px;
        }

        .table-header-modern th,
        .table-body-modern td {
            padding: 12px 16px;
        }

        .user-avatar-modern {
            width: 36px;
            height: 36px;
            font-size: 18px;
        }

        .user-name {
            font-size: 13px;
        }

        .user-meta {
            font-size: 11px;
        }

        .btn-action-modern {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }

        .actions-modern {
            gap: 6px;
        }

        .badge-modern {
            font-size: 10px;
            padding: 3px 6px;
        }

        .status-badge-modern {
            font-size: 11px;
            padding: 4px 8px;
        }
    }

    @media (max-width: 576px) {
        .table-responsive {
            font-size: 12px;
        }

        .badges-container-modern {
            flex-direction: column;
            gap: 2px;
        }

        .btn-modern {
            padding: 10px 16px;
            font-size: 13px;
        }
    }

    /* ===== ANIMAÇÕES ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .table-row-modern {
        animation: fadeInUp 0.3s ease forwards;
    }

    .table-row-modern:nth-child(even) {
        animation-delay: 0.1s;
    }

    .table-row-modern:nth-child(odd) {
        animation-delay: 0.05s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Moderno -->
    <div class="row mb-4">
        <div class="col">
            <div class="page-header-modern">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="page-icon-modern bg-sicoob-azul me-3">
                            <i class="bi bi-people"></i>
                        </div>
                        <div>
                            <h1 class="h3 mb-0 text-sicoob-azul">Gerenciamento de Usuários</h1>
                            <p class="text-muted mb-0">Controle de acesso e permissões do sistema</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button type="button" class="btn-modern btn-sicoob-primary" data-bs-toggle="modal" data-bs-target="#novoUsuarioModal">
                            <i class="bi bi-person-plus me-2"></i>
                            <span>Novo Usuário</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mensagens de feedback -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert-modern alert-{{ category }}-modern alert-dismissible fade show" role="alert">
                    <div class="alert-icon-modern">
                        {% if category == 'success' %}
                            <i class="bi bi-check-circle"></i>
                        {% elif category == 'danger' %}
                            <i class="bi bi-exclamation-triangle"></i>
                        {% elif category == 'warning' %}
                            <i class="bi bi-exclamation-circle"></i>
                        {% else %}
                            <i class="bi bi-info-circle"></i>
                        {% endif %}
                    </div>
                    <div class="alert-content-modern">
                        <span>{{ message }}</span>
                    </div>
                    <button type="button" class="btn-close-modern" data-bs-dismiss="alert" aria-label="Close">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <!-- Tabela de Usuários Moderna -->
    <div class="row">
        <div class="col-12">
            <div class="card-modern users-table-card">
                <div class="card-header-modern">
                    <div class="d-flex align-items-center">
                        <div class="header-icon-modern bg-sicoob-verde-escuro me-3">
                            <i class="bi bi-people"></i>
                        </div>
                        <div>
                            <h5 class="header-title-modern mb-0">Lista de Usuários</h5>
                            <p class="header-subtitle-modern mb-0">{{ users|length }} usuário(s) cadastrado(s)</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-modern">
                    <div class="table-responsive">
                        <table class="table-modern">
                            <thead class="table-header-modern">
                                <tr>
                                    <th>Nome</th>
                                    <th>Usuário</th>
                                    <th>PA</th>
                                    <th>Permissões</th>
                                    <th>Tipo</th>
                                    <th>Status</th>
                                    <th>Último Login</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody class="table-body-modern">
                                {% for user in users %}
                                <tr class="table-row-modern">
                                    <td class="user-name-cell">
                                        <div class="user-avatar-modern">
                                            <i class="bi bi-person-circle"></i>
                                        </div>
                                        <div class="user-info-modern">
                                            <div class="user-name">{{ user.name }}</div>
                                            <div class="user-meta">ID: {{ user.id }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="username-modern">{{ user.username }}</span>
                                    </td>
                                    <td>
                                        <div class="badges-container-modern">
                                            {% for pa in user.pa %}
                                                <span class="badge-modern badge-pa">PA {{ pa }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="badges-container-modern">
                                            {% for permission in user.permissions %}
                                                <span class="badge-modern badge-permission">{{ permission }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if user.is_admin %}
                                            <span class="badge-modern badge-admin">
                                                <i class="bi bi-shield-check me-1"></i>Admin
                                            </span>
                                        {% elif user.is_ad_user %}
                                            <span class="badge-modern badge-ad">
                                                <i class="bi bi-microsoft me-1"></i>AD
                                            </span>
                                        {% else %}
                                            <span class="badge-modern badge-local">
                                                <i class="bi bi-person me-1"></i>Local
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="status-badge-modern status-active">
                                                <i class="bi bi-check-circle me-1"></i>Ativo
                                            </span>
                                        {% else %}
                                            <span class="status-badge-modern status-pending">
                                                <i class="bi bi-clock me-1"></i>Pendente
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="date-modern">{{ user.updated_at }}</span>
                                    </td>
                                    <td>
                                        <div class="actions-modern">
                                            <button class="btn-action-modern btn-edit-modern"
                                                    onclick="editarUsuario('{{ user.id }}')"
                                                    title="Editar usuário">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            {% if user.is_ad_user %}
                                            <form action="{{ url_for('usuarios.toggle_user_active', user_id=user.id) }}"
                                                  method="POST"
                                                  class="d-inline"
                                                  onsubmit="return confirm('Tem certeza que deseja {% if user.is_active %}desativar{% else %}ativar{% endif %} este usuário?')">
                                                <button type="submit"
                                                        class="btn-action-modern {% if user.is_active %}btn-warning-modern{% else %}btn-success-modern{% endif %}"
                                                        title="{% if user.is_active %}Desativar{% else %}Ativar{% endif %} usuário">
                                                    <i class="bi {% if user.is_active %}bi-person-x{% else %}bi-person-check{% endif %}"></i>
                                                </button>
                                            </form>
                                            <button type="button"
                                                    class="btn-action-modern btn-danger-modern"
                                                    onclick="excluirUsuarioAD('{{ user.id }}', '{{ user.name }}')"
                                                    title="Excluir usuário">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                            {% if not user.is_ad_user %}
                                            <button type="button"
                                                    class="btn-action-modern btn-danger-modern"
                                                    onclick="excluirUsuario('{{ user.id }}', '{{ user.name }}')"
                                                    title="Excluir usuário">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Novo Usuário -->
<div class="modal fade" id="novoUsuarioModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Novo Usuário</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('usuarios.adicionar_usuario') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Nome</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Usuário</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Senha</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="pa" class="form-label">PA</label>
                        <select class="form-select" id="pa" name="pa" multiple size="5">
                            {% for pa in available_pas %}
                            <option value="{{ pa }}">PA {{ pa }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">Pressione CTRL para selecionar múltiplos PAs</small>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                            <label class="form-check-label" for="is_admin">Administrador</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Módulos</label>
                        <div class="modules-list">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_dashboard" name="modules" value="dashboard">
                                <label class="form-check-label" for="module_dashboard">Dashboard</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_rpa_monitor" name="modules" value="rpa_monitor">
                                <label class="form-check-label" for="module_rpa_monitor">RPA Monitor</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_cpfs" name="modules" value="cpfs">
                                <label class="form-check-label" for="module_cpfs">CPFs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_uploads" name="modules" value="uploads">
                                <label class="form-check-label" for="module_uploads">Uploads</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_robozometro" name="modules" value="robozometro">
                                <label class="form-check-label" for="module_robozometro">Robozômetro</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_relatorios" name="modules" value="relatorios">
                                <label class="form-check-label" for="module_relatorios">Relatórios</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_crls" name="modules" value="crls">
                                <label class="form-check-label" for="module_crls">CRLs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="module_audit" name="modules" value="audit">
                                <label class="form-check-label" for="module_audit">Auditoria</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Adicionar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Editar Usuário -->
<div class="modal fade" id="editarUsuarioModal" tabindex="-1" aria-labelledby="editarUsuarioModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editarUsuarioModalLabel">Editar Usuário</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <!-- Div para mensagens de erro -->
                <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>
                
                <form id="editUserForm" method="POST">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label for="editName" class="form-label">Nome</label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Nome de Usuário</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">Senha (deixe em branco para manter a atual)</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editPa" class="form-label">PA</label>
                        <select class="form-select" id="editPa" name="pa" multiple size="5">
                            {% for pa in available_pas %}
                            <option value="{{ pa }}">PA {{ pa }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">Pressione CTRL para selecionar múltiplos PAs</small>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="editIsAdmin" name="is_admin">
                            <label class="form-check-label" for="editIsAdmin">Administrador</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Módulos</label>
                        <div class="modules-list">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_dashboard_module" name="modules" value="dashboard">
                                <label class="form-check-label" for="edit_dashboard_module">Dashboard</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_rpa_monitor_module" name="modules" value="rpa_monitor">
                                <label class="form-check-label" for="edit_rpa_monitor_module">RPA Monitor</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_cpfs_module" name="modules" value="cpfs">
                                <label class="form-check-label" for="edit_cpfs_module">CPFs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_uploads_module" name="modules" value="uploads">
                                <label class="form-check-label" for="edit_uploads_module">Uploads</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_robozometro_module" name="modules" value="robozometro">
                                <label class="form-check-label" for="edit_robozometro_module">Robozômetro</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_relatorios_module" name="modules" value="relatorios">
                                <label class="form-check-label" for="edit_relatorios_module">Relatórios</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_crls_module" name="modules" value="crls">
                                <label class="form-check-label" for="edit_crls_module">CRLs</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input edit-module" id="edit_audit_module" name="modules" value="audit">
                                <label class="form-check-label" for="edit_audit_module">Auditoria</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarEdicao()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Excluir Usuário -->
<div class="modal fade" id="excluirUsuarioModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Excluir Usuário</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o usuário <strong id="excluirUsuarioNome"></strong>?</p>
            </div>
            <div class="modal-footer">
                <form id="excluirUsuarioForm" action="{{ url_for('usuarios.excluir_usuario', user_id=0) }}" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão de usuário AD -->
<div class="modal fade" id="modalExcluirUsuarioAD" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Excluir Usuário AD</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o usuário AD <strong id="nomeUsuarioADExcluir"></strong>?</p>
                <p class="text-danger">Esta ação não pode ser desfeita!</p>
            </div>
            <div class="modal-footer">
                <form id="formExcluirUsuarioAD" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let currentUserId = null;
let editModal = null;

document.addEventListener('DOMContentLoaded', function() {
    // Inicializa o modal
    editModal = new bootstrap.Modal(document.getElementById('editarUsuarioModal'));
    
    // Adiciona evento para quando o checkbox de admin é alterado
    document.getElementById('editIsAdmin').addEventListener('change', function() {
        const moduleCheckboxes = document.querySelectorAll('.edit-module');
        moduleCheckboxes.forEach(checkbox => {
            if (this.checked) {
                checkbox.checked = true;
                checkbox.disabled = true;
            } else {
                checkbox.disabled = false;
            }
        });
    });
    
    // Mesmo comportamento para o formulário de adicionar
    document.getElementById('is_admin').addEventListener('change', function() {
        const moduleCheckboxes = document.querySelectorAll('input[name="modules"]');
        moduleCheckboxes.forEach(checkbox => {
            if (this.checked) {
                checkbox.checked = true;
                checkbox.disabled = true;
            } else {
                checkbox.disabled = false;
            }
        });
    });
});

function editarUsuario(userId) {
    currentUserId = userId;
    
    // Limpa mensagens de erro anteriores
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('errorMessage').textContent = '';
    
    // Carrega os dados do usuário
    fetch(`/usuarios/editar/${userId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('editName').value = data.name;
            document.getElementById('editUsername').value = data.username;
            document.getElementById('editPassword').value = '';
            document.getElementById('editIsAdmin').checked = data.is_admin;
            
            // Limpa seleções anteriores
            const paSelect = document.getElementById('editPa');
            Array.from(paSelect.options).forEach(opt => opt.selected = false);
            
            // Seleciona os PAs do usuário
            data.pa.forEach(pa => {
                const option = Array.from(paSelect.options).find(opt => opt.value === pa);
                if (option) option.selected = true;
            });
            
            // Limpa e marca as permissões
            document.querySelectorAll('.edit-module').forEach(checkbox => {
                checkbox.checked = data.permissions.includes(checkbox.value);
                
                // Se for admin, marca e desabilita todos os módulos
                if (data.is_admin) {
                    checkbox.checked = true;
                    checkbox.disabled = true;
                } else {
                    checkbox.disabled = false;
                }
            });
            
            // Se for usuário AD, desabilita campos que não podem ser editados
            if (data.is_ad_user) {
                document.getElementById('editUsername').disabled = true;
                document.getElementById('editPassword').disabled = true;
            } else {
                document.getElementById('editUsername').disabled = false;
                document.getElementById('editPassword').disabled = false;
            }
            
            // Mostra o modal
            editModal.show();
        })
        .catch(error => {
            alert('Erro ao carregar dados do usuário');
            console.error('Erro:', error);
        });
}

function salvarEdicao() {
    const formData = new FormData();
    
    // Adiciona os campos do formulário
    formData.append('name', document.getElementById('editName').value);
    formData.append('username', document.getElementById('editUsername').value);
    formData.append('password', document.getElementById('editPassword').value);
    formData.append('is_admin', document.getElementById('editIsAdmin').checked ? 'on' : 'off');
    
    // Adiciona os PAs selecionados
    const paSelect = document.getElementById('editPa');
    Array.from(paSelect.selectedOptions).forEach(option => {
        formData.append('pa', option.value);
    });
    
    // Se for admin, inclui todos os módulos
    if (document.getElementById('editIsAdmin').checked) {
        ['dashboard', 'rpa_monitor', 'cpfs', 'uploads', 
         'robozometro', 'relatorios', 'crls', 'audit'].forEach(module => {
            formData.append('modules', module);
        });
    } else {
        // Adiciona apenas os módulos selecionados
        document.querySelectorAll('.edit-module:checked').forEach(checkbox => {
            formData.append('modules', checkbox.value);
        });
    }
    
    fetch(`/usuarios/editar/${currentUserId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Sucesso - fecha o modal e recarrega a página
            editModal.hide();
            location.reload();
        } else {
            // Erro - mostra a mensagem no modal
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = data.error || 'Erro ao salvar usuário';
            errorDiv.style.display = 'block';
        }
    })
    .catch(error => {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.textContent = 'Erro ao salvar as alterações. Por favor, tente novamente.';
        errorDiv.style.display = 'block';
        console.error('Erro:', error);
    });
}

// Limpa mensagens de erro quando o modal é fechado
document.getElementById('editarUsuarioModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('errorMessage').textContent = '';
});

function excluirUsuario(userId, username) {
    document.getElementById('excluirUsuarioNome').textContent = username;
    document.getElementById('excluirUsuarioForm').action = `/usuarios/excluir/${userId}`;
    new bootstrap.Modal(document.getElementById('excluirUsuarioModal')).show();
}

function excluirUsuarioAD(id, nome) {
    document.getElementById('nomeUsuarioADExcluir').textContent = nome;
    document.getElementById('formExcluirUsuarioAD').action = `/usuarios/excluir_ad/${id}`;
    const modal = new bootstrap.Modal(document.getElementById('modalExcluirUsuarioAD'));
    modal.show();
}
</script>
{% endblock %}
