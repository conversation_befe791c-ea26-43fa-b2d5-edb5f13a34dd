# Funcionalidade de Captura de Tela - Robozômetro

## Visão Geral
A funcionalidade de captura de tela permite aos usuários tirar um print completo da página do Robozômetro, visualizar a imagem capturada em um modal e fazer o download em formato PNG.

## Como Usar

### 1. Acessar a Funcionalidade
- Acesse a página do Robozômetro
- Localize o botão **"Capturar"** no canto superior direito da página
- O botão possui um ícone de câmera e está posicionado ao lado do botão "Modo TV"

### 2. Capturar a Tela
- Clique no botão **"Capturar"**
- Uma tela de loading aparecerá com a mensagem "Capturando tela..."
- O processo pode levar alguns segundos dependendo do tamanho da página

### 3. Visualizar e Baixar
- Após a captura, um modal será aberto mostrando a imagem capturada
- A imagem mostra toda a página do Robozômetro em alta qualidade
- Clique no botão **"Baixar PNG"** para salvar a imagem
- O arquivo será salvo com o nome: `robozometro_YYYYMMDD_HHMMSS.png`

### 4. Fechar o Modal
- Clique no **X** no canto superior direito do modal
- Clique fora da área do modal
- Pressione a tecla **ESC**

## Características Técnicas

### Qualidade da Captura
- **Resolução**: Utiliza a resolução nativa da tela (devicePixelRatio)
- **Formato**: PNG com qualidade de 95%
- **Área**: Captura toda a página, incluindo conteúdo que requer scroll
- **Fidelidade**: Mantém todas as cores, fontes e elementos visuais

### Compatibilidade
- **Navegadores**: Chrome, Firefox, Safari, Edge (versões modernas)
- **Dispositivos**: Desktop, tablet e mobile
- **Modo TV**: Funciona tanto no modo normal quanto no modo TV

### Performance
- **Tempo de Captura**: 2-5 segundos (dependendo do tamanho da página)
- **Tamanho do Arquivo**: Varia entre 500KB a 2MB
- **Memória**: Otimizado para não sobrecarregar o navegador

## Design e UX

### Posicionamento
- **Desktop**: Canto superior direito, ao lado do botão "Modo TV"
- **Mobile**: Centralizado acima dos outros botões
- **Modo TV**: Ajustado proporcionalmente para telas grandes

### Estilo Visual
- **Cores**: Gradiente verde escuro para azul escuro (padrão Sicoob)
- **Hover**: Efeito de elevação e inversão do gradiente
- **Ícone**: Câmera (Bootstrap Icons)
- **Tipografia**: Maiúscula, peso 600, espaçamento de letras

### Modal
- **Fundo**: Overlay escuro com blur
- **Conteúdo**: Fundo branco com bordas arredondadas
- **Responsivo**: Ajusta-se a diferentes tamanhos de tela
- **Acessibilidade**: Suporte a navegação por teclado

## Casos de Uso

### 1. Relatórios Executivos
- Capturar dashboards para apresentações
- Documentar métricas em momentos específicos
- Criar histórico visual de performance

### 2. Monitoramento
- Registrar estados do sistema
- Documentar problemas ou anomalias
- Compartilhar status com equipes

### 3. Auditoria
- Evidenciar dados em determinados períodos
- Criar registros para compliance
- Documentar configurações e resultados

## Limitações

### Técnicas
- Requer JavaScript habilitado
- Dependente da biblioteca html2canvas
- Pode ter limitações com alguns elementos CSS complexos

### Navegador
- Popup blockers podem interferir no download
- Alguns navegadores podem solicitar permissão para download
- Funcionalidade de fullscreen pode variar entre navegadores

## Troubleshooting

### Problemas Comuns

**Captura não funciona:**
- Verifique se JavaScript está habilitado
- Atualize a página e tente novamente
- Verifique se há bloqueadores de popup

**Download não inicia:**
- Verifique as configurações de download do navegador
- Permita downloads automáticos para o site
- Tente com outro navegador

**Qualidade baixa:**
- Verifique a resolução da tela
- Teste em um monitor com maior resolução
- Aguarde o carregamento completo da página antes de capturar

**Modal não abre:**
- Verifique se há erros no console do navegador
- Recarregue a página
- Limpe o cache do navegador

## Suporte Técnico

Para problemas técnicos ou sugestões de melhoria, entre em contato com a equipe de desenvolvimento fornecendo:
- Navegador e versão utilizada
- Sistema operacional
- Descrição detalhada do problema
- Screenshots se possível