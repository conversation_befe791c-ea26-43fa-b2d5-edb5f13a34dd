from ldap3 import Server, Connection, NTLM, ALL, SUBTREE, SIMPLE
from typing import Optional, Tuple, Dict
import logging
from models.user import User
from utils.db import get_mysql_connection
import mysql.connector
import socket

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ADAuth:
    def __init__(self):
        self.domain = "administracao.local"
        self.domain_netbios = "ADMINISTRACAO"
        
        # Tenta resolver o endereço IP do controlador de domínio
        try:
            self.dc_ip = socket.gethostbyname(self.domain)
            logger.info(f"Resolvido IP do controlador de domínio: {self.dc_ip}")
        except socket.gaierror:
            self.dc_ip = self.domain
            logger.warning(f"Não foi possível resolver o IP do domínio, usando nome: {self.domain}")
        
        self.server = Server(self.dc_ip, get_info=ALL)
        self.base_dn = "DC=administracao,DC=local"
    
    def authenticate(self, username: str, password: str) -> <PERSON><PERSON>[bool, Optional[Dict]]:
        """
        Autentica um usuário no Active Directory
        Retorna uma tupla (sucesso, dados_usuario)
        """
        try:
            # Tenta primeiro com autenticação simples
            user_dn = f"CN={username},{self.base_dn}"
            logger.info(f"Tentando autenticação com DN: {user_dn}")
            
            # Primeira tentativa: autenticação simples com DN completo
            conn = Connection(
                self.server,
                user=user_dn,
                password=password,
                authentication=SIMPLE
            )
            
            if not conn.bind():
                # Segunda tentativa: autenticação simples com UPN
                user_upn = f"{username}@{self.domain}"
                logger.info(f"Tentando autenticação com UPN: {user_upn}")
                
                conn = Connection(
                    self.server,
                    user=user_upn,
                    password=password,
                    authentication=SIMPLE
                )
                
                if not conn.bind():
                    # Terceira tentativa: autenticação com NTLM
                    ntlm_user = f"{self.domain_netbios}\\{username}"
                    logger.info(f"Tentando autenticação NTLM com: {ntlm_user}")
                    
                    conn = Connection(
                        self.server,
                        user=ntlm_user,
                        password=password,
                        authentication=NTLM
                    )
                    
                    if not conn.bind():
                        logger.warning(f"Todas as tentativas de autenticação falharam para {username}")
                        return False, None
            
            logger.info(f"Autenticação bem-sucedida para {username}, buscando informações...")
            
            # Busca informações do usuário no AD
            search_filter = f"(&(objectClass=user)(sAMAccountName={username}))"
            conn.search(
                self.base_dn,
                search_filter,
                attributes=['displayName', 'mail', 'department']
            )
            
            if not conn.entries:
                logger.warning(f"Usuário {username} não encontrado no AD após autenticação")
                return False, None
            
            user_data = {
                'username': username,
                'name': conn.entries[0].displayName.value if hasattr(conn.entries[0], 'displayName') else username,
                'email': conn.entries[0].mail.value if hasattr(conn.entries[0], 'mail') else None,
                'department': conn.entries[0].department.value if hasattr(conn.entries[0], 'department') else None
            }
            
            logger.info(f"Informações do usuário recuperadas com sucesso: {user_data}")
            return True, user_data
            
        except Exception as e:
            logger.error(f"Erro na autenticação AD: {str(e)}")
            return False, None
        finally:
            if 'conn' in locals() and conn.bound:
                conn.unbind()
    
    def ensure_ad_user_in_db(self, user_data: Dict) -> Optional[User]:
        """
        Garante que o usuário AD existe no banco de dados local
        e tem as permissões mínimas necessárias.
        Novos usuários são criados como inativos até aprovação do administrador.
        """
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor(dictionary=True)
            
            # Verifica se o usuário já existe
            cursor.execute(
                'SELECT id, is_active FROM users WHERE username = %s AND is_ad_user = 1',
                (user_data['username'],)
            )
            existing_user = cursor.fetchone()
            
            if existing_user:
                user_id = existing_user['id']
                # Se o usuário existe mas está inativo, retorna None
                if not existing_user['is_active']:
                    logger.warning(f"Usuário AD {user_data['username']} existe mas está inativo")
                    return None
                    
                # Atualiza informações do usuário
                cursor.execute('''
                    UPDATE users 
                    SET name = %s,
                        email = %s,
                        department = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                ''', (
                    user_data['name'],
                    user_data.get('email'),
                    user_data.get('department'),
                    user_id
                ))
            else:
                # Cria novo usuário como inativo
                cursor.execute('''
                    INSERT INTO users (
                        username, name, email, department,
                        is_ad_user, is_admin, first_login, is_active
                    ) VALUES (%s, %s, %s, %s, 1, 0, 0, 0)
                ''', (
                    user_data['username'],
                    user_data['name'],
                    user_data.get('email'),
                    user_data.get('department')
                ))
                user_id = cursor.lastrowid
                logger.info(f"Novo usuário AD {user_data['username']} criado como inativo")
                
                # Adiciona permissão padrão para o Robozômetro (será ativada quando o usuário for aprovado)
                cursor.execute('''
                    INSERT INTO user_permissions (user_id, module)
                    VALUES (%s, 'robozometro')
                ''', (user_id,))
            
            conn.commit()
            
            # Retorna o objeto User apenas se estiver ativo
            user = User.get_by_id(user_id)
            if user and user.is_active:
                logger.info(f"Usuário AD {user_data['username']} sincronizado com sucesso no banco local")
                return user
            return None
            
        except mysql.connector.Error as e:
            logger.error(f"Erro ao sincronizar usuário AD com banco: {str(e)}")
            if 'conn' in locals():
                conn.rollback()
            return None
        finally:
            if 'conn' in locals():
                conn.close() 