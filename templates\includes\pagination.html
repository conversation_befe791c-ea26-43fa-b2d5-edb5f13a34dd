<!-- Paginação -->
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="text-secondary">
        Mostrando {{ items|length }} de {{ total }} registros
    </div>
    <nav aria-label="Navegação de páginas">
        <ul class="pagination mb-0">
            <li class="page-item {% if page == 1 %}disabled{% endif %}">
                <a class="page-link text-dark" href="{{ url_for(request.endpoint, page=page-1, per_page=per_page, tab=tab) }}" aria-label="Anterior">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% for p in range(start_page, end_page + 1) %}
            <li class="page-item {% if p == page %}active{% endif %}">
                <a class="page-link {% if p == page %}text-white{% else %}text-dark{% endif %}" href="{{ url_for(request.endpoint, page=p, per_page=per_page, tab=tab) }}">{{ p }}</a>
            </li>
            {% endfor %}
            <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                <a class="page-link text-dark" href="{{ url_for(request.endpoint, page=page+1, per_page=per_page, tab=tab) }}" aria-label="Próximo">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    </nav>
</div> 