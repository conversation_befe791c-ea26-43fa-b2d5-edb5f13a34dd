import sys
import os

# Adiciona o diretório raiz do projeto ao PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)

# Define o caminho do diretório atual do script
current_dir = os.path.dirname(os.path.abspath(__file__))

import pandas as pd
import mysql.connector
import logging
from utils.db import get_mysql_connection
from datetime import datetime

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def formatar_cpf(cpf):
    """Formata o CPF para garantir 11 dígitos com zeros à esquerda"""
    if pd.isna(cpf):
        return None
    
    # Remove caracteres não numéricos
    cpf = ''.join(filter(str.isdigit, str(cpf)))
    
    # Garante que tenha 11 dígitos com zeros à esquerda
    return cpf.zfill(11)

def atualizar_rendas():
    try:
        # Conectar ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Ler a planilha
        planilha_path = os.path.join(current_dir, 'cpfs_rendas_2.xlsx')
        logger.info(f"Lendo planilha {planilha_path}...")
        
        # Lê a planilha especificando o tipo da coluna CPF como string
        df = pd.read_excel(planilha_path, dtype={'CPF': str})
        
        # Verificar nomes das colunas
        logger.info("Colunas encontradas na planilha: " + ", ".join(df.columns))
        
        # Verificar e ajustar nomes das colunas
        coluna_cpf = None
        coluna_renda = None
        
        # Procura pela coluna de CPF
        possiveis_cpf = ['CPF', 'cpf', 'Cpf']
        for col in possiveis_cpf:
            if col in df.columns:
                coluna_cpf = col
                break
                
        # Procura pela coluna de Renda
        possiveis_renda = ['RENDA', 'Renda', 'renda', 'RENDA BRUTA', 'Renda Bruta', 'renda_bruta']
        for col in possiveis_renda:
            if col in df.columns:
                coluna_renda = col
                break
        
        if not coluna_cpf:
            raise ValueError("Coluna de CPF não encontrada na planilha. Colunas disponíveis: " + ", ".join(df.columns))
        if not coluna_renda:
            raise ValueError("Coluna de Renda não encontrada na planilha. Colunas disponíveis: " + ", ".join(df.columns))
            
        logger.info(f"Usando coluna '{coluna_cpf}' para CPF e '{coluna_renda}' para Renda")
        
        # Formata os CPFs na coluna
        df[coluna_cpf] = df[coluna_cpf].apply(formatar_cpf)
        
        # Remove linhas com CPF inválido ou nulo
        df = df.dropna(subset=[coluna_cpf])
        
        # Remover duplicatas de CPF, mantendo a primeira ocorrência
        df = df.drop_duplicates(subset=[coluna_cpf], keep='first')
        
        # Inicializar contadores
        total_cpfs = len(df)
        cpfs_nao_encontrados = 0
        cpfs_atualizados = 0
        
        # Lista para armazenar detalhes das atualizações
        detalhes_atualizados = []
        
        # Debug: Mostrar primeiros CPFs da planilha
        logger.info("\nAmostras de CPFs da planilha:")
        for index, row in df.head().iterrows():
            logger.info(f"CPF: {row[coluna_cpf]}, Renda: {row[coluna_renda]}")
        
        # Processar cada CPF da planilha
        for _, row in df.iterrows():
            cpf = row[coluna_cpf]
            renda = row[coluna_renda]
            
            # Verificar se o CPF existe na tabela associados
            cursor.execute("SELECT cpf, renda FROM associados WHERE cpf = %s", (cpf,))
            result = cursor.fetchone()
            
            if result:
                # Atualizar a renda
                cursor.execute("UPDATE associados SET renda = %s WHERE cpf = %s", (renda, cpf))
                cpfs_atualizados += 1
                
                detalhes_atualizados.append({
                    'cpf': cpf,
                    'renda_anterior': result['renda'],
                    'renda_nova': renda
                })
                
                logger.info(f"CPF {cpf} atualizado com sucesso - Renda anterior: {result['renda']}, Nova renda: {renda}")
            else:
                cpfs_nao_encontrados += 1
                logger.info(f"CPF {cpf} não encontrado na tabela associados")
        
        # Commit das alterações
        conn.commit()
        
        # Log do resumo final
        logger.info("\nResumo do processamento:")
        logger.info(f"Total de CPFs processados: {total_cpfs}")
        logger.info(f"CPFs atualizados: {cpfs_atualizados}")
        logger.info(f"CPFs não encontrados: {cpfs_nao_encontrados}")
        
        # Log detalhado das atualizações
        if detalhes_atualizados:
            logger.info("\nDetalhes das atualizações:")
            for detalhe in detalhes_atualizados:
                logger.info(f"CPF: {detalhe['cpf']}")
                logger.info(f"   Renda anterior: {detalhe['renda_anterior']}")
                logger.info(f"   Nova renda: {detalhe['renda_nova']}")
                logger.info("---")
        
    except Exception as e:
        logger.error(f"Erro ao processar planilha: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    atualizar_rendas() 