import os
import logging
from utils.db import get_mysql_connection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def drop_old_tables():
    """
    Remove as tabelas antigas (rpa_executions e rpa_execution_history) 
    que foram substituídas pela rpa_tracking
    """
    try:
        # Lê o arquivo SQL
        migration_file = os.path.join('config', 'db_migrations', 'drop_old_tables.sql')
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_commands = f.read()

        # Conecta ao banco de dados
        conn = get_mysql_connection()
        cursor = conn.cursor()

        # Executa os comandos SQL
        for command in sql_commands.split(';'):
            if command.strip():
                cursor.execute(command)
                
        conn.commit()
        logger.info("Tabelas antigas removidas com sucesso!")

    except Exception as e:
        logger.error(f"Erro ao remover tabelas antigas: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    drop_old_tables() 